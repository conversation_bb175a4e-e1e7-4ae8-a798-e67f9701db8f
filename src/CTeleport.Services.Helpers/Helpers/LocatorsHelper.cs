using System.Collections.Generic;

namespace CTeleport.Services.Helpers
{
    public static class LocatorsHelper
    {
        /// <summary>
        /// Get Provider Code from locators
        /// </summary>
        /// <param name="locators"></param>
        /// <returns></returns>
        public static string GetProviderCode(IReadOnlyDictionary<string, string> locators)
        {
             locators.TryGetValue(LocatorNames.PROVIDER, out var provider);

             return provider;
        }

        /// <summary>
        /// Get Main Locator Record for Provider (UNIVERSAL_RECORD for Galileo, MAIN_RECORD for other providers)
        /// </summary>
        /// <param name="locators"></param>
        /// <returns></returns>
        public static string GetMainRecord(IReadOnlyDictionary<string, string> locators)
        {
            // TODO: AMADEUS + LowCost
            //if (locators.ContainsKey(LocatorNames.UNIVERSAL_RECORD))
            //{
            //    return locators[LocatorNames.UNIVERSAL_RECORD];
            //}
            //return locators[LocatorNames.MAIN_RECORD]; 

            locators.TryGetValue(LocatorNames.UNIVERSAL_RECORD, out var mainRecord);

            return mainRecord;
        }

        /// <summary>
        /// Get AirReservation locator code.
        /// </summary>
        /// <param name="locators">All available locator records.</param>
        /// <returns>AirReservation Locator code.</returns>
        public static string GetAirReservationRecord(IReadOnlyDictionary<string, string> locators)
        {
            locators.TryGetValue(LocatorNames.AIR_RESERVATION, out var airReservationRecord);

            return airReservationRecord;
        }
    }
}