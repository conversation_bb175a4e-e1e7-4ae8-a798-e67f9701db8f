namespace CTeleport.Services.Helpers.Extensions;

public static class ProviderBusinessLogicExtensions
{
    public static bool IsTicketedImmediately(this string source)
    {
        return source.IsTravelfusion() || source.IsAirgateway();
    }
    
    public static bool CanBeMarkedAsCancelled(this string source)
    {
        return source.IsTravelfusion() || source.IsAirgateway();
    }
        
    public static bool IsAutoRefundable(this string source)
    {
        return !source.IsTravelfusion();
    }
    
    public static bool ExpiresAtTicketingTime(this string source)
    {
        return source.IsAmadeus() || source.IsAirgateway();
    }
        
    public static bool IsVoidable(this string source)
    {
        return !source.IsTravelfusion();
    }
        
    public static bool IsCancelable(this string source)
    {
        return !source.IsTravelfusion();
    }
        
    public static bool CanBeRepriced(this string source)
    {
        return !source.IsTravelfusion() && !source.IsAirgateway();
    }

    public static int TicketingPriority(this string source) => source switch
    {
        _ when source.IsTravelfusion() => 50,
        _ when source.IsAirgateway() => 50,
        _ when source.IsAmadeus() => 10,
        _ when source.IsGalileo() => 10,
        _ => 100
    };
}