using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace CTeleport.Services.AirGateway;

public class AirGatewayJsonSerializer : DefaultContractResolver
{
    private static readonly string[] PropertiesToRemoveFromIgnore = { "ProviderKeys", "Net", "OriginalCcy", "FareCalc" };
        
    protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
    {
        var property = base.CreateProperty(member, memberSerialization);

        if (PropertiesToRemoveFromIgnore.Any(p => p.Equals(property.PropertyName, StringComparison.OrdinalIgnoreCase)))
        {
            property.Ignored = false;
        }
        return property;
    }
}
