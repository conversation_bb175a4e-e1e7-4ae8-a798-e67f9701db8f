using Autofac;
using Microsoft.Extensions.Configuration;
using CTeleport.Common.Extensions;
using CTeleport.Services.Travelfusion.Clients;
using CTeleport.Services.Travelfusion.Configuration;

namespace CTeleport.Services.Travelfusion
{
    public class TravelfusionModule : Autofac.Module
    {
        private readonly IConfiguration _configuration;

        public TravelfusionModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterInstance(_configuration.GetSettings<TravelfusionOptions>()).SingleInstance();
            builder.RegisterType<TravelfusionClient>().As<ITravelfusionClient>();
        }
    }
}