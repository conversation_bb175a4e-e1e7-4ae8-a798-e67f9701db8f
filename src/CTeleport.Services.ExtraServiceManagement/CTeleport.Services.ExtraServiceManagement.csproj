<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Autofac" Version="8.1.1" />
      <PackageReference Include="CTeleport.Messages" Version="1.1.3440" />
      <PackageReference Include="CTeleport.Messages.Abstractions" Version="1.1.3440" />
      <PackageReference Include="CTeleport.Services.Ancillary.Shared" Version="1.1.76" />
      <PackageReference Include="CTeleport.Services.Booking.Core" Version="1.1.174" />
      <PackageReference Include="CTeleport.Services.ExtraServiceManagement.Shared" Version="2025.4.29.1173" />
      <PackageReference Include="CTeleport.Services.Search.Core" Version="1.1.249" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
      <PackageReference Include="Polly" Version="8.5.0" />
      <PackageReference Include="RestEase" Version="1.4.5" />
      <PackageReference Include="Serilog" Version="2.9.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\CTeleport.Services.AirlineSettingsClient\CTeleport.Services.AirlineSettingsClient.csproj" />
      <ProjectReference Include="..\CTeleport.Services.PlacesClient\CTeleport.Services.PlacesApiClient.csproj" />
      <ProjectReference Include="..\CTeleport.Services.Providers\CTeleport.Services.Providers.csproj" />
    </ItemGroup>

</Project>
