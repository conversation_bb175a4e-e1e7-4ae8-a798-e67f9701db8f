using System.Collections.Generic;
using CTeleport.Services.Ancillary.Shared;

namespace CTeleport.Services.ExtraServiceManagement.Models
{
    /// <summary>
    /// Extra baggage option details received from provider.  
    /// </summary>
    public class ExtraBaggageOption
    {
        /// <summary>
        /// Key to buy option from provider.
        /// </summary>
        public string Key { get; set; }
        
        /// <summary>
        /// Provider key of solution. It is required for calculation of extra service for each option.  
        /// </summary>
        public string SolutionProviderKey { get; set; }
        
        /// <summary>
        /// Currency
        /// </summary>
        public string Ccy { get; set; }
        public string Description { get; set; }
        public BaggageType Type { get; set; }
        public decimal Price { get; set; }
        public int? Weight { get; set; }
        public string Dimensions { get; set; }
        
        /// <summary>
        /// Segment Refs that option is for 
        /// </summary>
        public List<string> SegmentRefs { get; set; }
    }
}