using Autofac;
using CTeleport.Common.Extensions;
using CTeleport.Services.ExtraServiceManagement.Clients;
using CTeleport.Services.ExtraServiceManagement.Services;
using CTeleport.Services.Helpers;
using Microsoft.Extensions.Configuration;

namespace CTeleport.Services.ExtraServiceManagement
{
    
    public class ExtraServiceManagementModule : Autofac.Module
    {
        private static string KEY = "CTeleport.Services.ExtraServiceManagement";

        private readonly IConfiguration _configuration;

        public ExtraServiceManagementModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }
    
        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterInstance(_configuration.GetSettings<Configuration.ExtraServiceManagementOptions>()).SingleInstance();

            builder.Register((ctx, p) => ctx.Resolve<IApiClientProxyFactory>()
                    .CreateApiClient<IExtraServiceManagementApi>(ctx.Resolve<Configuration.ExtraServiceManagementOptions>().Url))
                .As<IExtraServiceManagementApi>();
            builder.RegisterType<ExtraServiceManagementClient>().As<IExtraServiceManagementClient>();
            
            builder.RegisterType<AncillaryService>().As<IAncillaryService>();
        }

    }
}