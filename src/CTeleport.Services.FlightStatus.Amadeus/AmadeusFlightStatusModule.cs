using Autofac;
using CTeleport.Services.Booking.Core.Interfaces;
using Microsoft.Extensions.Configuration;

namespace CTeleport.Services.FlightStatus.Amadeus
{
    public class AmadeusFlightStatusModule : Module
    {
        private readonly IConfiguration _configuration;

        public AmadeusFlightStatusModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        private const string ProviderKey = "1A";

        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterType<AmadeusTicketStateService>().Keyed<IProviderTicketStateService>(ProviderKey);
        }
    }
}
