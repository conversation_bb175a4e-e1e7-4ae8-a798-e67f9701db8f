using System.Collections.Generic;
using CTeleport.Services.Booking.Models;

namespace CTeleport.Services.Booking.Dto
{
    public class BaseReservationDto
    {
        /// <summary>
        /// Reservation id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// Identifier of PNR source, the combination of Provider and PCC (OID) codes
        /// <example>"1G.8WG0"</example>
        /// </summary>
        public string Source { get; set; }

        /// <summary>Funding source</summary>
        public string FundingSource { get; set; }

        /// <summary>
        /// Platting carrier
        /// </summary>
        public string PlatingCarrier { get; set; }

        /// <summary>
        /// Provider locator (Galileo GDS)
        /// </summary>
        public string Locator { get; set; }

        /// <summary>
        /// Reservation state
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Irregularities registered for reservation, if any
        /// </summary>
        public ReservationIrregularities Irregularities { get; set; }        

        /// <summary>
        /// Collection of tickets in this reservation
        /// </summary>
        public IList<TicketDto> Tickets { get; set; }

        /// <summary>
        /// If true, checks for class drop will be run. 
        /// </summary>
        public bool ClassDropIsAllowed { get; set; }

        /// <summary>
        /// Indicates whether the Reservation can be cancelled
        /// </summary>
        public bool CanCancel { get; set; }

        /// <summary>
        /// Indicates whether the Reservation has a ticket associated or not. (If Ticketless==true, no ticket number is expected)
        /// </summary>
        public bool Ticketless { get; set; }

        /// <summary>
        /// Indicates whether the Reservation is virtual
        /// </summary>
        public bool IsVirtual { get; set; }

        /// <summary>
        /// Invoices numbers this reservation appears in
        /// </summary>
        public ICollection<string> InvoiceNumbers { get; set; }

        /// <summary>
        /// Credit note numbers this reservation appears in
        /// </summary>
        public ICollection<string> CreditNoteNumbers { get; set; }

        /// <summary>
        /// Collection of flight segments grouped by leg
        /// </summary>
        public List<List<SegmentDto>> LegSegments { get; set; }
    }
}