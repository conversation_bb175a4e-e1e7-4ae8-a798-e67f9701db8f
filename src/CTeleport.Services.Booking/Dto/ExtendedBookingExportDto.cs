using AutoMapper.Configuration.Annotations;
using CTeleport.Services.Excel.Helpers;

namespace CTeleport.Services.Booking.Dto;

public class ExtendedBookingExportDto : BaseBookingExportDto
{
    [ExportColumn("Booking Type")]
    [MappingOrder(5)]
    public string BookingType { get; set; }
    
    [ExportColumn("Airline Name")]
    [MappingOrder(82)]
    public string AirlineName { get; set; }
    
    [ExportColumn("Origin airport")]
    [MappingOrder(141)]
    public string OriginAirport { get; set; }
    
    [ExportColumn("Destination airport")]
    [MappingOrder(142)]
    public string DestinationAirport { get; set; }
    
    [ExportColumn("Origin country")]
    [MappingOrder(143)]
    public string OriginCountry { get; set; }
    
    [ExportColumn("Destination country")]
    [MappingOrder(144)]
    public string DestinationCountry { get; set; }
    
    [ExportColumn("Origin city")]
    [MappingOrder(145)]
    public string OriginCity { get; set; }
    
    [ExportColumn("Destination city")]
    [MappingOrder(146)]
    public string DestinationCity { get; set; }
    
    //[ExportColumn("Transaction fee")]
    //[MappingOrder(89)]
    //public decimal TransactionFee { get; set; }
    
    [ExportColumn("IATA used")]
    [MappingOrder(91)]
    public string IATAUsed { get; set; }
    
    [ExportColumn("Office Id")]
    [MappingOrder(92)]
    public string OfficeId { get; set; }
}