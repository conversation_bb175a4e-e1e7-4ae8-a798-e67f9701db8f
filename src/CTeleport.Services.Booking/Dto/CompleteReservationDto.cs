using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Models;
using System;
using System.Collections.Generic;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;

namespace CTeleport.Services.Booking.Dto
{
    public class CompleteReservationDto : BriefReservationDto
    {
        /// <summary>
        /// Reservation locators
        /// </summary>
        public Models.Locators Locators { get; set; }

        /// <summary>
        /// Fare specifications
        /// </summary>
        public FareDto Fare { get; set; }

        /// <summary>
        /// Reservation price specifications
        /// </summary>
        public ReservationPrice Price { get; set; }

        /// <summary>
        /// Reservation refund specifications
        /// </summary>
        public ReservationRefund Refund { get; set; }

        /// <summary>
        /// Baggage allowance
        /// </summary>
        [Obsolete]
        public Search.Shared.Models.BaggageAllowance Baggage { get; set; }

        /// <summary>
        /// Baggage allowance details dictionary with O-D as key
        /// </summary>
        public IDictionary<string, Search.Shared.Models.BaggageAllowance> BaggageAllowances { get; set; }

        /// <summary>
        /// Fare rules dictionary with O-D pair as a key
        /// </summary>
        public Dictionary<string, List<Search.Shared.Models.FareRuleSection>> FareRules { get; set; }

        /// <summary>
        /// Collection of flight segments grouped by leg
        /// </summary>
        public List<List<SegmentDto>> LegSegments { get; set; }

        /// <summary>
        /// Collection of tickets in this reservation with complete details
        /// </summary>
        new public IList<CompleteTicketDto> Tickets { get; set; }

        /// <summary>
        /// MD5 hash for the text from 16 category
        /// </summary>
        public List<string> FareRuleCat16Ids { get; set; }

        /// <summary>
        /// Full cancellation timeline.
        /// </summary>
        public List<ConditionsTimespan> CancellationTimeline { get; set; }

        /// <summary>
        /// Full change timeline.
        /// </summary>
        public List<ConditionsTimespan> ChangeTimeline { get; set; }
        
        /// <summary>
        /// Full changes timeline for partially used ticket.
        /// Can be null if the ticket has no promised conditions for changing partially used ticket.
        /// </summary>
        public List<ConditionsTimespan>? PartiallyUsedChangeTimeline { get; set; }

        /// <summary>
        /// Indicates whether the reservation is virtual.
        /// </summary>
        public bool IsVirtual { get; set; }

        /// <summary>
        /// Custom and calculated fields
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; }

        /// <summary>
        /// Reservation Passenger.
        /// </summary>
        public ReservationPassenger Passenger { get; set; }

        /// <summary>
        /// Indicates whether the reservation requires approval
        /// </summary>
        public bool ApprovalRequired { get; set; }

        /// <summary>
        /// Reservation's tickets changes
        /// </summary>
        public IList<TicketChanges> Changes { get; set; }

        /// <summary>
        /// Search job metadata
        /// </summary>
        public SearchJobMetadata SearchJobMetadata { get; set; }

        /// <summary>
        /// Funding source
        /// </summary>
        public string FundingSource { get; set; }
    }
}