using CTeleport.Services.Booking.Models;
using SmartFormat.Core.Settings;

namespace CTeleport.Services.Booking.Dto;

public class SingleNameSettingResponse
{
    public SingleNameSetting SingleNameSetting { get; private init; }
    
    public bool HasError { get; private init; }
    
    public string Error { get; private init; }

    public static SingleNameSettingResponse WithError(string error) => new()
    {
        Error = error,
        HasError = true
    };

    public static SingleNameSettingResponse Success(SingleNameSetting setting) => new()
    {
        HasError = false,
        SingleNameSetting = setting
    };
}