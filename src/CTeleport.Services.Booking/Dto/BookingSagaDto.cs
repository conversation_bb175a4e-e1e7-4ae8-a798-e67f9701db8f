using System.Collections.Generic;
using CTeleport.Services.Ancillary.Shared;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.Booking.Dto
{
    public class BookingSagaDto : BaseBookingDto
    {
        /// <summary>
        /// Flight solution id this booking was booked with
        /// </summary>
        public string FlightSolutionId { get; set; }

        /// <summary>
        /// Flight solution route id
        /// </summary>
        public string RouteId { get; set; }

        /// <summary>
        /// Flight solution search id
        /// </summary>
        public string SearchId { get; set; }

        /// <summary>
        /// Original booking id
        /// </summary>
        public string OriginalBookingId { get; set; }
        
        
        /// <summary>
        /// Invoicee id selected for this booking
        /// </summary>
        public int InvoiceeId { get; set; }
        
        /// <summary>
        /// Payment method
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; }
        
        /// <summary>
        /// Payment method id
        /// </summary>
        public string PaymentMethodId { get; set; }

        /// <summary>
        /// Payment reference id
        /// </summary>
        public string PaymentId { get; set; }
        

        public PassengerDetails Passenger { get; set; }

        /// <summary>
        /// Booking flight leg Segments
        /// </summary>
        public IList<IList<FlightSegment>> LegSegments { get; set; }

        /// <summary>
        /// Collection of reservations, pairs of travelport key and reservation id
        /// </summary>
        public ICollection<BookingReservation> Reservations { get; set; } = new List<BookingReservation>();
        
        /// <summary>
        /// Site or location of traveler final destination (oil rig, mining site, etc.)
        /// </summary>
        public SiteDto Site { get; set; }

        public class Ancillary
        {
            /// <summary>
            /// Key to buy option from provider.
            /// </summary>
            public string Key { get; set; }
        
            /// <summary>
            /// Provider key of solution. It is required for calculation of extra service for each option.  
            /// </summary>
            public string SolutionProviderKey { get; set; }
        
            /// <summary>
            /// Currency
            /// </summary>
            public string Ccy { get; set; }
            public string Description { get; set; }
            public BaggageType Type { get; set; }
            public decimal Price { get; set; }
            public int? Weight { get; set; }
        }
        
        public class BookingReservation
        {
            public string ProviderKey { get; set; }

            public string ReservationId { get; set; }

            public string OriginalReservationId { get; set; }

            public ReservationState State { get; set; }

            public bool IsVirtual { get; set; }

            public List<Ancillary> SelectedAncillaries { get; set; }
        }
        
        public class PassengerDetails
        {
            /// <summary>
            /// Passenger id (id of a passenger in 3-rd party crewing software)
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// Passenger user id
            /// </summary>
            public string UserId { get; set; }

            /// <summary>
            /// Passenger last name
            /// </summary>
            public string LastName { get; set; }

            /// <summary>
            /// Passenger first name
            /// </summary>
            public string FirstName { get; set; }

            /// <summary>
            /// Passenger Email.
            /// </summary>
            public string Email { get; set; }
            
            /// <summary>
            /// Passenger Phone Number.
            /// </summary>
            public string Phone { get; set; }
        }

        public class SiteDto
        {
            /// <summary>
            /// Site Id
            /// </summary>
            public string Id { get; set; }
        
            /// <summary>
            /// Site name
            /// </summary>
            public string Name { get; set; }
        
            /// <summary>
            /// Site type
            /// </summary>
            public string Type { get; set; }
        
            /// <summary>
            /// When <see cref="Type"/> = 'site', this will contain 'Location' field
            /// </summary>
            public IDictionary<string, string> CustomFields { get; set; }
        }

    }
}