using System;

namespace CTeleport.Services.Booking.Dto
{
    public class ReservationUpdateDto
    {
        public string Id { get; set; }
        public string BookingId { get; set; }
        public DateTime UpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool? ApprovalRequired { get; set; }
        public bool? IsVirtual { get; set; }
        public string State { get; set; }
        public decimal Markup { get; set; }
    }
}