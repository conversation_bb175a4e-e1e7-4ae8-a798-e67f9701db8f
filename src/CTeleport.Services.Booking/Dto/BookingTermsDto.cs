using System;
using System.Collections.Generic;

namespace CTeleport.Services.Booking.Dto
{
    public class BookingTermsDto
    {
        /// <summary>
        /// Fare type
        /// </summary>
        public string FareType { get; set; }

        /// <summary>
        /// Refund conditions
        /// </summary>
        public string Cancellations { get; set; }

        /// <summary>
        /// Cancellation fee in case of paid refund and in case of known,
        /// 0 if fully refundable, null otherwise.
        /// </summary>
        public decimal? CancellationFee { get; set; }

        /// <summary>
        /// Deadline for refund in case of RefundableWithDeadline or PaidRefundWithDeadline
        /// and in case the deadline is known, null for all other cases
        /// </summary>
        public DateTime? CancellationDeadline { get; set; }

        /// <summary>
        /// Changes conditions
        /// </summary>
        public string Changes { get; set; }

        /// <summary>
        /// Deadline for change in case of FreeChangeWithDeadline or PaidChangeWithDeadline
        /// and in case the deadline is known, null for all other cases
        /// </summary>
        public DateTime? ChangeDeadline { get; set; }

        /// <summary>
        /// Change fee in case of paid change and in case of known,
        /// 0 if changes are free, null otherwise.
        /// </summary>
        public decimal? ChangeFee { get; set; }

        /// <summary>
        /// Indicator is booking contains split reservations (tickets)
        /// </summary>
        public bool Splitting { get; set; }

        /// <summary>
        /// Terms components with O-D pair as a key
        /// This property is defined for split tickets only
        /// </summary>
        public Dictionary<string, BookingComponentTermsDto> Components { get; set; }

        /// <summary>
        /// Indicates whether the Booking can be cancelled
        /// </summary>
        public bool CanCancel { get; set; }
    }

    public class BookingComponentTermsDto
    {
        /// <summary>
        /// Fare type
        /// </summary>
        public string FareType { get; set; }

        /// <summary>
        /// Refund conditions
        /// </summary>
        public string Cancellations { get; set; }

        /// <summary>
        /// Changes conditions
        /// </summary>
        public string Changes { get; set; }

        /// <summary>
        /// Consideration time corresponds to the UTC time until which the booking cancellation is free.
        /// Eg: 0 - No Free Cancellation; 1573116732: Time in UTC until when the cancellation is free;
        /// </summary>
        public int ConsiderationTime { get; set; }
    }
}