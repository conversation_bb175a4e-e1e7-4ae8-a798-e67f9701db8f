using System.Threading;
using System.Threading.Tasks;
using Autofac;
using CTeleport.Common.Messaging.Extensions;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.TradeCredit;
using CTeleport.Messages.Events.ApprovalFlow;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Cards;
using CTeleport.Messages.Events.Changes;
using CTeleport.Messages.Events.CreditCardPayments;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Aggregate.Messages.Commands;
using CTeleport.Services.Booking.Commands;
using RawRabbit;

namespace CTeleport.Services.Booking.Subscriptions
{
    internal static class BookingsSubscriptions
    {
        public static async Task Subscribe(ILifetimeScope scope, string queue, ushort basePrefetch, CancellationToken ct)
        {
            var bus = scope.Resolve<IBusClient>();
            await Task.WhenAll(
                bus.SubscribeToCommand<CreateBooking>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RejectBookingSaga>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToCommand<CompleteBookingSaga>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<HoldCardFundsCompleted>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<CancelPaymentForBooking>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToCommand<CancelPaymentForBookingSaga>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToCommand<ResetTicketQueue>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetBookingState>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ResetBookingState>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ResetBookingsState>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetBookingAsNoShow>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetBookingComment>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<UpdatePassengerDetails>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ResetRefundPending>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RunBaggageReservationsMigration>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RunInvoicesMigration>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<BookingUpdatedAtMigration>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<UpdateVessel>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<CalcUninvoicedReservations>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<HistoricalBookingsUpdate>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<StartCheckingBookingsCancellationFeeDeadline>(scope, ct, queue, basePrefetch),
                bus.SubscribeToMessage<CheckCreditCommand>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RunFundingSourceMigration>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RunTicketFundingSourceMigration>(scope, ct, queue, basePrefetch),

                // ApprovalFlow
                bus.SubscribeToEvent<BookingApprovalGranted>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<ApprovalRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                
                bus.SubscribeToEvent<PNRPlacedOnQueue>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<BookingApproved>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<BookingApprovalRejected>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<BookingPaymentConfirmed>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<BookingPaymentRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<ChargeCardRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<BookingCreated>(scope, ct, queue, basePrefetch), // TODO: introduce strong retry logic
                bus.SubscribeToEvent<BookingConfirmed>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<BookingCancelled>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<CreateBookingRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<BookingRepricingFailed>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ReservationSyncCompleted>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<FlightTimeChanged>(scope, ct, queue, basePrefetch)
            );
        }
    }
}