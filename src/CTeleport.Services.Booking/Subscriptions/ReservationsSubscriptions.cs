using System.Threading;
using System.Threading.Tasks;
using Autofac;
using CTeleport.Common.Messaging.Extensions;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Messages.Events.Reservations.Emd;
using CTeleport.Services.Booking.Commands;
using RawRabbit;

namespace CTeleport.Services.Booking.Subscriptions
{
    internal static class ReservationsSubscriptions
    {
        public static async Task Subscribe(ILifetimeScope scope, string queue, ushort basePrefetch, CancellationToken ct)
        {
            var bus = scope.Resolve<IBusClient>();
            await Task.WhenAll(
                bus.SubscribeToCommand<CreateReservation>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RetrieveSupplierLocators>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ResetFareRefreshQueue>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RefreshReservationFare>(scope, ct, queue, 1),
                bus.SubscribeToCommand<SyncReservation>(scope, ct, queue, 1),
                bus.SubscribeToCommand<ResetTicketingTime>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<CheckReservationIfCheapestFareSet>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ConfirmChangedSegments>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RegisterCancelledSegments>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RegisterRescheduledSegments>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<RestoreReservation>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ProviderRestoreReservation>(scope, ct, queue, basePrefetch), //TODO: CT-2306/CT-2307 Move to Travelport
                bus.SubscribeToCommand<CheckPendingReservations>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<AddRetentionLine>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<ReservationUpdatedAtMigration>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<IssueEmd>(scope, ct, queue, basePrefetch),
                bus.SubscribeToCommand<SetReservationAsCancelled>(scope, ct, queue, basePrefetch),

                bus.SubscribeToCommand<UpdateFrequentFlyerNumber>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ProviderUpdateFrequentFlyerNumberRejected>(scope, ct, queue, basePrefetch),

                bus.SubscribeToEventKeyed<ReservationCreated>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<CreateReservationRejected>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                
                bus.SubscribeToEvent<ReservationFareUpdateSkipped>(scope, ct, queue, basePrefetch), // TODO!!!: set strong retry policy!
                bus.SubscribeToEvent<ReservationFareUpdateFailed>(scope, ct, queue, basePrefetch), // TODO!!!: set strong retry policy!
                bus.SubscribeToEvent<ReservationFareUpdated>(scope, ct, queue, basePrefetch),

                bus.SubscribeToEvent<ProviderCreateReservationCompleted>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<ProviderCreateReservationRejected>(scope, ct, queue, basePrefetch),

                bus.SubscribeToEvent<RestoreReservationCompleted>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<RestoreReservationRejected>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ProviderRestoreReservationCompleted>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ProviderRestoreReservationRejected>(scope, ct, queue, basePrefetch),
                
                bus.SubscribeToEvent<Services.ReservationBroken>(scope, ct, queue, basePrefetch, retryOptions: RetryOptions.Default),
                bus.SubscribeToEvent<ProviderCreateReservationPending>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ReservationPendingCompleted>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ReservationPendingRejected>(scope, ct, queue, basePrefetch),

                bus.SubscribeToEvent<ProviderAddRetentionLineCompleted>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ProviderAddRetentionLineRejected>(scope, ct, queue, basePrefetch),

                bus.SubscribeToEvent<ProviderIssueEmdRejected>(scope, ct, queue, basePrefetch),
                bus.SubscribeToEvent<ProviderIssueEmdCompleted>(scope, ct, queue, basePrefetch)
            );
        }
    }
}