using System.Collections.Generic;

namespace CTeleport.Services.Booking.Configuration
{
    /// <summary>
    /// Ticketing configuration options
    /// </summary>
    public class TicketingOptions
    {
        /// <summary>
        /// Max number of hours before departure
        /// </summary>
        public int BeforeDepartureTTL { get; set; }   
        
        /// <summary>
        /// Number of hour to determine immediate ticket
        /// </summary>
        public int ImmediateTicketingTTL { get; set; }

        /// <summary>
        /// Custom ticket time limit per plating carrier with carrier code as a key and number of hours as value
        /// </summary>
        public IDictionary<string, int> TTL { get; set; }

        /// <summary>
        /// Number of minutes to retry ticketing after failure
        /// </summary>
        public int RetryIntervalAfterFailure { get; set; }

        /// <summary>
        /// List of carriers (Marketing and Operating) for which tickets are issued at booking time
        /// </summary>
        /// <value></value>
        public IEnumerable<string> InstantTicketCarriers { get; set; }

        /// <summary>
        /// List of carriers (Marketing and Operating) per provider for which tickets are issued at booking time
        /// </summary>
        /// <value></value>
        public IDictionary<string, IReadOnlyCollection<string>> InstantTicketCarriersPerProvider { get; set; }

        /// <summary>
        /// Option for checking fare mask before ticketing
        /// </summary>
        public FareMaskValidity FareMaskValidity { get; set; }
    }
}