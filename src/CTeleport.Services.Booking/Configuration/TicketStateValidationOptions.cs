namespace CTeleport.Services.Booking.Configuration
{
    /// <summary>
    /// Configuration of the period to check ticket (coupon) usage state
    /// </summary>
    public class TicketStateValidationOptions
    {
        /// <summary>
        /// Number of hours before departure
        /// </summary>
        public int CooldownBeginsHoursBeforeDeparture { get; set; }

        /// <summary>
        /// Number of hours after departure
        /// </summary>
        public int CooldownEndHoursAfterDeparture { get; set; }
    }
}