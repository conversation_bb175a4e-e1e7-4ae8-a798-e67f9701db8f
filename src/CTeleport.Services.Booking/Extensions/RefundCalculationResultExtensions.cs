using CTeleport.Services.Booking.Models;
using CTeleport.Services.Price.Shared.Models;

namespace CTeleport.Services.Booking.Extensions;

public static class RefundCalculationResultExtensions
{
    /// <summary>
    /// Converts RefundCalculationResult to ReservationRefund
    /// </summary>
    public static ReservationRefund ConvertToReservationRefund(this RefundCalculationResult result)
    {
        return new ReservationRefund
        {
            Currency = result.TargetCurrency,
            Net = result.NetRefundAmount,
            CurrencyMargin = result.TargetCurrencyMargin,
            TargetCancellationFee = result.TargetCancellationFee,
            Total = result.TotalRefundAmount
        };
    }
}