using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace CTeleport.Services.Booking.Enums
{
    /// <summary>
    /// Payment methods
    /// TODO: consider to merge with CTeleport.Services.Billing.Domain.PaymentMethod and CTeleport.Messages.Commands.Enums.PaymentMethodType
    /// and move to CTeleport.Common (?)
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum PaymentMethod
    {
        /// <summary>
        /// Customer receives an invoice and pays it by regular bank transfer
        /// </summary>
        [EnumMember(Value = "bank-transfer")]
        BankTransfer,
        /// <summary>
        /// Customer pays using AMEX BTA. Invoices created using this method should be counted in indebtedness logic
        /// </summary>
        [EnumMember(Value = "amex-bta")]
        AmexBta,
        /// <summary>
        /// Customer pays using Credit Card
        /// </summary>
        [EnumMember(Value = "credit-card")]
        CreditCard
    }
}