using System.Runtime.Serialization;

namespace CTeleport.Services.Booking.Enums
{
    /// <summary>
    /// Booking state, generic state representing merged air reservation(s) and ticket(s) states
    /// </summary>
    public enum BookingState
    {
        /// Active booking, reservation(s) are scheduled for auto-ticketing
        [EnumMember(Value = "confirmed")]
        Confirmed,

        /// Cancelled booking
        [EnumMember(Value = "cancelled")]
        Cancelled,

        /// One or more tickets are issued
        [EnumMember(Value = "issued")]
        Issued,

        /// One of more tickets are used (wholly of partly)
        [EnumMember(Value = "used")]
        Used,

        /// Refund is pending
        [EnumMember(Value = "refund_pending")]
        RefundPending,

        /// Changes are pending
        [EnumMember(Value = "change_pending")]
        ChangePending,

        /// Booking requires approval by travel policies
        [EnumMember(Value = "approval_required")]
        ApprovalRequired,

        /// Booking is declined by travel policies
        [EnumMember(Value = "declined")]
        Declined,

        /// Booking requires confirmed payment
        [EnumMember(Value = "payment_required")]
        PaymentRequired
    }
}