using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.Booking.Helpers
{
    public class SegmentExtractor
    {
        public static IList<IList<FlightSegment>> GetSegmentsByProviderKey(FlightSolution flightSolution, string providerKey)
        {
            if (flightSolution.Terms.Splitting)
            {
                var splitOD = flightSolution
                    .SplitProviderODs
                    ?.Where(od => od.ProviderKey == providerKey)
                    .Select(od => od.OriginDestination)
                    .First();

                return flightSolution
                    .LegSegments
                    .Select(s => s.Where(s1 => s1.SplitOD == splitOD).ToArray())
                    .Where(s => s.Any())
                    .ToArray();
            }
            return flightSolution.LegSegments;
        }
    }
}