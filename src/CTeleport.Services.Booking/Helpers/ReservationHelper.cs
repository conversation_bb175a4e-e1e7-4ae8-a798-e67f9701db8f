using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using ServiceStack;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.FareRules;
using CTeleport.Messages.Commands.Models;
using Segment = CTeleport.Services.Booking.Shared.Models.Segment;

namespace CTeleport.Services.Booking.Helpers
{
    public class ReservationHelper
    {
        public const int MAX_SCHEDULE_SHIFT_MINUTES = 30;
        public const int DEPARTURE_DATE_WITHIN_DAYS = 2;

        public const string NO_CHANGES_TEXT = "No changes";

        public static CanConfirmSegmentsCheck CanConfirmSegments(Reservation original, List<Segment> segmentsNew, List<string> segmentStatuses)
        {
            var check = new CanConfirmSegmentsCheck
            {
                Changes = new List<FlightChange>()
            };

            if (original.DepartureAt < DateTime.UtcNow)
            {
                check.AlreadyDeparted = true;
                return check;
            }

            if (!segmentStatuses.All(s => s == "TK" || s == "HK"))
            {
                check.HasUnconfirmedSegments = true;
                return check;
            }
            
            check.Changes = GetFlightChanges(original, segmentsNew);
            return check;
        }

        public static string BuildSegmentsDiffString(Reservation original, List<Segment> segmentsNew)
        {
            var builder = new StringBuilder();

            var flightChanges = GetFlightChanges(original, segmentsNew);

            foreach (var flightChange in flightChanges.Where(x=> x.IsFlightTimeChanged))
            {
                var departure = FormatTime(flightChange.DepartureDate, flightChange.Departure, flightChange.DepartureShiftMinutes);
                var arrival = FormatTime(flightChange.ArrivalDate, flightChange.Arrival, flightChange.ArrivalShiftMinutes);

                builder.AppendLine($"{flightChange.Carrier}{flightChange.FlightNumber} {departure} - {arrival}");
            }

            var changes = builder.ToString();
            if (string.IsNullOrWhiteSpace(changes))
            {
                return NO_CHANGES_TEXT;
            }

            return changes;
        }

        public static List<FlightChange> GetFlightChanges(Reservation original, List<Segment> newSegments)
        {
            var changes = new List<FlightChange>();
            
            var segmentsOriginal = original.LegSegments.SelectMany(l => l).OrderBy(x=>x.DepartureTimestampUtc).ToList();
            
            newSegments = newSegments.OrderBy(x => x.DepartureTimestampUtc).ToList();
            
            for (var index = 0; index < segmentsOriginal.Count; index++)
            {
                var newSegment = newSegments[index];
                var originalSegment = segmentsOriginal[index];

                if (newSegment.DepartureTimestampUtc == 0 || originalSegment.DepartureTimestampUtc == 0 
                                                          || newSegment.ArrivalTimestampUtc == 0 
                                                          || originalSegment.ArrivalTimestampUtc == 0)
                {
                    continue;
                }
                
                var departureShiftInMinutes = (int)TimeSpan.FromSeconds(newSegment.DepartureTimestampUtc - originalSegment.DepartureTimestampUtc).TotalMinutes;
                var arrivalShiftInMinutes = (int)TimeSpan.FromSeconds(newSegment.ArrivalTimestampUtc - originalSegment.ArrivalTimestampUtc).TotalMinutes;

                var isFlightTimeChanged = departureShiftInMinutes != (int)decimal.Zero || arrivalShiftInMinutes != (int)decimal.Zero;
                
                var isFlightNumberChanged = !newSegment.FlightNumber.Equals(originalSegment.FlightNumber)
                                            || !newSegment.Carrier.Equals(originalSegment.Carrier);
                
                if (isFlightTimeChanged || isFlightNumberChanged)
                {
                    var connectedSegment = index > 0;
                    var invalidDeparture = connectedSegment
                        ? departureShiftInMinutes < -MAX_SCHEDULE_SHIFT_MINUTES
                        : Math.Abs(departureShiftInMinutes) > MAX_SCHEDULE_SHIFT_MINUTES;

                    var isFlightTimeShiftedTooFar = invalidDeparture || arrivalShiftInMinutes > MAX_SCHEDULE_SHIFT_MINUTES;
                    
                    changes.Add(new FlightChange
                    {
                        IsFlightTimeChanged = isFlightTimeChanged,
                        FlightNumberChanged = isFlightNumberChanged,
                        FlightTimeShiftedTooFar = isFlightTimeShiftedTooFar,
                        Carrier = newSegment.Carrier,
                        FlightNumber = newSegment.FlightNumber,
                        Departure = newSegment.DepartureTime,
                        DepartureDate = newSegment.DepartureDate,
                        DepartureShiftMinutes = departureShiftInMinutes,
                        Arrival = newSegment.ArrivalTime,
                        ArrivalDate = newSegment.ArrivalDate,
                        PreviousDepartureDate = originalSegment.DepartureDate,
                        PreviousDepartureTime = originalSegment.DepartureTime,
                        ArrivalShiftMinutes = arrivalShiftInMinutes,
                        IsDepartureDateTooFar = newSegment.DepartureTimestampUtc.ToDateTimeUtc() > DateTime.UtcNow.AddDays(DEPARTURE_DATE_WITHIN_DAYS)
                    });
                }
            }

            return changes;
        }

        public static List<string> GetPenaltyFareRulesTexts(Dictionary<string, List<FareRuleSection>> fareRules)
        {
            return fareRules != null ? fareRules.Aggregate(new List<string>(), (acc, rule) =>
            {
                var text = rule.Value.FirstOrDefault(r => r.Category == CTeleport.Services.Helpers.FareRules.PenaltiesSection)?.Text;
                if (string.IsNullOrWhiteSpace(text))
                {
                    return acc;
                }

                acc.Add(FareRulesHelper.CleanupMarkup(text));

                return acc;
            }) : new List<string>();
        }

        public static List<string> GetPenaltyFareRulesIds(Dictionary<string, List<FareRuleSection>> fareRules)
        {
            return GetPenaltyFareRulesTexts(fareRules).Map(FareRulesHelper.GetFareRuleIdByText);
        }

        public static bool? MergeRefunds(IEnumerable<bool?> refunds)
        {
            if (refunds.Any(r => r == null))
            {
                return null;
            }

            if (refunds.Any(r => r == true))
            {
                return true;
            }

            if (refunds.All(r => r == false))
            {
                return false;
            }

            return null;
        }

        public static bool? IsUsed(IEnumerable<bool?> usages)
        {
            if (usages.Any(u => u == true))
            {
                return true;
            }

            if (usages.All(u => u == false))
            {
                return false;
            }

            return null;
        }

        public static bool? IsFullyUsed(IEnumerable<bool?> usages)
        {
            if (usages.All(u => u == true))
            {
                return true;
            }

            if (usages.Any(u => u == false))
            {
                return false;
            }

            return null;
        }

        public static IEnumerable<List<T>> SplitList<T>(List<T> list, int size)
        {
            for (int i = 0; i < list.Count; i += size)
            {
                yield return list.GetRange(i, Math.Min(size, list.Count - i));
            }
        }

        private static string FormatTime(string date, string time, int diff)
        {
            return date.FormatTo("ddMMM").ToUpperInvariant() + time.Replace(":", "") + DiffToString(diff);
        }

        private static string DiffToString(int minutes)
        {
            if (minutes == 0)
            {
                return string.Empty;
            }

            return minutes > 0
                ? $"(+{minutes})"
                : $"({minutes})";
        }
        
        public static Dictionary<string, List<string>> ProcessFareRuleSections(Dictionary<string, List<FareRuleSection>> fareRules)
        {
            // method is sync flow as needed to persist the order of fare rule sections 
            
            if (fareRules == null)
                return null;
            
            var result = new Dictionary<string, List<string>>();
            
            foreach (var kvp in fareRules)
            {
                var sectionIds = kvp.Value
                    .Select(FareRulesHelper.GetFareRuleIdBySection)
                    .ToList();
                
                result[kvp.Key] = sectionIds;
            }
            
            return result;
        }
    }
}
