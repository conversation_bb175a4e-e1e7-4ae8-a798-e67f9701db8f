using CTeleport.Services.Booking.Models;
using System;
using System.Collections.Generic;
using System.Text;

namespace CTeleport.Services.Booking.Helpers
{
    public static class PassengerHelper
    {
        public static bool IsSingleNameOnly(PassengerDetails passenger)
        {
            return string.IsNullOrWhiteSpace(passenger.LastName) || string.IsNullOrWhiteSpace(passenger.FirstName);
        }
    }
}
