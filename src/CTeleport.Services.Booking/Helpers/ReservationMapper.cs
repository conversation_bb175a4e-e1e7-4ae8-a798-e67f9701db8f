using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Price.Shared.Models;
using CTeleport.Services.Search.Shared.Models;
using ServiceStack.Text;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Helpers
{
    public static class ReservationMapper
    {
        public static Reservation Map(Reservation original, ProviderFlightReservation providerReservation, BookingSaga saga, ILogger logger)
        {
            original.Passenger = providerReservation.Passenger;

            if(original.Source != providerReservation.Source)
            {
                logger.ForContext("ReservationId", original.Id)
                    .Warning("Reservation Mapper: Source was changed from {OriginalSource} to {NewSource}", original.Source, providerReservation.Source);
                
                original.Source = providerReservation.Source;
            }
            
            original.Locators = providerReservation.Locators;
            original.SupplierLocators = providerReservation.SupplierLocators;

            if (providerReservation.Baggage is { Count: > 0 })
            {
                original.BaggageAllowances = providerReservation.Baggage;
            }

            original.DepartureAt = providerReservation.Segments.Select(s => s.DepartureTimestampUtc).Min().FromUnixTime();

            original.CanCancel = providerReservation.CanCancel;
            original.Ticketless = providerReservation.Ticketless;
            
            // Set original.LegSegments with providers segments from the reservation if those were correctly mapped, otherwise
            // keep original segments.
            var mappedSegments = MapSegments(original.LegSegments, providerReservation.Segments, saga, out var warnings);
            if (mappedSegments.SelectMany(leg => leg).Any())
            {
                original.LegSegments = mappedSegments;
                if (warnings.Count > 0)
                {
                    logger.ForContext("ReservationId", original.Id)
                        .ForContext("Warnings", warnings)
                        .Warning("Reservation Mapper: segments mapped with warnings");
                }
            }
            else
            {
                logger.ForContext("ReservationId", original.Id)
                    .Error("Reservation Mapper: It wasn't possible to map reservation segments. Original LegSegments will be kept.");
            }

            original.LegDurations = MapDuration(original.LegSegments);

            //Override draft Fare if provider returns fresh data
            if (providerReservation.FareRules is { Count: > 0 } && providerReservation.FareRules.Values.Any(v => v.Count > 0))
            {
                FareRulesProcessor.ApplyFareRules(original, providerReservation.FareRules);
            }

            if (original.Fare != null)
            {
                providerReservation.Fare.Cancellations = original.Fare.Cancellations;
                providerReservation.Fare.Changes = original.Fare.Changes;
                providerReservation.Fare.NonRefAmounts = original.Fare.NonRefAmounts;

                if (original.Fare.PrivateFareCode != null && providerReservation.Fare.PrivateFareCode == null)
                {
                    //Use PrivateFareCode from draft reservation. We can not get it from PNR.
                    providerReservation.Fare.PrivateFareCode = original.Fare.PrivateFareCode;
                }
            }

            var fareType = original.Fare?.FareType;
            original.Fare = providerReservation.Fare;
            
            //we don't trust provider's fare type
            original.Fare.FareType = fareType ?? providerReservation.Fare.FareType;

            return original;
        }

        public static ReservationPrice BuildPrice(FlightSolution flightSolution, decimal netPrice, 
            string currency, 
            string originalCurrency, 
            Search.Shared.Enums.FareType fareType,
            PriceConverter priceConverter)
        {
            var calculationResult = priceConverter.Calculate(netPrice, 
                originalCurrency, 
                fareType, 
                platingCarrier: flightSolution.PlatingCarrier, 
                targetRebookTotalPriceDiff:flightSolution.Changes?.PriceDiffForRebookMarkup);

            return new ReservationPrice
            {
                Net = netPrice,
                Total = calculationResult.TotalPrice,
                Currency = currency,
                OriginalCurrency = originalCurrency,
                ConversionMarginRates = calculationResult.ConversionMarginRates,
                ConversionRates = calculationResult.ConversionRates,
                Markup = calculationResult.Markup,
                MarkupCurrency = calculationResult.MarkupCurrency,
                TargetMarkup = calculationResult.TargetMarkup,
                Kickback = calculationResult.Kickback,
                TargetKickback = calculationResult.TargetKickback,
                KickbackCurrency = calculationResult.KickbackCurrency,
                ConsolidatorMarkup = calculationResult.ConsolidatorMarkup,
                TargetConsolidatorMarkup = calculationResult.TargetConsolidatorMarkup,
                CompensationMarkup = calculationResult.CompensationMarkup,
                TargetCompensationMarkup = calculationResult.TargetCompensationMarkup,
                TargetCurrencyMargin = calculationResult.CurrencyMargin,
                MarkupComponents = calculationResult.MarkupComponents
            };
        }

        public static ICollection<int> MapDuration(ICollection<ICollection<Segment>> legSegments)
        {
            var durations = new List<int>();
            foreach (var leg in legSegments)
            {
                if (leg.Count > 0)
                {
                    var firstSegmentDeparture = leg.First().DepartureTimestampUtc.FromUnixTime();
                    var secondLegArrival = leg.Last().ArrivalTimestampUtc.FromUnixTime();

                    var travelduration = (int)(secondLegArrival - firstSegmentDeparture).TotalMinutes;
                    durations.Add(travelduration);
                }
            }

            return durations;
        }

        public static ICollection<int> MapDuration(IList<IList<FlightSegment>> legSegments)
        {
            var durations = new List<int>();
            foreach (var leg in legSegments)
            {
                if (leg.Count > 0)
                {
                    var firstSegmentDeparture = leg.First().DepartureTimestampUtc.FromUnixTime();
                    var secondLegArrival = leg.Last().ArrivalTimestampUtc.FromUnixTime();

                    var travelduration = (int)(secondLegArrival - firstSegmentDeparture).TotalMinutes;
                    durations.Add(travelduration);
                }
            }

            return durations;
        }

        private static ICollection<ICollection<Segment>> MapSegments(ICollection<ICollection<Segment>> legs, IList<Segment> bookedSegments,
            BookingSaga saga, out IList<string> warnings)
        {
            var legSegments = new List<ICollection<Segment>>();

            warnings = new List<string>();
            foreach (var leg in legs)
            {
                var segments = new List<Segment>();

                foreach (var expectedSegment in leg)
                {
                    var sourceSegment = bookedSegments.FirstOrDefault(s =>
                        s.Carrier == expectedSegment.Carrier
                        && s.FlightNumber == expectedSegment.FlightNumber
                        && s.DepartureDate == expectedSegment.DepartureDate
                        && s.Origin == expectedSegment.Origin
                        && s.Destination == expectedSegment.Destination);

                    // NOTE: It is possible for split flight, because it splits into multiple reserviations
                    if (sourceSegment == null)
                    {
                        continue;
                    }
                    var fareComponent = sourceSegment.FareComponent ?? expectedSegment.FareComponent;
                    if (expectedSegment.FareComponent != null &&
                        sourceSegment.FareComponent != null &&
                        expectedSegment.FareComponent != sourceSegment.FareComponent)
                    {
                        fareComponent = expectedSegment.FareComponent;  // Keep original fare component

                        var segmentOD = $"{sourceSegment.Origin}-{sourceSegment.Destination}";
                        if (segmentOD != sourceSegment.FareComponent)
                        {
                            warnings.Add($"Segment {segmentOD} has difference in fare components. Expected {expectedSegment.FareComponent}, provided {sourceSegment.FareComponent}");
                        }
                    }

                    var segment = new Segment
                    {
                        Carrier = sourceSegment.Carrier,
                        FlightNumber = sourceSegment.FlightNumber,
                        Origin = sourceSegment.Origin,
                        Destination = sourceSegment.Destination,
                        DepartureDate = sourceSegment.DepartureDate,
                        DepartureTime = sourceSegment.DepartureTime,
                        DepartureTimestamp = sourceSegment.DepartureTimestamp,
                        DepartureTimestampUtc = sourceSegment.DepartureTimestampUtc,
                        ArrivalDate = sourceSegment.ArrivalDate,
                        ArrivalTime = sourceSegment.ArrivalTime,
                        ArrivalTimestamp = sourceSegment.ArrivalTimestamp,
                        ArrivalTimestampUtc = sourceSegment.ArrivalTimestampUtc,
                        OriginTerminal = sourceSegment.OriginTerminal,
                        DestinationTerminal = sourceSegment.DestinationTerminal,
                        Connecting = sourceSegment.Connecting,
                        BookingCode = sourceSegment.BookingCode,
                        FareBasis = sourceSegment.FareBasis,
                        CabinClass = sourceSegment.CabinClass ?? expectedSegment.CabinClass,
                        Operator = sourceSegment.Operator,
                        OperatorName = sourceSegment.OperatorName,
                        FareTier = sourceSegment.FareTier ?? expectedSegment.FareTier,
                        FareComponent = fareComponent,
                        FareFamilyName = sourceSegment.FareFamilyName,
                        EquipmentCode = sourceSegment.EquipmentCode ?? expectedSegment.EquipmentCode,
                        TechnicalStops = sourceSegment.TechnicalStops ?? expectedSegment.TechnicalStops
                    };

                    var relatedFlightSegment = saga.FlightSolutionEntity.Itinerary.Segments
                        .FirstOrDefault(s => s.Carrier == expectedSegment.Carrier && s.FlightNumber == expectedSegment.FlightNumber &&
                                             s.DepartureDate == expectedSegment.DepartureDate);

                    if (relatedFlightSegment != null)
                    {
                        var reservation = saga.Reservations
                            .FirstOrDefault(r => r.ProviderItinerary.Legs
                                .SelectMany(l => l.Segments)
                                .Contains(relatedFlightSegment));

                        if (reservation is not null)
                            segment.SplitOD = $"{reservation.ProviderItinerary.Origin}-{reservation.ProviderItinerary.Destination}";
                    }

                    segments.Add(segment);
                }

                for (var i = 0; i < segments.Count - 1; i++)
                {
                    var departure = segments[i + 1].DepartureTimestampUtc.FromUnixTime();
                    var arrival = segments[i].ArrivalTimestampUtc.FromUnixTime();
                    segments[i].ConnectionDuration = (int)departure.Subtract(arrival).TotalMinutes;
                }

                if (segments.Count > 0)
                {
                    legSegments.Add(segments);
                }
            }

            return legSegments;
        }
    }
}
