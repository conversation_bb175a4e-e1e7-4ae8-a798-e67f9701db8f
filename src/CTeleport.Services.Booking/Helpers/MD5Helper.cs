using System.Security.Cryptography;
using System.Text;

namespace CTeleport.Services.Booking.Helpers
{
    public static class MD5Helper
    {
        public static string MD5Hash(string input)
        {
            using (var md5 = MD5.Create())
            {
                var hashenc = md5.ComputeHash(Encoding.ASCII.GetBytes(input));
                string result = "";
                foreach (var b in hashenc)
                {
                    result += b.ToString("x2");
                }
                return result;
            }
        }
    }
}