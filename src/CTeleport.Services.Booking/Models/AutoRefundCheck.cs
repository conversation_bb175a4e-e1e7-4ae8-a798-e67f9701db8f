namespace CTeleport.Services.Booking.Models
{
    /// <summary>
    /// Auto-refund check result
    /// </summary>
    public class AutoRefundCheck
    {
        public AutoRefundCheck()
        {
            TaxesToHold = new string[0];
        }

        /// <summary>
        /// Can auto-refund
        /// </summary>
        public bool CanAutoRefund { get; set; }

        /// <summary>
        /// Refund fee amount
        /// </summary>
        public decimal FeeAmount { get; set; }

        /// <summary>
        /// Taxes to hold
        /// </summary>
        public string[] TaxesToHold { get; set; }

        /// <summary>
        /// Fee currency, eg EUR
        /// </summary>
        public string FeeCcy { get; set; }

        /// <summary>
        /// Reason why auto refund is prohibited
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// The code of the reason why auto refund is prohibited
        /// </summary>
        public string ReasonCode { get; set; }

        /// <summary>
        /// Is predicted Auto Refund?
        /// </summary>
        public bool IsPredicted { get; set; }
    }
}
