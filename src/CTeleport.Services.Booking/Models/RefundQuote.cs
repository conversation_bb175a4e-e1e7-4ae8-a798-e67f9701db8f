namespace CTeleport.Services.Booking.Models
{
    public class RefundQuote
    {
        /// <summary>
        /// Default constructor w/o arguments to aid unit tests
        /// </summary>
        public RefundQuote() { }

        public RefundQuote(bool canCancel, decimal totalPrice, bool? refund, decimal? refundFee, string ccy, bool? isUsed, bool? isFullyUsed)
        {
            CanCancel = canCancel;
            TotalPrice = totalPrice;
            Refund = refund;
            RefundFee = refund == false ? totalPrice : refundFee;
            RefundAmount = totalPrice - RefundFee;
            UsedAmount = 0;
            Ccy = ccy;

            if (isUsed == true && isFullyUsed == true)
            {
                RefundAmount = 0;
                UsedAmount = totalPrice;
            }

            if (isUsed == null || isFullyUsed == null || isUsed == true && isFullyUsed == false)
            {
                RefundAmount = null;
                UsedAmount = null;
            }
        }


        /// <summary>
        /// Set to true, when it is _technically_ allowed by provider to cancel and refund a reservation.
        /// </summary>
        public bool CanCancel { get; set; }

        /// <summary>
        /// Total booking price in the client's currency (defined in the field Ccy below)
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// If the refund is allowed by the rules of an airline, null in case if the related fare rule hasn't been validated yet
        /// </summary>
        public bool? Refund { get; set; }

        /// <summary>
        /// Price for used part of the ticket.
        ///  TotalPrice - if we are sure that ticket was fully used.
        ///  0 - if we are sure that ticket wasn't used at all.
        ///  'null' - we cannot be sure about the ticket(s) state.
        /// </summary>
        public decimal? UsedAmount { get; set; }

        /// <summary>
        /// Total cancellation fee in booking currency, if known, null in case if the related fare rule hasn't been validated yet
        /// </summary>
        public decimal? RefundFee { get; set; }

        /// <summary>
        /// Total amount to be refunded to user
        /// </summary>
        public decimal? RefundAmount { get; set; }

        /// <summary>
        /// Target(client) currency
        /// </summary>
        public string Ccy { get; set; }
    }
}
