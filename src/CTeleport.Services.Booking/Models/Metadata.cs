using System.Collections.Generic;

namespace CTeleport.Services.Booking.Models
{
    /// <summary>
    /// Booking metadata
    /// </summary>
    public class Metadata
    {
        /// <summary>
        /// Vessel name
        /// </summary>
        public string VesselName { get; set; }

        /// <summary>
        /// Vessel flag, 2-letter country ISO code
        /// </summary>
        public string VesselFlag { get; set; }

        /// <summary>
        /// Crew change member
        /// </summary>
        public string CrewChangeMember { get; set; }

        /// <summary>
        /// Crew change airport, 3-letter IATA code
        /// </summary>
        public string CrewChangeAirport { get; set; }

        /// <summary>
        /// Crew change date, 'yyyy-MM-dd' format
        /// </summary>
        public string CrewChangeDate { get; set; }

        /// <summary>
        /// Exclude LI tax from price
        /// </summary>
        public bool? ExemptLiTax { get; set; }

        /// <summary>
        /// Custom Booking Metadata (collection of custom field stored in booking from UI and additional sources
        /// </summary>
        public Dictionary<string, string> CustomFields { get; set; }
    }
}
