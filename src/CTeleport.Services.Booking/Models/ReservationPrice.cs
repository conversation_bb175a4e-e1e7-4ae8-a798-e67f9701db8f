using System;
using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.Price.Shared.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace CTeleport.Services.Booking.Models
{
    /// <summary>
    /// Reservation price specification, including markups, margin, currency conversion details
    /// </summary>
    public class ReservationPrice
    {
        /// <summary>
        /// Net fare (reservation price), in original currency
        /// </summary>
        [BsonRepresentation(BsonType.Double, AllowTruncation = true)]
        public decimal Net { get; set; }
        
        /// <summary>
        /// C Teleport fare markup, in EUR
        /// </summary>
        public decimal Markup { get; set; }

        /// <summary>
        /// Different Parts of Markup 
        /// </summary>
        public MarkupComponents MarkupComponents { get; set; }

        /// <summary>
        /// C Teleport fare markup, in target currency
        /// </summary>
        public decimal TargetMarkup { get; set; }

        /// <summary>
        /// Kickback, in specified currency
        /// </summary>
        public decimal Kickback { get; set; }

        /// <summary>
        /// Kicback currency
        /// </summary>
        public string KickbackCurrency { get; set; }

        /// <summary>
        /// Kickback, in target currency
        /// </summary>
        public decimal TargetKickback { get; set; }

        /// <summary>
        /// Consolidator markup in consolidator currency
        /// </summary>
        public decimal ConsolidatorMarkup { get; set; }

        /// <summary>
        /// Consolidator markup in target currency
        /// </summary>
        public decimal TargetConsolidatorMarkup { get; set; }

        /// <summary>
        /// Compensation markup in source currency
        /// </summary>
        public decimal CompensationMarkup { get; set; }

        /// <summary>
        /// Compensation markup in target currency
        /// </summary>
        public decimal TargetCompensationMarkup { get; set; }

        /// <summary>
        /// Sum of all currency convertions securty margins in target currency
        /// </summary>
        public decimal TargetCurrencyMargin { get; set; }

        /// <summary>
        /// Currency conversion rates for currency pairs Origin-Target
        /// </summary>
        public Dictionary<string, decimal> ConversionRates { get; set; }

        /// <summary>
        /// Currency conversion margin rates for currency pairs Origin-Target
        /// </summary>
        public Dictionary<string, decimal> ConversionMarginRates { get; set; }

        /// <summary>
        /// Total reservation price, in target currency. Includes rounding margin, if rounding is applied
        /// </summary>
        [BsonRepresentation(BsonType.Double, AllowTruncation = true)]
        public decimal Total { get; set; }

        /// <summary>
        /// Target currency 
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Original currency
        /// </summary>
        public string OriginalCurrency { get; set; }

        /// <summary>
        /// Markup Currency
        /// </summary>
        public string MarkupCurrency { get; set; }

        /// <summary>
        /// Payment ID. Null if PaymentMethod is bank-transfer or amex-bta
        /// </summary>
        public string PaymentId { get; set; }

        public static ReservationPrice operator +(ReservationPrice a, ReservationPrice b)
        {
            if (a.Currency != b.Currency 
                || a.OriginalCurrency != b.OriginalCurrency
                || a.KickbackCurrency != b.KickbackCurrency
                || a.MarkupCurrency != b.MarkupCurrency)
            {
                throw new InvalidOperationException("Cannot sum prices in different currencies");
            }

            var conversionRates = a.ConversionRates ?? new Dictionary<string, decimal>();
            foreach (var (ccy, value) in b.ConversionRates)
            {
                if (!conversionRates.ContainsKey(ccy))
                {
                    conversionRates.Add(ccy, value);
                }
                else if (conversionRates[ccy] != value)
                {
                    throw new InvalidOperationException("Cannot sum prices with different currency rates");
                }
            }

            var conversionMarginRates = a.ConversionMarginRates;
            foreach (var (ccy, value) in b.ConversionMarginRates)
            {
                if (!conversionMarginRates.ContainsKey(ccy))
                {
                    conversionMarginRates.Add(ccy, value);
                }
                else if (conversionMarginRates[ccy] != value)
                {
                    throw new InvalidOperationException("Cannot sum prices with different currency rates");
                }
            }

            return new ReservationPrice
            {
                Currency = a.Currency,
                Kickback = a.Kickback + b.Kickback,
                Markup = a.Markup + b.Markup,
                Net = a.Net + b.Net,
                Total = a.Total + b.Total,
                CompensationMarkup = a.CompensationMarkup + b.CompensationMarkup,
                ConsolidatorMarkup = a.ConsolidatorMarkup + b.ConsolidatorMarkup,
                ConversionRates = conversionRates,
                ConversionMarginRates = conversionMarginRates,
                KickbackCurrency = a.KickbackCurrency,
                MarkupComponents = new MarkupComponents
                {
                    BaseMarkup = a.MarkupComponents?.BaseMarkup ?? 0 + b.MarkupComponents?.BaseMarkup ?? 0,
                    BaseTargetMarkup = a.MarkupComponents?.BaseTargetMarkup ?? 0 + b.MarkupComponents?.BaseTargetMarkup ?? 0,
                    HighFareTargetMarkup = a.MarkupComponents?.HighFareTargetMarkup ?? 0 + b.MarkupComponents?.HighFareTargetMarkup ?? 0,
                    MarineFareTargetMarkup = a.MarkupComponents?.MarineFareTargetMarkup ?? 0 + b.MarkupComponents?.MarineFareTargetMarkup ?? 0,
                },
                MarkupCurrency = a.MarkupCurrency,
                OriginalCurrency = a.OriginalCurrency,
                TargetKickback = a.TargetKickback + b.TargetKickback,
                TargetMarkup = a.TargetMarkup + b.TargetMarkup,
                TargetCompensationMarkup = a.TargetCompensationMarkup + b.TargetCompensationMarkup,
                TargetConsolidatorMarkup = a.TargetConsolidatorMarkup + b.TargetConsolidatorMarkup,
                TargetCurrencyMargin = a.TargetCurrencyMargin + b.TargetCurrencyMargin
            };
        }
    }
}