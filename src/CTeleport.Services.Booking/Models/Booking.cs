using System;
using System.Collections.Generic;
using CTeleport.Api;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.FrequentFlyer.Models;

namespace CTeleport.Services.Booking.Models
{
    /// <summary>
    /// Booking model
    /// </summary>
    public class Booking : IUpdateTrackingEntity
    {
        public string Id { get; set; }

        /// <summary>
        /// In case of rebooking (cancel/book new PNR), here we have id of the original booking
        /// </summary>
        public string OriginalBookingId { get; set; }
        /// <summary>
        /// Id of first booking. Will be the same for booking after changes.
        /// </summary>
        public string InitialBookingId { get; set; }
        /// <summary>
        /// Locator of inital reservation or virtual locator
        /// </summary>
        public string InitialLocator { get; set; }
        public string SearchId { get; set; }
        public string FlightSolutionId { get; set; }
        public string RouteId { get; set; }
        public string Locator { get; set; }
        public PassengerDetails Passenger { get; set; }
        public BookingTerms Terms { get; set; }
        public Metadata Metadata { get; set; }
        public BookingState State { get; set; }
        public BookingPrice Price { get; set; }
        public DateTime DepartureAt { get; set; }
        public ICollection<Leg> Legs { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? CancelledAt { get; set; }
        public Search.Shared.Models.User CreatedBy { get; set; }
        public string TenantId { get; set; }
        public bool NoShow { get; set; }
        public string Comment { get; set; }
        public Search.Shared.Models.User CommentedBy { get; set; }
        public Search.Shared.Models.User CancelledBy { get; set; }
        public Search.Shared.Models.User RejectedBy { get; set; }
        public string RejectReason { get; set; }
        public DateTime? RejectedAt { get; set; }
        public ICollection<Search.Shared.Models.User> ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }

        #region Payment specifications

        /// <summary>
        /// Payment method
        /// </summary>
        public PaymentMethod PaymentMethod { get; set; }

        /// <summary>
        /// Payment method id, e.g. reference to credit card.
        /// Empty value for bank-transfer and amex-bta payment methods
        /// </summary>
        public string PaymentMethodId { get; set; }

        /// <summary>
        /// Payment reference id, value is set for payments with credit card
        /// </summary>
        public string PaymentId { get; set; }

        /// <summary>
        /// Payment hold expiration date. Payment will be cancelled at that time
        /// </summary>
        public DateTime? PaymentExpiresAt { get; set; }

        #endregion

        /// <summary>
        /// Invoicee id selected during the booking
        /// </summary>
        public int InvoiceeId { get; set; }

        /// <summary>
        /// Key - LoyaltyProgramComponent.Key
        /// </summary>
        public IDictionary<string, FrequentFlyerNumber> FrequentFlyerNumbers { get; set; }
        
        /// <summary>
        /// Site or location of traveler final destination (oil rig, mining site, etc.)
        /// </summary>
        public Site Site { get; set; }
    }
}