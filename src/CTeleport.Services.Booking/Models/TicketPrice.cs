using System.Collections.Generic;
using CTeleport.Services.Booking.Shared.Models;

namespace CTeleport.Services.Booking.Models
{
    public class TicketPrice
    {
        /// <summary>
        /// Net ticket price, as charged by airline
        /// </summary>
        public decimal Net { get; set; }

        /// <summary>
        /// Taxes included in the net price. Tax category as a key and tax amount as a value
        /// </summary>
        public IDictionary<string, decimal> Taxes { get; set; }

        /// <summary>
        /// PFC tax details
        /// </summary>
        public IList<TaxDetail> TaxDetails { get; set; }

        /// <summary>
        /// Currency for this ticket
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Fare calc including taxes
        /// </summary>
        public string FareCalc { get; set; }
    }
}