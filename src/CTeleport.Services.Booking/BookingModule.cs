using System.Reflection;
using Amazon;
using Amazon.SecretsManager;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using CTeleport.Common.Cloud;
using CTeleport.Common.Cloud.Aws;
using CTeleport.Common.Extensions;
using CTeleport.Common.GoogleSpreadsheet;
using CTeleport.Common.Host;
using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Galileo.Configuration;
using CTeleport.Services.FareCache;
using CTeleport.Services.FlightStatus;
using CTeleport.Services.CheckFare;
using CTeleport.Services.CustomFields.Configuration;
using CTeleport.Services.CustomFields.Services;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Helpers.Interfaces;
using CTeleport.Services.Booking.Options;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Export;
using CTeleport.Services.Booking.Services.HistoricalBookingsUpdateServices;
using CTeleport.Services.Booking.Services.Implementations;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Services.PnrEventProcessingServices;
using CTeleport.Services.Booking.Validation;
using CTeleport.Services.FlightStatus.Cache;
using CTeleport.Services.Booking.Services.SafeOperations;
using CTeleport.Services.Helpers;
using Microsoft.Extensions.Configuration;
using ProvidersCode = CTeleport.Services.Helpers.Constants.Providers;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Services.ClassDrop;
using CTeleport.Services.VoidCalc.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using CTeleport.Services.FareRules.Client.Extensions;

namespace CTeleport.Services.Booking
{
    public class BookingModule : Autofac.Module
    {
        private readonly IConfiguration _configuration;

        public BookingModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        private static string KEY = "CTeleport.Services.Booking";

        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterInstance(_configuration.GetSettings<NotificationsOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<RefundOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<Configuration.AuthzOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<Configuration.TicketingOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<Configuration.TicketStateValidationOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<Configuration.BillingOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<Configuration.FareRefreshOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<RemarksOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<Configuration.BookingExportOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<GalileoOptions>()).SingleInstance();
            builder.RegisterInstance(_configuration.GetSettings<BookingSoonExpirationOptions>()).SingleInstance();

            var assembly = typeof(BookingModule).GetTypeInfo().Assembly;

            builder.RegisterAssemblyTypes(assembly).AsClosedTypesOf(typeof(IEventHandler<>), KEY);
            builder.RegisterAssemblyTypes(assembly).AsClosedTypesOf(typeof(ICommandHandler<>), KEY);

            builder.RegisterType<DatabaseInitializer>().As<IDatabaseInitializer>();

            builder.RegisterType<DatabaseInitializer>().Keyed<IDatabaseInitializer>(KEY);

            builder.RegisterType<BookingsRepository>().As<IBookingsRepository>();
            builder.RegisterType<ReservationsRepository>().As<IReservationsRepository>();
            builder.RegisterType<BookingSagaDetailsRepository>().As<IBookingSagaDetailsRepository>();
            builder.RegisterType<SingleNameSettingsRepository>().As<ISingleNameSettingsRepository>();
            builder.RegisterType<PendingReservationRepository>().As<IPendingReservationRepository>();

            builder
                .Register((ctx, p) => ctx.Resolve<IApiClientProxyFactory>().CreateApiClient<BillingClient.IBillingApi>(ctx.Resolve<Configuration.BillingOptions>().Url))
                .As<BillingClient.IBillingApi>();
            builder.RegisterType<BillingClient>().As<IBillingClient>();
            builder.RegisterType<AuthzClient>().As<IAuthzClient>().SingleInstance();

            builder.RegisterType<AuthService>().As<IAuthService>();
            builder.RegisterType<AuthzQueryParserService>().As<IAuthzQueryParserService>();
            builder.RegisterType<SingleNameService>().As<ISingleNameService>();
            builder.RegisterType<BookingService>().As<IBookingService>();
            builder.RegisterType<BookingSagaService>().As<IBookingSagaService>();
            builder.RegisterType<BookingPaymentService>().As<IBookingPaymentService>(); 
            builder.RegisterType<BookingRepriceService>().As<IBookingRepriceService>(); 
            builder.RegisterType<BookingExportService>().As<IBookingExportService>();
            builder.RegisterType<BookingStructuredExportService>().As<IBookingStructuredExportService>();
            builder.RegisterType<TicketService>().As<ITicketService>();
            builder.RegisterType<VoidCalc.Services.SourceTimeZoneResolverService>().As<ISourceTimeZoneResolverService>();
            builder.RegisterType<RequestAutoRefundBuilder>().As<IRequestAutoRefundBuilder>();
            builder.RegisterType<AutoRefundService>().As<IAutoRefundService>();
            builder.RegisterType<ClassDropService>().As<IClassDropService>();
            
            AddClassDropVerifier(builder);

            builder.RegisterType<TicketUsageService>().As<ITicketUsageService>();
            builder.RegisterType<ClassDropOptionsService>().As<IClassDropOptionsService>();
            builder.RegisterType<CommonRemarksBuilderService>().As<ICommonRemarksBuilderService>();
            builder.RegisterType<ReservationBuilder>().As<IReservationBuilder>();
            builder.RegisterType<BookingApprovalItemsService>().As<IBookingApprovalItemsService>();
            builder.RegisterType<BrokenReservationService>().As<IBrokenReservationService>();
            builder.RegisterType<PendingReservationService>().As<IPendingReservationService>();
            builder.RegisterType<BookingAlternativeSolutionService>().As<IBookingAlternativeSolutionService>();
            builder.RegisterType<SearchBookingStatsService>().As<ISearchBookingStatsService>();
            builder.RegisterType<ReissueTicketService>().As<IReissueTicketService>();
            builder.RegisterType<TicketPriceChangeService>().As<ITicketPriceChangeService>();
            builder.RegisterType<LegRecalculationService>().As<ILegRecalculationService>();
            builder.RegisterType<BookingStateService>().As<IBookingStateService>();
            builder.RegisterType<SegmentSyncService>().As<ISegmentSyncService>();
            builder.RegisterType<SyncReservationService>().As<ISyncReservationService>();
            builder.RegisterType<FareChangeService>().As<IFareChangeService>();
            builder.RegisterType<CO2EmissionsService>().As<ICO2EmissionsService>();
            builder.RegisterType<TicketingQueueService>().As<ITicketingQueueService>();
            builder.RegisterType<Cache.CommandContextCache>().As<Cache.ICommandContextCache>();
            builder.RegisterType<Cache.TicketRefundCache>().As<Cache.ITicketRefundCache>();
            builder.RegisterType<TicketInfoCache>().As<ITicketInfoCache>();
            builder.RegisterType<AgentMetadataService>().As<IAgentMetadataService>();
            builder.RegisterType<ExtendedStructuredBookingMapper>().As<IExtendedStructuredBookingMapper>();
            builder.RegisterType<ReservationRefundService>().As<IReservationRefundService>();
            builder.RegisterType<ReservationRefundNotificationService>().As<IReservationRefundNotificationService>();
            builder.RegisterType<SagaMapper>().As<ISagaMapper>();
            builder.RegisterType<BookingCheckingExpirationService>().As<IBookingCheckingExpirationService>();
            builder.RegisterType<BookingVariantService>().As<IBookingVariantService>();
            builder.RegisterType<HidePenaltiesService>().As<IHidePenaltiesService>();

            builder.RegisterType<ProviderReservationInfoServiceDecorator>().As<IProviderReservationInfoService>();
            builder.RegisterType<ProviderRepriceReservationServiceDecorator>().As<IProviderRepriceReservationService>();
            builder.RegisterType<ProviderClassdropRepriceServiceDecorator>().As<IProviderClassdropRepriceService>();
            builder.RegisterType<ProviderConfirmReservationServiceDecorator>().As<IProviderConfirmReservationService>();

            builder.RegisterType<PnrEventProcessingServiceDecorator>().As<IPnrEventProcessingService>();
            builder.RegisterType<AmadeusPnrEventProcessingService>().Keyed<IPnrEventProcessingService>(ProvidersCode.AMADEUS);
            builder.RegisterType<GalileoPnrEventProcessingService>().Keyed<IPnrEventProcessingService>(ProvidersCode.GALILEO);
            
            builder.RegisterType<ProviderTicketStateServiceDecorator>().As<IProviderTicketStateService>();
            builder.RegisterType<ProviderTicketStateCacheAdapter>().As<IProviderTicketStateCacheAdapter>();

            builder.RegisterType<FareRulesCache>().As<IFareRulesCache>();

            builder.RegisterType<ProviderCheckFareClientDecorator>().As<IProviderCheckFareService>();

            builder.RegisterType<BillingDetailsValidator>().As<IBillingDetailsValidator>();

            builder.RegisterType<RedisSafeOperationsService>().As<IRedisSafeOperationsService>();
            builder.RegisterType<CompletedReissueService>().As<ICompletedReissueService>();

            builder.RegisterInstance<IAmazonSecretsManager>(
                new AmazonSecretsManagerClient(RegionEndpoint.GetBySystemName(_configuration["Aws:PrimarySecretsRegion"]))).SingleInstance();
            builder.RegisterType<Services.SecretsManagerService>().As<Services.ISecretsManagerService>().SingleInstance();

            builder.RegisterType<AwsConnect>().As<ICloudConnect>().SingleInstance();
            builder.RegisterType<Common.Cloud.SecretsManagerService>().As<Common.Cloud.ISecretsManagerService>().SingleInstance();
            builder.RegisterType<SpreadsheetClient>().As<ISpreadsheetClient>().SingleInstance();
            builder.RegisterType<SafeOperationService>().As<ISafeOperationService>();

            builder.RegisterType<RetrieveSupplierLocatorsHandlerService>().As<IRetrieveSupplierLocatorsHandlerService>();
            builder.RegisterType<RefreshReservationFareHandlerService>().As<IRefreshReservationFareHandlerService>();

            builder.RegisterType<BookingMetricsService>().As<IBookingMetricsService>();

            builder.RegisterType<HistoricalBookingsUpdateService>().As<IHistoricalBookingsUpdateService>();
            
            builder.RegisterType<FareMaskValidityChecker>().As<IFareMaskValidityChecker>();
            builder.RegisterType<ApprovalFlowService>().As<IApprovalFlowService>();

            builder.RegisterType<ReservationManualCancellationService>().As<IReservationManualCancellationService>();

            RegisterFareRulesServices(builder);
        }

        private static void AddClassDropVerifier(ContainerBuilder builder)
        {
            builder.RegisterType<ClassDropVerifier>().SingleInstance().As<IClassDropVerifier>();

            builder.RegisterType<BookingCodesIdenticalCheck>().As<IClassDropCheck>();
            builder.RegisterType<FaresIdenticalCheck>().As<IClassDropCheck>();
            builder.RegisterType<PriceLowerCheck>().As<IClassDropCheck>();
            builder.RegisterType<CabinClassesEqualCheck>().As<IClassDropCheck>();
            builder.RegisterType<BaggageEqualCheck>().As<IClassDropCheck>();
            builder.RegisterType<FareRulesEqualCheck>().As<IClassDropCheck>();
        }

        private void RegisterFareRulesServices(ContainerBuilder builder)
        {
            // FareRules related services
            builder.RegisterType<FareRulesEnrichmentService>().As<IFareRulesEnrichmentService>();
            builder.RegisterType<FareRulesProcessor>().As<IFareRulesProcessor>();

            // Register FareRules API client with cache
            var services = new ServiceCollection();
            services.AddFareRulesApiClientWithCache(
                apiOptions => _configuration.GetSection("FareRulesApi").Bind(apiOptions)
            );
            builder.Populate(services);
        }

        public static void SeedDatabase(IResolver resolver)
        {
            resolver.ResolveKeyed<IDatabaseInitializer>(KEY).Initialize();
        }
    }
}