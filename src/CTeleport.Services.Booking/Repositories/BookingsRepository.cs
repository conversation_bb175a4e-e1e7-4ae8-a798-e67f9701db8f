using CTeleport.Common.Mongo;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories.FilterConverters;
using CTeleport.Services.Booking.Repositories.Queries;
using CTeleport.Services.Booking.Repositories.TransactionManagement;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Repositories
{
    public class BookingsRepository : SafeRepositoryBase, IBookingsRepository
    {
        private readonly IMongoCollection<Models.Booking> _collection;
        private readonly IDbSessionAccessor _dbSessionAccessor;

        public BookingsRepository(IMongoDatabase database, ILogger logger, 
            IDbSessionAccessor dbSessionAccessor) : base(database, logger)
        {
            _dbSessionAccessor = dbSessionAccessor;
            _collection = database.Bookings();
        }

        public async Task AddAsync(Models.Booking booking)
            => await _collection.ReplaceOneAsync(b => b.Id == booking.Id, booking, new UpdateOptions { IsUpsert = true });

        public async Task<Models.Booking> GetAsync(string id)
        {
            var session = _dbSessionAccessor.GetSession(); 
            if (session == null)
            {
                return await SafeReadAsync(_collection.AsQueryable()
                    .Where(b => b.Id == id)
                    .FirstOrDefaultAsync());    
            }

            return await SafeReadAsync(_collection.AsQueryable(session: session)
                .Where(b => b.Id == id)
                .FirstOrDefaultAsync());
        }

        public async Task<IEnumerable<Models.Booking>> GetByIdsAsync(IEnumerable<string> ids)
        {
            var filter = Builders<Models.Booking>.Filter.In(x => x.Id, ids);
            var bookings = await ReadFilteredAsync(filter);
            
            return bookings;
        }

        public async Task SetStateAsync(string id, BookingState state)
            => await _collection.FindOneAndUpdateAsync(
                r => r.Id == id,
                Builders<Models.Booking>.Update.Set(r => r.State, state)
            );

        public async Task SetBookingCancelledByAsync(string bookingId, User user)
            => await _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update.Set(r => r.CancelledBy, user).Set(r => r.CancelledAt, DateTime.UtcNow)
            );

        public Task SetAsDeclinedByAsync(string bookingId, User rejectedBy, string reason)
            => _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update
                .Set(r => r.State, BookingState.Declined)
                .Set(r => r.RejectedBy, rejectedBy)
                .Set(r => r.RejectReason, reason)
                .Set(r => r.RejectedAt, DateTime.UtcNow));

        public Task SetAsApprovedByAsync(string bookingId, params User[] approvedBy)
            => _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update
                .Set(r => r.State, BookingState.Confirmed)
                .Set(r => r.ApprovedBy, approvedBy)
                .Set(r => r.ApprovedAt, DateTime.UtcNow));
        
        [Obsolete]
        public async Task<IEnumerable<Models.Booking>> GetAllScopedAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters = null,
            DateTime? startFrom = null, DateTime? endAt = null)
        {
            var filter = CreateBaseFilter(securityFilters);

            var builder = new FilterDefinitionBuilder<Models.Booking>();
            if (startFrom.HasValue)
            {
                filter = builder.And(filter, builder.Gt(x => x.CreatedAt, startFrom.Value));
            }

            if (endAt.HasValue)
            {
                filter = builder.And(filter, builder.Lt(x => x.CreatedAt, endAt.Value.AddDays(1).Date));
            }

            return await ReadFilteredAsync(filter);
        }

        public Task<IEnumerable<Models.Booking>> GetPagedAsync(IEnumerable<IEnumerable<FilterCondition>> filters,
            int limit, int offset, DateTime? createdAfter, DateTime? createdUntil, CancellationToken cancellationToken)
        {
            var filterDefinition = FilterBuilderDefinition(filters, createdAfter, createdUntil);
            var hasSecurityFilters = filters.Any();
            return ReadFilteredAsync(filterDefinition, new FindOptions<Models.Booking>
            {
                Skip = offset,
                Limit = limit,
                Sort = Builders<Models.Booking>.Sort.Ascending(b => b.CreatedAt),
                Hint = hasSecurityFilters ? BookingQueries.Indexes.TenantIdCreatedAt : null
            }, cancellationToken);
        }

        private static FilterDefinition<Models.Booking> FilterBuilderDefinition(
            IEnumerable<IEnumerable<FilterCondition>> baseFilters, DateTime? createdFrom, DateTime? createdUntil)
        {
            var filters = BuildFilters(baseFilters, createdFrom, createdUntil);
            
            return Builders<Models.Booking>.Filter.And(filters.ToArray());
        }

        private static IEnumerable<FilterDefinition<Models.Booking>> BuildFilters(
            IEnumerable<IEnumerable<FilterCondition>> baseFilters, DateTime? createdFrom, DateTime? createdUntil)
        {
            yield return CreateBaseFilter(baseFilters);
            
            if (createdFrom.HasValue)
            {
                yield return Builders<Models.Booking>.Filter.Gte(b => b.CreatedAt, createdFrom.Value);
            }
            
            if (createdUntil.HasValue)
            {
                yield return Builders<Models.Booking>.Filter.Lte(b => b.CreatedAt, createdUntil.Value);
            }
        }
        
        public async Task<IEnumerable<Models.Booking>> GetBatchScopedAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, BookingsPagingToken startFrom,
            BookingsPagingToken endAt, int batchSize)
        {
            var baseFilter = CreateBaseFilter(securityFilters);
            var updatedAtName = nameof(Models.Booking.UpdatedAt);
            var createdAtName = nameof(Models.Booking.CreatedAt);
            var sort = startFrom != null
                ? Builders<Models.Booking>.Sort.Ascending(updatedAtName).Ascending(createdAtName)
                : Builders<Models.Booking>.Sort.Descending(updatedAtName).Descending(createdAtName);

            var options = new FindOptions<Models.Booking, Models.Booking>
            {
                Limit = batchSize,
                Sort = sort
            };

            var dateFilter = FilterDefinition<Models.Booking>.Empty;
            var builder = new FilterDefinitionBuilder<Models.Booking>();
            if (startFrom != null)
            {
                //(UpdatedAt > startFromUpdated || (UpdatedAt == startFromUpdated && CreatedAt >= startFromCreated))
                dateFilter = builder.Or(
                    builder.Gt(booking => booking.UpdatedAt, startFrom.UpdatedAt),
                    builder.And(
                        builder.Eq(booking => booking.UpdatedAt, startFrom.UpdatedAt),
                        builder.Gte(booking => booking.CreatedAt, startFrom.CreatedAt)));
            }

            if (endAt != null)
            {
                //(UpdatedAt < endAtUpdated || (UpdatedAt == endAtUpdated && CreatedAt <= endAtCreated))
                dateFilter = builder.Or(
                    builder.Lt(booking => booking.UpdatedAt, endAt.UpdatedAt),
                    builder.And(
                        builder.Eq(booking => booking.UpdatedAt, endAt.UpdatedAt),
                        builder.Lte(booking => booking.CreatedAt, endAt.CreatedAt)));
            }

            var filter = builder.And(baseFilter, dateFilter);
            return await ReadFilteredAsync(filter, options);
        }
        
        public Task<long> CountAsync(IEnumerable<IEnumerable<FilterCondition>> filters, DateTime? createdAfter,
            DateTime? createdUntil, CancellationToken cancellationToken = default)
        {
            var filterDefinition = FilterBuilderDefinition(filters, createdAfter, createdUntil);
            
            return SafeReadAsync(_collection.CountDocumentsAsync(filterDefinition, cancellationToken: cancellationToken));
        }

        public async Task UpdatePriceAsync(string bookingId, BookingPrice bookingPrice)
        {
            var filter = Builders<Models.Booking>.Filter.Eq(b => b.Id, bookingId);
            var updateDefinition = Builders<Models.Booking>.Update
                .Set(b => b.Price, bookingPrice)
                .Set(b => b.UpdatedAt, DateTime.UtcNow);
            
            var session = _dbSessionAccessor.GetSession();
            if (session == null)
            {
                await _collection.FindOneAndUpdateAsync(filter, updateDefinition);
            }
            else
            {
                await _collection.FindOneAndUpdateAsync(session, filter, updateDefinition);
            }
        }

        private static FilterDefinition<Models.Booking> CreateBaseFilter(IEnumerable<IEnumerable<FilterCondition>> securityFilters)
        {
            // TODO: consider to always require securityFilters to be set
            var filter = securityFilters == null
                ? FilterDefinition<Models.Booking>.Empty
                : BuildSecurityFilter(securityFilters);
            return filter;
        }

        public async Task<IEnumerable<Models.Booking>> GetAllAsync(DateTime startFrom, DateTime endAt, string tenantId = null)
        {
            var builder = new FilterDefinitionBuilder<Models.Booking>();
            var filter = builder.Gt(x => x.CreatedAt, startFrom);
            filter = builder.And(filter, builder.Lt(x => x.CreatedAt, endAt.AddDays(1).Date));
            if (!string.IsNullOrWhiteSpace(tenantId))
            {
                filter = builder.And(filter, builder.Eq(x => x.TenantId, tenantId));
            }
            return await SafeReadAsync(Database.BookingsSecondary().Find(filter).ToListAsync());
        }

        public async Task<IEnumerable<Models.Booking>> GetUpdatedSinceBatchAsync(int limit, DateTime sinceUpdatedAt, DateTime sinceCreatedAt)
            => await SafeReadAsync(Database.BookingsSecondary().AsQueryable()
                    .Where(x => x.UpdatedAt > sinceUpdatedAt || x.UpdatedAt == sinceUpdatedAt && x.CreatedAt >= sinceCreatedAt)
                    .OrderBy(x => x.UpdatedAt).ThenBy(x => x.CreatedAt)
                    .Take(limit)
                    .ToListAsync());

        public async Task<IEnumerable<Models.Booking>> GetAllForTenantAsync(string tenantId)
            => await SafeReadAsync(_collection.AsQueryable().Where(b => b.TenantId == tenantId).ToListAsync());

        public async Task<IEnumerable<Models.Booking>> GetActiveForCrewChangeAsync(string vesselName, string crewChangeDate)
            => await SafeReadAsync(_collection.AsQueryable()
                .Where(b => (b.State != BookingState.Cancelled && b.State != BookingState.Declined && b.State != BookingState.RefundPending)
                            && b.Metadata.VesselName == vesselName && b.Metadata.CrewChangeDate == crewChangeDate)
                .ToListAsync());

        public async Task<IEnumerable<Models.Booking>> GetRecentlyDepartedAsync(int minutes)
            => await SafeReadAsync(_collection.AsQueryable()
                .Where(b => b.State == BookingState.Issued
                            && b.DepartureAt < DateTime.UtcNow.AddMinutes(-minutes))
                .ToListAsync());

        public async Task<IEnumerable<Models.Booking>> GetBySearchIdAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, string searchId)
        {
            var filter = BuildSecurityFilter(securityFilters);

            var builder = new FilterDefinitionBuilder<Models.Booking>();
            filter = builder.And(filter, builder.Eq(b => b.SearchId, searchId));

            return await ReadFilteredAsync(filter);
        }

        public async Task<IEnumerable<Models.Booking>> GetByPassengerAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, string firstName, string lastName)
        {
            var filter = BuildSecurityFilter(securityFilters);

            var builder = new FilterDefinitionBuilder<Models.Booking>();
            filter = builder.And(filter, builder.Where(b =>
                b.Passenger.LastName == lastName &&
                b.Passenger.FirstName == firstName));

            return await ReadFilteredAsync(filter, new FindOptions<Models.Booking>
            {
                Hint = BookingQueries.Indexes.PassengerName, 
                Collation =  BookingQueries.IgnoreCaseCollation
            });
        }

        public async Task<IEnumerable<Models.Booking>> GetActiveByPassengerInDatesRangeAsync(
            IEnumerable<IEnumerable<FilterCondition>> securityFilters, string firstName, string lastName,
            DateTime startFrom, DateTime endAt)
        {
            var filter = BuildSecurityFilter(securityFilters);

            var builder = new FilterDefinitionBuilder<Models.Booking>();
            filter = builder.And(filter, builder.Where(b =>
                b.State != BookingState.Cancelled && b.State != BookingState.Declined && b.State != BookingState.RefundPending &&
                b.DepartureAt >= startFrom && b.DepartureAt <= endAt &&
                b.Passenger.LastName == lastName &&
                b.Passenger.FirstName == firstName));

            return await ReadFilteredAsync(filter, new FindOptions<Models.Booking>
            {
                Hint = BookingQueries.Indexes.PassengerName, 
                Collation =  BookingQueries.IgnoreCaseCollation
            });
        }

        public async Task<IEnumerable<Models.Booking>> GetByUserIdAsync(string userId)
            => await SafeReadAsync(_collection.AsQueryable().Where(b => b.CreatedBy.Id == userId).ToListAsync());

        public async Task<IEnumerable<Vessel>> GetBookedVesselsAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, string vesselName, int maxCount)
        {
            var filter = BuildSecurityFilter(securityFilters);

            var builder = new FilterDefinitionBuilder<Models.Booking>();
            filter = builder.And(filter, builder.Where(b => b.Metadata.VesselName != null));
            
            if (vesselName != null)
            {
                filter = builder.And(filter, builder.Regex(b => b.Metadata.VesselName, new Regex("^" + vesselName + ".*", RegexOptions.IgnoreCase)));
            }

            maxCount = maxCount == 0 ? int.MaxValue : maxCount;
            var vessels = await SafeReadAsync(_collection.Aggregate()
                .Match(filter)
                .Group(
                    b => new { Name = b.Metadata.VesselName, Flag = b.Metadata.VesselFlag },
                    g => new { vessel = g.Key })
                .Limit(maxCount)
                .ToListAsync());

            return vessels.Select(x => new Vessel
                {
                    Name = x.vessel.Name,
                    Flag = new Country
                    {
                        Code = x.vessel.Flag
                    }
                })
                .OrderBy(v => v.Name)
                .ToList();
        }

        public async Task SetAsNoShowAsync(string bookingId)
            => await _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update
                    .Set(r => r.NoShow, true));

        public async Task UpdateComment(string bookingId, string comment, User user)
            => await _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update
                    .Set(r => r.Comment, comment)
                    .Set(r => r.CommentedBy, user));

        public async Task UpdatePassengerDetails(string bookingId, MutablePassengerDetails passengerDetails)
            => await _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update
                    .Set(r => r.Passenger.Gender, passengerDetails.Gender)
                    .Set(r => r.Passenger.DateOfBirth, passengerDetails.DateOfBirth)
                    .Set(r => r.Passenger.Nationality, passengerDetails.Nationality)
                    .Set(r => r.Passenger.DocType, passengerDetails.DocType)
                    .Set(r => r.Passenger.DocNumber, passengerDetails.DocNumber)
                    .Set(r => r.Passenger.DocCountry, passengerDetails.DocCountry)
                    .Set(r => r.Passenger.DocExpire, passengerDetails.DocExpire)
                );

        public async Task<List<Models.Booking>> GetBookingsInStateAsync(BookingState state)
            => await SafeReadAsync(_collection.AsQueryable().Where(b => b.State == state).ToListAsync());

        public async Task UpdateVesselAsync(string bookingId, VesselDetails vessel)
            => await _collection.FindOneAndUpdateAsync(
                r => r.Id == bookingId,
                Builders<Models.Booking>.Update
                    .Set(r => r.Metadata.VesselName, vessel.Name)
                    .Set(r => r.Metadata.VesselFlag, vessel.Flag));

        public async Task<Models.Booking> GetByFlightSolutionAsync(string flightSolutionId)
            => await SafeReadAsync(_collection.AsQueryable().Where(b => b.FlightSolutionId == flightSolutionId).FirstOrDefaultAsync());

        public async Task<Models.Booking> GetTenantFirstBookingAsync(string tenant)
            => await SafeReadAsync(_collection.AsQueryable().OrderBy(b => b.CreatedAt).Where(b => b.TenantId == tenant).FirstOrDefaultAsync());

        public async Task UpdateLegsAsync(string id, ICollection<Leg> legs)
        {
            var filter = Builders<Models.Booking>.Filter.Eq(b => b.Id, id);
            var updateDefinition = Builders<Models.Booking>.Update
                .Set(b => b.Legs, legs)
                .Set(b => b.DepartureAt, legs.First().DepartureUtc.ToDateTimeUtc());

            var session = _dbSessionAccessor.GetSession();
            if (session == null)
            {
                await _collection.FindOneAndUpdateAsync(filter, updateDefinition);
            }
            else
            {
                await _collection.FindOneAndUpdateAsync(session:session, filter, updateDefinition);
            }
        }

        public async IAsyncEnumerable<IReadOnlyCollection<Models.Booking>> GetRefundubleBookingsByBatchesAsync(
            DateTime? startDate,
            DateTime? endDate,
            int batchSize = 10,
            [EnumeratorCancellation] CancellationToken cancellationToken = default)
        {
            var filterByStatus = 
                Builders<Models.Booking>.Filter.Eq(b => b.State, BookingState.Confirmed)
                | Builders<Models.Booking>.Filter.Eq(b => b.State, BookingState.Issued);

            var filterByRefundubility =
                Builders<Models.Booking>.Filter.Eq(b => b.Terms.Cancellations, Search.Shared.Enums.RefundCondition.Refundable)
                | Builders<Models.Booking>.Filter.Eq(b => b.Terms.Cancellations, Search.Shared.Enums.RefundCondition.RefundableWithDeadline);

            var filterByCancellation =
                Builders<Models.Booking>.Filter.Eq(b => b.Terms.CanCancel, true);

            var filterByStartDate = startDate is not null
                ? Builders<Models.Booking>.Filter.Gte(b => b.CreatedAt, startDate)
                : Builders<Models.Booking>.Filter.Empty;

            var filterByEndDate = startDate is not null
                ? Builders<Models.Booking>.Filter.Lt(b => b.CreatedAt, endDate)
                : Builders<Models.Booking>.Filter.Empty;

            var filter = filterByStatus & filterByRefundubility & filterByCancellation & filterByStartDate & filterByEndDate;

            var totalCount = await _collection.CountDocumentsAsync(filter, cancellationToken: cancellationToken);

            for (var i = 0; i < totalCount; i += batchSize)
            {
                var bookings = await _collection
                    .Find(filter)
                    .Skip(i)
                    .Limit(batchSize)
                    .ToListAsync(cancellationToken);

                yield return bookings;
            }
        }

        private static FilterDefinition<Models.Booking> BuildSecurityFilter(IEnumerable<IEnumerable<FilterCondition>> securityFilters)
        {
            if (securityFilters == null) throw new ArgumentException($"{nameof(securityFilters)} cannot be null");

            return securityFilters.Any()
                ? MongoDbFilterConverter.GetFilterDefinitions<Models.Booking>(securityFilters)
                : FilterDefinition<Models.Booking>.Empty;
        }

        private async Task<IEnumerable<Models.Booking>> ReadFilteredAsync(FilterDefinition<Models.Booking> filter,
            FindOptions<Models.Booking, Models.Booking> findOptions = null, CancellationToken cancellationToken = default)
            => await SafeReadAsync((await _collection.FindAsync(filter, findOptions, cancellationToken)).ToListAsync(cancellationToken));
    }
}