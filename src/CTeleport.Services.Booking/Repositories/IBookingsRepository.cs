using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Search.Shared.Models;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Repositories
{
    public interface IBookingsRepository
    {
        Task AddAsync(Models.Booking booking);
        Task<Models.Booking> GetAsync(string id);
        Task<IEnumerable<Models.Booking>> GetByIdsAsync(IEnumerable<string> ids);

        Task SetStateAsync(string id, BookingState state);

        /// <summary>
        /// Retrieves all bookings from the bookings collection for public usage
        /// </summary>
        /// <param name="securityFilters">Security filter to apply. Normally this parameter must be set unless it is system service call.</param>
        /// <param name="startFrom">Optional argument to retrieve bookings created after the specific datetime.</param>
        /// <param name="endAt">Optional argument to retrieve bookings created before and including the specific datetime.</param>
        Task<IEnumerable<Models.Booking>> GetAllScopedAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters = null,
            DateTime? startFrom = null, DateTime? endAt = null);

        /// <summary>
        /// Retrieves all bookings from the bookings collection for internal usage
        /// Do not use for public endpoints 
        /// </summary>
        /// <param name="startFrom">Optional argument to retrieve bookings created after the specific datetime.</param>
        /// <param name="endAt">Optional argument to retrieve bookings created before and including the specific datetime.</param>
        /// <param name="tenantId">Optional argument to retrieve the specified tenant bookings</param>
        Task<IEnumerable<Models.Booking>> GetAllAsync(DateTime startFrom, DateTime endAt, string tenantId = null);

        /// <summary>
        /// Retrieves all bookings updated since a date
        Task<IEnumerable<Models.Booking>> GetUpdatedSinceBatchAsync(int limit, DateTime sinceUpdatedAt, DateTime sinceCreatedAt);

        Task<IEnumerable<Models.Booking>> GetAllForTenantAsync(string tenantId);
        Task<IEnumerable<Models.Booking>> GetRecentlyDepartedAsync(int minutes);
        Task<IEnumerable<Models.Booking>> GetByPassengerAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, string firstName, string lastName);
        Task<IEnumerable<Models.Booking>> GetBySearchIdAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, string searchId);
        Task<IEnumerable<Models.Booking>> GetByUserIdAsync(string userId);
        Task<IEnumerable<Models.Booking>> GetActiveForCrewChangeAsync(string vesselName, string crewChangeDate);
        Task<IEnumerable<Vessel>> GetBookedVesselsAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters, string vesselName, int maxCount);
        Task SetAsNoShowAsync(string bookingId);
        Task UpdateComment(string bookingId, string comment, User user);
        Task UpdatePassengerDetails(string bookingId, MutablePassengerDetails passengerDetails);
        Task<List<Models.Booking>> GetBookingsInStateAsync(BookingState state);
        Task SetBookingCancelledByAsync(string bookingId, User user);
        Task SetAsDeclinedByAsync(string bookingId, User rejectedBy, string reason);
        Task SetAsApprovedByAsync(string bookingId, params User[] approvedBy);
        Task UpdateVesselAsync(string bookingId, VesselDetails vessel);
        Task<Models.Booking> GetByFlightSolutionAsync(string flightSolutionId);
        Task<Models.Booking> GetTenantFirstBookingAsync(string tenant);
        Task UpdateLegsAsync(string id, ICollection<Leg> legs);

        Task<IEnumerable<Models.Booking>> GetBatchScopedAsync(IEnumerable<IEnumerable<FilterCondition>> securityFilters,
            BookingsPagingToken startFrom, BookingsPagingToken endAt, int batchSize);
        Task UpdatePriceAsync(string bookingId, BookingPrice bookingPrice);
        
        Task<long> CountAsync(IEnumerable<IEnumerable<FilterCondition>> filters, DateTime? createdAfter,
            DateTime? createdUntil, CancellationToken cancellationToken);
        
        Task<IEnumerable<Models.Booking>> GetPagedAsync(IEnumerable<IEnumerable<FilterCondition>> filters, int limit,
            int offset, DateTime? createdAfter, DateTime? createdUntil, CancellationToken cancellationToken);

        IAsyncEnumerable<IReadOnlyCollection<Models.Booking>> GetRefundubleBookingsByBatchesAsync(
            DateTime? startDate,
            DateTime? endDate,
            int batchSize = 10,
            CancellationToken cancellationToken = default);

        Task<IEnumerable<Models.Booking>> GetActiveByPassengerInDatesRangeAsync(
            IEnumerable<IEnumerable<FilterCondition>> securityFilters, string firstName, string lastName,
            DateTime startFrom, DateTime endAt);
    }
}
