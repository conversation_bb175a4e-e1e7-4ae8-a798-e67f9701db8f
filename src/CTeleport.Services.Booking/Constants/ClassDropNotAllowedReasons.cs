namespace CTeleport.Services.Booking.Constants;

public static class ClassDropNotPossibleReasons
{
    public static string RePriceError = "Failed to run class drop re-price due to error: {0}";
    public static string FareIsNull = "Reservation Fare can not be null";
    public static string InvoicedReservation = "Cannot run class drop for reservation with invoices associated";
    public static string ReservationHasExtraServices = "Reservation has extra services";
    public static string ScheduledDepartureIn24Hours = "Scheduled departure for reservation in 24 hours";
    public static string TicketClassDropCarrierRestrictions = "Ticket class drop is not allowed for reservation due to carrier {0} restrictions";
    public static string TicketReissued = "Reservation has been reissued before";
    public static string ReservationHasNoOpenTickets = "Reservation has no open tickets";
    public static string ReservationHasMoreThenOneOpenTicket = "Reservation has more than 1 open tickets";
    public static string TicketNotFullyRefundable = "Ticket is not fully refundable and cannot be voided for reservation";
    public static string TicketCanNotBeVoided =  "Ticket cannot be voided for reservation. Airline {0} blocked refunds";
    public static string TicketCheckedIn = "Ticket is checked in for reservation";
    public static string CanNotDetermineTicketState = "Cannot determine ticket state for reservation";
    public static string PaymentMethodIsCreditCard = "Payment method is credit card";
    public static string OriginalBookingCodesIdenticalToNewCodes = "Original booking codes are identical to new booking codes";
    public static string OriginalFareIdenticalToNewFare = "Original fare is identical to new fare";
    public static string NewFareIsNotLower = "New fare is not lower then original";
    public static string CabinClassesAreNotEqual = "Cabin classes are not equal";
    public static string BaggageAllowancesNotSame = "Baggage allowances not same";
    public static string FareRulesNotSame = "Fare rules not same";
    public static string FundingSourceIsDisabled =  "Funding source is disabled for carrier";
}