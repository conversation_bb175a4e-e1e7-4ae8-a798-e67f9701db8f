<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\CTeleport.Services.Airlines\CTeleport.Services.Airlines.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Domain\CTeleport.Services.Booking.Domain.csproj" />
    <ProjectReference Include="..\CTeleport.Services.ChangeExecution\CTeleport.Services.ChangeExecution.csproj" />
    <ProjectReference Include="..\CTeleport.Services.CheckFare\CTeleport.Services.CheckFare.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Co2Emissions\CTeleport.Services.Co2Emissions.csproj" />
    <ProjectReference Include="..\CTeleport.Services.CustomFields\CTeleport.Services.CustomFields.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Excel.Shared\CTeleport.Services.Excel.Shared.csproj" />
    <ProjectReference Include="..\CTeleport.Services.ExtraServiceManagement\CTeleport.Services.ExtraServiceManagement.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FareCacheService\CTeleport.Services.FareCache.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FareTerms\CTeleport.Services.FareTerms.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FlightStatus\CTeleport.Services.FlightStatus.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FrequentFlyer\CTeleport.Services.FrequentFlyer.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Galileo\CTeleport.Services.Galileo.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Price\CTeleport.Services.Price.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Providers\CTeleport.Services.Providers.csproj" />
    <ProjectReference Include="..\CTeleport.Services.SearchProxy\CTeleport.Services.SearchProxy.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Settings\CTeleport.Services.Settings.csproj" />
    <ProjectReference Include="..\CTeleport.Services.ApprovalQueueClient\CTeleport.Services.ApprovalQueueClient.csproj" />
    <ProjectReference Include="..\CTeleport.Services.VoidCalc\CTeleport.Services.VoidCalc.csproj" />
  </ItemGroup>
	<ItemGroup>
    <PackageReference Include="CTeleport.Authorization" Version="1.1.200" />
    <PackageReference Include="CTeleport.Common.V2.Messaging.CAP" Version="1.28.183-beta" />
    <PackageReference Include="CTeleport.Common.V2.Messaging.CAP.MongoDB" Version="1.28.183-beta" />
    <PackageReference Include="CTeleport.Common.V2.Redis" Version="1.1.39" Aliases="RedisV2" />
    <PackageReference Include="CTeleport.FeatureFlag.Resolver.FeatBit" Version="2025.1.6.15" />
    <PackageReference Include="CTeleport.Infrastructure.Metrics" Version="2025.3.28.62" />
    <PackageReference Include="CTeleport.Messages" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Messages.Abstractions" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Services.Booking.Aggregate.Messages" Version="2025.1.16.173" />
    <PackageReference Include="CTeleport.Services.Booking.Orchestrator.Client" Version="2024.11.14.145" />
    <PackageReference Include="CTeleport.Services.Booking.Shared" Version="1.1.124" />
    <PackageReference Include="CTeleport.Services.FareRules.Client" Version="2025.7.22.9" />
    <PackageReference Include="AutoMapper" Version="10.1.1" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.400.72" />
    <PackageReference Include="CTeleport.Services.Co2Emissions.Contracts" Version="2023.11.22.64-feature-rnd-951" />
    <PackageReference Include="CTeleport.Services.ExtraServiceManagement.Shared" Version="2025.4.29.1173" />
    <PackageReference Include="CTeleport.Services.TenantShared" Version="1.1.183" />
    <PackageReference Include="CTeleport.Services.Travelers.Contracts.Events" Version="1.0.4.91" />
    <PackageReference Include="DotNetCore.CAP.RabbitMQ" Version="8.3.2-rabbit-downgrade" />
    <PackageReference Include="Humanizer" Version="2.14.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageReference Include="NodaTime" Version="2.4.0" />
    <PackageReference Include="SnowflakeGenerator" Version="2.0.0" />
  </ItemGroup>
	<ItemGroup>
	  <Compile Remove="Repositories\BookingMetadataRepository.cs" />
	  <Compile Remove="Repositories\IBookingMetadataRepository.cs" />
	</ItemGroup>
</Project>
