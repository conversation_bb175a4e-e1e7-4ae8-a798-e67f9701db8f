using CTeleport.Services.Booking.Constants;
using CTeleport.Services.Booking.Services.Interfaces;

namespace CTeleport.Services.Booking.Services.ClassDrop;

public sealed class FareRulesEqualCheck : IClassDropCheck
{
    public bool IsPossible(ClassDropPossibilityRequest request, out string notPossibleReason)
    {
        if (request.FareRulesAreEquals)
        {
            notPossibleReason = null;
            return true;
        }

        notPossibleReason = ClassDropNotPossibleReasons.FareRulesNotSame;
        return false;
    }
}