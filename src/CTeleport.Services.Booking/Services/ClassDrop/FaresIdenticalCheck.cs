using CTeleport.Services.Booking.Constants;
using CTeleport.Services.Booking.Services.Interfaces;

namespace CTeleport.Services.Booking.Services.ClassDrop;

public sealed class FaresIdenticalCheck : IClassDropCheck
{
    public bool IsPossible(ClassDropPossibilityRequest request, out string notPossibleReason)
    {
        if (request.AreFaresIdentical)
        {
            notPossibleReason = ClassDropNotPossibleReasons.OriginalFareIdenticalToNewFare;
            return false;
        }

        notPossibleReason = null;
        return true;
    }
}