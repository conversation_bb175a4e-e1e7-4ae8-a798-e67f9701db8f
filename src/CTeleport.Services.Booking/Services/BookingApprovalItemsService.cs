using AutoMapper;
using CTeleport.Messages.Events;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Resolvers;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.ApprovalQueueClient;
using Serilog;
using Serilog.Context;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Authorization.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Models;
using CTeleport.Messages.Models.ApprovalFlow.Enums;
using CTeleport.Services.ApprovalQueueClient.Models;
using CTeleport.Services.Booking.Constants;
using OpenFeature;

namespace CTeleport.Services.Booking.Services
{
    public class BookingApprovalItemsService : IBookingApprovalItemsService
    {
        private readonly IProviderReservationInfoService _reservationInfoService;
        private readonly IApprovalQueueClient _approvalQueueClient;
        private readonly IBookingMetricsService _metricsService;
        private readonly IBookingService _bookingService;
        private readonly IServiceContext _serviceContext;
        private readonly IFeatureClient _featureClient;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;

        public BookingApprovalItemsService(
            IProviderReservationInfoService reservationInfoService,
            IApprovalQueueClient approvalQueueClient,
            IBookingMetricsService metricsService,
            IBookingService bookingService,
            IMapper mapper,
            ILogger logger, 
            IServiceContext serviceContext, IFeatureClient featureClient)
        {
            _reservationInfoService = reservationInfoService;
            _approvalQueueClient = approvalQueueClient;
            _metricsService = metricsService;
            _bookingService = bookingService;
            _mapper = mapper;
            _logger = logger;
            _serviceContext = serviceContext;
            _featureClient = featureClient;
        }

        public async Task<IEnumerable<ApprovalItem>> GetApprovalItemsAsync(string authToken, string approverId)
        {
            return await _featureClient.GetBooleanValueAsync(FeatureFlags.EnableNewApprovalFlow, true)
                ? await GetApprovalBundlesAsync(authToken)
                : await GetOldApprovalItemsAsync(authToken, approverId);
        }

        private async Task<IEnumerable<ApprovalItem>> GetApprovalBundlesAsync(string authToken)
        {
            var approvalQueueBundles = (await _approvalQueueClient.GetPendingFlightApprovalBundlesAsync(authToken))
                .ApprovalBundles.ToList();

            var bookingIds = approvalQueueBundles.Select(x => x.ReferenceId).ToArray();
            var approvalRequiredBookings = (await _bookingService.GetBookingsAsync(bookingIds))
                .Where(x => x.State == BookingState.ApprovalRequired).ToList();
            
           return approvalQueueBundles.Join(
                approvalRequiredBookings, 
                x => x.ReferenceId, 
                x => x.Id, (pendingApprovalBundle, booking) => _mapper.Map(pendingApprovalBundle, _mapper.Map<ApprovalItem>(_mapper.Map<BaseBookingDto>(booking))));
        }

        private async Task<IEnumerable<ApprovalItem>> GetOldApprovalItemsAsync(string authToken, string approverId)
        {
            var approvalQueueItems = (await _approvalQueueClient.GetPendingApprovalQueueItemsAsync(authToken, approverId)).ToList();
            
            var bookingIds = approvalQueueItems.Select(x => x.BookingId).ToArray();
            var approvalRequiredBookings = (await _bookingService.GetBookingsAsync(bookingIds))
                .Where(x => x.State == BookingState.ApprovalRequired).ToList();
            
            return approvalQueueItems.Join(
                approvalRequiredBookings, 
                x => x.BookingId, 
                x => x.Id, (approvalQueueItem, booking) => _mapper.Map(approvalQueueItem, _mapper.Map<ApprovalItem>(_mapper.Map<BaseBookingDto>(booking))));
        }

        public async Task<Models.ApprovalQueueItem> GetApprovalQueueItemAsync(string authToken, string bookingId)
        {
            var approvalQueueItem = _mapper.Map<Models.ApprovalQueueItem>(await _approvalQueueClient.GetApprovalQueueItemAsync(authToken, bookingId));

            approvalQueueItem.RepricingRequired = await GetApprovalQueueItemRepricingRequiredAsync(bookingId);

            return approvalQueueItem;
        }

        public async Task<bool> GetApprovalQueueItemRepricingRequiredAsync(string bookingId, bool checkOnlyForRealPnr = false)
        {
            LogContext.PushProperty("BookingId", bookingId);

            _logger.Information("GetApprovalQueueItemRepricingRequiredAsync");

            var booking = await _bookingService.GetBaseBookingAsync(bookingId);
            var reservations = await _bookingService.GetBookingReservationsAsync(bookingId);
            var activeReservations = reservations
                .Where(r => r.State == ReservationState.Active)
                .ToArray();

            bool repricingRequired = checkOnlyForRealPnr
                ? false
                : RepricingRequiredResolver.Resolve(activeReservations, booking);

            if ((!repricingRequired && activeReservations.All(r => !r.IsVirtual)) || checkOnlyForRealPnr)
            {
                var nonVirtualReservations = activeReservations.Where(r => !r.IsVirtual).ToArray();

                _logger.Information("Iterate non-virtual Reservations");
                foreach (var reservation in nonVirtualReservations)
                {
                    _logger.Information("Start ProviderRetrieveReservation");
                    var request = new ProviderRetrieveReservationRequest
                    {
                        Locators = reservation.Locators,
                        Source = reservation.Source
                    };

                    _logger.Information("ProviderRetrieveReservationRequest");

                    var providerReservation = await _reservationInfoService.GetReservationAsync(request);

                    _metricsService.IncrementProviderValidationRequestCounter(
                        reservation?.TenantId ?? string.Empty, reservation?.Source ?? string.Empty, providerReservation?.HasError ?? false);

                    _logger.Information("ProviderRetrieveReservationResponse");

                    if (providerReservation == null || providerReservation.FlightReservation == null)
                    {
                        _logger.Error("ProviderReservation.FlightReservation is null");
                    }

                    if (providerReservation?.FlightReservation?.Fare?.IsValid != true)
                    {
                        repricingRequired = true;
                    }
                    else
                    {
                        foreach (var expectedSegment in reservation.LegSegments.SelectMany(s => s))
                        {
                            var providerSegment = providerReservation.FlightReservation.Segments.FirstOrDefault(s =>
                                   s.Carrier == expectedSegment.Carrier && s.FlightNumber == expectedSegment.FlightNumber);

                            repricingRequired = providerSegment?.Status != TravelportAirSegmentStatusCode.Confirmed;

                            if (repricingRequired)
                            {
                                break;
                            }
                        }
                    }

                    if (repricingRequired)
                    {
                        break;
                    }

                }
            }
            return repricingRequired;
        }

        public async Task<IAuthenticatedCommand> BuildApproveCommandAsync(string authToken, string bookingId,
            string flightSolutionId,
            string comment)
        {
            var booking = await _bookingService.GetBookingAsync(bookingId);

            //New flow call
            await _approvalQueueClient.ApproveBundleAsync(authToken, bookingId, new ApproveRejectBundleRequest
            {
                ApplicableFor = ApprovalApplicableFor.Flight,
                Comment = comment
            });
            
            // Old flow command 
            return new ApproveBooking 
            {
                BookingId = bookingId,
                BookingState = _mapper.Map<CTeleport.Messages.Commands.Enums.BookingState>(booking.State),
                Comment = comment,
                FlightSolutionId = flightSolutionId,
                User = new User
                {
                    Id = _serviceContext.User.Id,
                    Name = _serviceContext.User.Name,
                    Email = _serviceContext.User.Email,
                    TenantId = _serviceContext.TenantId
                }
            };
        }

        public async Task<IAuthenticatedCommand> BuildRejectCommandAsync(string authToken, string bookingId, string comment)
        {
            //New flow call
            await _approvalQueueClient.RejectBundleAsync(authToken, bookingId, new ApproveRejectBundleRequest
            {
                ApplicableFor = ApprovalApplicableFor.Flight,
                Comment = comment
            });

            // Old flow command 
            return new RejectBooking 
            {
                BookingId = bookingId,
                Comment = comment,
                User = new User
                {
                    Id = _serviceContext.User.Id,
                    Name = _serviceContext.User.Name,
                    Email = _serviceContext.User.Email,
                    TenantId = _serviceContext.TenantId
                }
            };
        }

        //TODO [pd]
        private class TravelportAirSegmentStatusCode
        {
            public const string Confirmed = "HK";
        }
    }

    public class ReservationBroken : IAuthenticatedEvent
    {
        public ReservationBroken(string userId, string requestId, string bookingId, string reservationId)
        {
            UserId = userId;
            RequestId = requestId;
            BookingId = bookingId;
            ReservationId = reservationId;
        }
        public string ReservationId { get; }
        public string BookingId { get; }
        public string RequestId { get; }
        public string UserId { get; }
    }
}