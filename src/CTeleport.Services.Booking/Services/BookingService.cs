using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Helpers;
using CTeleport.Common.Authorization.Services;
using CTeleport.Messages.Commands.Models;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Configuration;
using CTeleport.Services.Booking.Constants;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.ExtraServiceManagement.Clients;
using CTeleport.Services.FareTerms;
using CTeleport.Services.FareTerms.Shared.Dto;
using CTeleport.Services.FareTerms.Shared.Requests;
using CTeleport.Services.FareTerms.Shared.Responses;
using CTeleport.Services.FrequentFlyer.Service;
using CTeleport.Services.PlacesApiClient;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using OpenFeature;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Mappers;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.ExtraServiceManagement.Shared.Enums;
using CTeleport.Services.Helpers;
using CTeleport.Services.SearchProxy.Client;
using CTeleport.Services.SearchProxy.Models;
using CTeleport.Services.Settings.Clients;
using BillingPaymentMethod = CTeleport.Services.Billing.Clients.Dto.PaymentMethod;
using CancellationTermsDto = CTeleport.Services.FareTerms.Shared.Dto.CancellationTermsDto;
using IBillingClient = CTeleport.Services.Billing.Clients.IBillingClient;
using ProviderFlightReservation = CTeleport.Services.Booking.Shared.Models.ProviderFlightReservation;
using Ticket = CTeleport.Services.Booking.Models.Ticket;
using TicketPrice = CTeleport.Services.Booking.Models.TicketPrice;
using User = CTeleport.Services.Search.Shared.Models.User;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using CollectionExtensions = System.Collections.Generic.CollectionExtensions;
using FundingSource = CTeleport.Messages.Models.FundingSource;
using PassengerDetails = CTeleport.Messages.Commands.Models.PassengerDetails;

namespace CTeleport.Services.Booking.Services
{
    internal class BookingService : IBookingService
    {
        private readonly IServiceContext _context;
        private readonly IAuthService _authService;
        private readonly IPlacesClient _placesClient;

        private readonly ITicketService _ticketService;

        private readonly IBookingsRepository _bookingsRepository;
        private readonly IReservationsRepository _reservationsRepository;

        private readonly IMapper _mapper;

        private readonly FareRefreshOptions _options;
        private readonly ISingleNameService _singleNameService;
        private readonly ILogger _logger;
        private readonly IBillingClient _billingClient;
        private readonly IFareTermsClient _fareTermsService;
        private readonly ITicketUsageService _ticketUsageService;
        private readonly ICustomFieldsDisplayService _customFieldsDisplayService;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IFrequentFlyerService _frequentFlyerService;
        private readonly IExtraServiceManagementClient _extraServiceManagementClient;
        private readonly ISearchClient _searchClient;
        private readonly IFareChangeService _fareChangeService;
        private readonly ITenantManagementServiceClient _tenantManagementService;

        private readonly IProviderTicketStateService _providerTicketStateService;
        private readonly DateTimeProvider _dateTimeProvider;
        private readonly IFareRulesEnrichmentService _fareRulesEnrichmentService;
        private readonly IFeatureClient _featureClient;

        public BookingService(IServiceContext context, ITicketService ticketService,
            IBookingsRepository bookingsRepository, IReservationsRepository reservationsRepository, IMapper mapper,
            FareRefreshOptions options, ISingleNameService singleNameService,
            ILogger logger, IBillingClient billingClient, IAuthService authService,
            IFareTermsClient fareTermsService, ITicketUsageService ticketUsageService,
            ICustomFieldsDisplayService customFieldsDisplayService, IFrequentFlyerService frequentFlyerService,
            IBookingSagaService bookingSagaService, IExtraServiceManagementClient extraServiceManagementClient,
            IPlacesClient placesClient,
            IFareChangeService fareChangeService,
            ISearchClient searchClient, IProviderTicketStateService providerTicketStateService,
            ITenantManagementServiceClient tenantManagementService, DateTimeProvider dateTimeProvider,
            IFareRulesEnrichmentService fareRulesEnrichmentService, IFeatureClient featureClient)
        {
            _context = context;
            _ticketService = ticketService;
            _bookingsRepository = bookingsRepository;
            _reservationsRepository = reservationsRepository;

            _mapper = mapper;

            _options = options;
            _singleNameService = singleNameService;
            _logger = logger;
            _billingClient = billingClient;
            _authService = authService;
            _fareTermsService = fareTermsService;
            _ticketUsageService = ticketUsageService;
            _customFieldsDisplayService = customFieldsDisplayService;
            _frequentFlyerService = frequentFlyerService;
            _bookingSagaService = bookingSagaService;
            _extraServiceManagementClient = extraServiceManagementClient;
            _placesClient = placesClient;
            _fareChangeService = fareChangeService;
            _searchClient = searchClient;
            _providerTicketStateService = providerTicketStateService;
            _tenantManagementService = tenantManagementService;
            _dateTimeProvider = dateTimeProvider;
            _fareRulesEnrichmentService = fareRulesEnrichmentService;
            _featureClient = featureClient;
        }

        public async Task RemoveRejectedReservation(string reservationId)
        {
            await _reservationsRepository.RemoveNewAsync(reservationId);
        }

        public Task<Reservation> UpdatePendingReservation(Models.Reservation reservation,
            ProviderFlightReservation providerReservation, BookingSaga saga) =>
            SaveCompletedReservation(reservation, providerReservation, saga, ReservationState.New);

        public async Task<Reservation> SaveCompletedReservation(Models.Reservation reservation,
            ProviderFlightReservation providerReservation, BookingSaga saga,
            ReservationState state = ReservationState.Active)
        {
            reservation = ReservationMapper.Map(reservation, providerReservation, saga, _logger);

            reservation.State = state;

            var reservationForStorage = await PrepareReservationForStorageAsync(reservation);
            await _reservationsRepository.ReplaceReservationAsync(reservationForStorage);
            return reservation;
        }

        public async Task<Reservation> BuildDraftReservation(CreateReservationParams reservationParams)
        {
            var passenger = await CreatePassengerFromReservationParams(reservationParams);

            var draftReservation = _mapper.Map<Reservation>(reservationParams);
            draftReservation.Locators = new Dictionary<string, string>();
            draftReservation.Tickets = Array.Empty<Ticket>();
            draftReservation.Changes = Array.Empty<TicketChanges>();
            draftReservation.FareChanges = Array.Empty<FareChange>();
            draftReservation.Passenger = _mapper.Map<Shared.Models.ReservationPassenger>(passenger);
            draftReservation.CanCancel = true;

            if (reservationParams.FaresCache != null)
            {
                if (draftReservation.Fare == null)
                {
                    draftReservation.Fare = new Shared.Models.Fare();
                }

                draftReservation.FareRules = reservationParams.FaresCache.FareRules;
                draftReservation.FareRulesIds = ReservationHelper.ProcessFareRuleSections(reservationParams.FaresCache.FareRules);
                draftReservation.FareRuleCat16Ids = ReservationHelper.GetPenaltyFareRulesIds(reservationParams.FaresCache.FareRules);

                draftReservation.CancellationTimeline = reservationParams.FaresCache.CancellationTimeline;
                draftReservation.ChangeTimeline = reservationParams.FaresCache.ChangeTimeline;
                draftReservation.PartiallyUsedChangeTimeline = reservationParams.FaresCache.PartiallyUsedChangeTimeline;
                draftReservation.Fare.PlatingCarrier = reservationParams.PlatingCarrier;
                draftReservation.Fare.Cancellations = reservationParams.FaresCache.Cancellations;
                draftReservation.Fare.Changes = reservationParams.FaresCache.Changes;
            }

            draftReservation.Metadata = reservationParams.ReservationMetadata;
            draftReservation.SearchJobMetadata = reservationParams.SearchJobMetadata;

            draftReservation.Price = reservationParams.ReservationPrice;

            draftReservation.CreatedAt = DateTime.UtcNow;

            draftReservation.FrequentFlyerNumbers =
                _mapper.Map<List<Booking.Shared.Models.FrequentFlyerNumber>>(reservationParams.FrequentFlyerNumbers);
            draftReservation.Ancillaries = reservationParams.Ancillaries;
            
            draftReservation.FundingSource = reservationParams.FundingSource?.Id;

            return draftReservation;
        }

        public async Task<Reservation> CreateReservation(Reservation reservation)
        {
            var reservationForStorage = await PrepareReservationForStorageAsync(reservation);            
            await _reservationsRepository.AddAsync(reservationForStorage);

            return reservation;
        }

        public async Task<Reservation> UpdateReservationAsync(Reservation reservation)
        {
            var reservationForStorage = await PrepareReservationForStorageAsync(reservation);
            await _reservationsRepository.ReplaceReservationAsync(reservationForStorage);

            return reservation;
        }
        
        private async Task<Reservation> PrepareReservationForStorageAsync(Reservation reservation)
        {
            var shouldExcludeFareRules = await _featureClient
                .GetBooleanValueAsync(FeatureFlags.ExcludeFareRulesFromReservationStorage, false);
            
            _logger.Information("Feature flag {FeatureFlag} value to save FareRules in reservation: {ShouldExcludeFareRules}", 
                FeatureFlags.ExcludeFareRulesFromReservationStorage, shouldExcludeFareRules);

            if (!shouldExcludeFareRules) 
                return reservation;

            if (reservation.FareRules != null)
            {
                _logger.Information("Truncating FareRules from reservation {ReservationId}. Covered by FareRuledIds property", reservation.Id);
            }
            
            // exclude
            _logger.Information("Removing FareRules from reservation {ReservationId}", reservation.Id);
            var reservationForStorage = reservation.Clone();
            reservationForStorage.FareRules = null;
            
            return reservationForStorage;
        }

        public async Task<Reservation> CreateVirtualReservation(string providerKey, Reservation draftReservation,
            FlightSolution flightSolution, CreateReservationParams reservationParams)
        {
            //reservation.Fare.GuaranteeDate = ??
            draftReservation.Fare.Taxes = flightSolution.Price.Taxes;
            //reservation.Fare.Base = ??
            //reservation.Fare.ApproximateBase =??
            draftReservation.Fare.Net = draftReservation.Price.Net;
            //reservation.Fare.ExemptedTaxes = ??
            //reservation.Fare.Surcharges = ??
            draftReservation.Fare.Currency = draftReservation.Price.Currency;

            draftReservation.State = ReservationState.Active;

            await _reservationsRepository.AddAsync(draftReservation);

            return draftReservation;
        }

        private async Task<Models.PassengerDetails> CreatePassengerFromReservationParams(
            CreateReservationParams reservationParams)
        {
            var passenger = reservationParams.Passenger.Clone();
            if (reservationParams.Passenger.SingleNameOnly)
            {
                await _singleNameService.SetSingleNameAsync(passenger, reservationParams.PlatingCarrier);
            }

            return passenger;
        }

        public async Task<ProviderCreateReservation> CreateReservationCommand(CreateReservationParams reservationParams)
        {
            var passenger = await CreatePassengerFromReservationParams(reservationParams);

            var command = new ProviderCreateReservation
            {
                ReservationId = reservationParams.ReservationId,
                TraceId = reservationParams.SearchJobId,
                ProviderKey = reservationParams.ProviderKey,
                Passenger = _mapper.Map<PassengerDetails>(passenger),
                ReservationMetadata = new ReservationMetadata
                {
                    AgentMetadata = reservationParams.AgentMetadata == null
                        ? null
                        : new AgentMetadata
                        {
                            AccountCode = reservationParams.AgentMetadata.AccountCode,
                            AccountName = reservationParams.AgentMetadata.AccountName,
                            NotificationEmail = reservationParams.AgentMetadata.NotificationEmail
                        },
                    AgencyMetadata = new AgencyMetadata
                    {
                        IATA = reservationParams.TargetSource.AgencyIata,
                        Name = reservationParams.TargetSource.AgencyName,
                        Phone = reservationParams.TargetSource.AgencyPhone
                    },
                    VesselMetadata = new VesselMetadata
                    {
                        Name = reservationParams.Metadata.VesselName,
                        Flag = reservationParams.Metadata.VesselFlag
                    },
                    PriceMetadata = new PriceMetadata
                    {
                        Currency = reservationParams.Price.Currency,
                        QuotedPrice = reservationParams.Price.QuotedPrice,
                        MarkupCurrency = reservationParams.Price.MarkupCurrency,
                        Markup = reservationParams.Price.Markup
                    },
                    CorporateCodes = reservationParams.CorporateCodes,
                    RemarksMetadata = reservationParams.RemarksMetadata,
                    FrequentFlyerNumbers =
                        _mapper.Map<List<Messages.Commands.Models.FrequentFlyerNumber>>(reservationParams
                            .FrequentFlyerNumbers)
                },
                ExemptLiTax = reservationParams.ExemptLiTax ?? false,
                CreatedBy = new Messages.Commands.Models.User
                {
                    Id = reservationParams.CreatedBy.Id,
                    Name = reservationParams.CreatedBy.Name,
                    Email = reservationParams.CreatedBy.Email
                },
                RequestId = reservationParams.RequestId,
                FormOfPayment = new FormOfPayment
                {
                    Type = reservationParams.TargetSource.DefaultPaymentMethod
                },
                FundingSource = reservationParams.FundingSource is null ? null : new FundingSource
                {
                    Id = reservationParams.FundingSource.Id,
                    Type = reservationParams.FundingSource.Type
                },
                Ancillaries = reservationParams.Ancillaries?.Select(x => new Messages.Commands.Models.Ancillary
                {
                    Key = x.Key
                }).ToList()
            };

            return command;
        }

        public async Task AddBookingAsync(Models.Booking booking)
        {
            await _bookingsRepository.AddAsync(booking);
        }

        public async Task SetPaymentRequiredAsync(string reservationId, bool paymentRequired)
            => await _reservationsRepository.SetPaymentRequiredAsync(reservationId, paymentRequired);

        public async Task SetTicketingTimeAsync(string reservationId, DateTime? ticketingTime)
            => await _reservationsRepository.SetTicketingTimeAsync(reservationId, ticketingTime);

        public async Task SetRefreshFareTimeAsync(string reservationId, DateTime refreshFareAt,
            bool adjustToOfficeHours = true)
        {
            var dt = refreshFareAt;

            if (dt.Kind != DateTimeKind.Utc)
            {
                throw new ArgumentException($"{nameof(refreshFareAt)} must be of king DateTimeKind.Utc");
            }

            if (adjustToOfficeHours)
            {
                if (dt.Hour < _options.OfficeHours[0])
                {
                    // Reschedule to 30 after after office opening time
                    dt = new DateTime(dt.Year, dt.Month, dt.Day, _options.OfficeHours[0], 0, 0, DateTimeKind.Utc)
                        .AddMinutes(30);
                }
                else if (dt.Hour >= _options.OfficeHours[1])
                {
                    // Reschedule to 30 before office closing time
                    dt = new DateTime(dt.Year, dt.Month, dt.Day, _options.OfficeHours[1], 0, 0, DateTimeKind.Utc)
                        .AddMinutes(-30);
                }
            }

            await _reservationsRepository.SetRefreshFareTimeAsync(reservationId, dt);
        }

        // TODO: check calling methods
        public async Task<Models.Booking> GetBookingAsync(string id)
        {
            var booking = await _bookingsRepository.GetAsync(id);

            if (booking == null)
            {
                return null;
            }

            // TODO: consider to inject context into bookings query above
            await ValidateAccessAsync(booking);

            return booking;
        }

        public async Task<IEnumerable<Models.Booking>> GetRecentlyDepartedBookingsAsync(int minutes)
            => await _bookingsRepository.GetRecentlyDepartedAsync(minutes);

        public async Task<IEnumerable<Reservation>> GetBookingReservationsAsync(string id)
            => await _reservationsRepository.GetByBookingIdAsync(id);

        public async Task<Reservation> GetReservationAsync(string id)
        {
            var reservation = await _reservationsRepository.GetAsync(id);
            
            return reservation;
        }

        public async Task<Reservation> GetEnrichedReservationAsync(string id)
        {
            var reservation = await GetReservationAsync(id);
            
            if (reservation == null)
            {
                return null;
            }
            
            await _fareRulesEnrichmentService.EnrichFareRulesAsync([reservation]);
            
            return reservation;
        }

        public async Task<Reservation> GetReservationByTicketNumberAsync(string ticketNumber)
        {
            var reservation = await _reservationsRepository.GetByTicketNumberAsync(ticketNumber);  
            
            if (reservation == null)
            {
                return null;
            }
            
            await _fareRulesEnrichmentService.EnrichFareRulesAsync([reservation]);
            
            return reservation;
        }

        public async Task<Reservation> GetReservationByLocatorAsync(string locator, string lastname = null)
        {
            var reservations = (await _reservationsRepository.GetByProviderLocatorAsync(locator)).ToList();

            await _fareRulesEnrichmentService.EnrichFareRulesAsync(reservations);
            
            if (String.IsNullOrEmpty(lastname) && reservations.Count != 0)
            {
                return reservations.OrderByDescending(r => r.CreatedAt).First();
            }

            foreach (var reservation in reservations)
            {
                if (string.Equals(reservation.Passenger.LastName, lastname, StringComparison.CurrentCultureIgnoreCase))
                {
                    return reservation;
                }
            }

            return null;
        }

        public async Task<BaseBookingDto> GetByReservationId(string id)
        {
            var reservation = await _reservationsRepository.GetAsync(id);
            var booking = await _bookingsRepository.GetAsync(reservation.BookingId);

            if (booking == null)
            {
                return null;
            }

            var mappedBooking = _mapper.Map<CompleteBookingDto>(booking);
            return _mapper.Map(new List<Reservation> { reservation }, mappedBooking);
        }

        public async Task<BaseBookingDto> GetBaseBookingAsync(string id)
        {
            var booking = await _bookingsRepository.GetAsync(id);

            if (booking == null)
            {
                return null;
            }

            // TODO: consider to inject context into bookings query above
            await ValidateAccessAsync(booking);

            return _mapper.Map<BaseBookingDto>(booking);
        }

        public async Task<IEnumerable<BaseBookingDto>> GetDuplicateBookingsBookingAsync(IReadOnlyCollection<IReadOnlyCollection<FlightSegment>> segments,
            string firstName, string lastName, string departure)
        {
            const int daysToCheck = 7;

            if (string.IsNullOrWhiteSpace(firstName))
            {
                firstName = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(lastName))
            {
                lastName = string.Empty;
            }

            var departureDateTime = DateTime.Parse(departure, CultureInfo.InvariantCulture);
            var checkFrom = departureDateTime.AddDays(-daysToCheck);
            var checkTo = departureDateTime.AddDays(daysToCheck);

            var filters = await _authService.GetReadValidationFiltersAsync();
            var bookings =
                await _bookingsRepository.GetActiveByPassengerInDatesRangeAsync(filters, firstName, lastName,
                    checkFrom, checkTo);

            var result = bookings.Where(b => IsDuplicate(b, segments));
            return _mapper.Map<IEnumerable<BaseBookingDto>>(result);
        }

        private bool IsDuplicate(Models.Booking booking, IReadOnlyCollection<IReadOnlyCollection<FlightSegment>> segments)
        {
            var bookingLegs = booking.Legs.OrderBy(l => l.DepartureUtc).ToList();
            var firstBookingLeg = bookingLegs.First();
            var lastBookingLeg = bookingLegs.Last();
            var bookingOrigin = firstBookingLeg.Origin;
            var bookingDestination = lastBookingLeg.Destination;
            var bookingDepartureDateTime = firstBookingLeg.DepartureUtc.ToDateTimeUtc();

            //If booking already arrived - it's not a duplicate
            var bookingArrivalDateTime = lastBookingLeg.ArrivalUtc.ToDateTimeUtc();
            if (bookingArrivalDateTime < _dateTimeProvider.UtcNow)
                return false;

            var flightSegments = segments.OrderBy(s => s.Min(s => s.DepartureTimestampUtc)).ToList();
            var firstSegment = flightSegments.First().First();
            var lastSegment = flightSegments.Last().Last();
            var flightSolutionDeparture = firstSegment.DepartureTimestampUtc.ToDateTimeUtc();
            var flightSolutionArrival = lastSegment.ArrivalTimestampUtc.ToDateTimeUtc();
            var flightSolutionOrigin = firstSegment.Origin;
            var flightSolutionDestination = lastSegment.Destination;

            //We try to book next part of the same trip - new flight solution departs where previous one arrives
            if (bookingDestination == flightSolutionOrigin && (flightSolutionDeparture - bookingArrivalDateTime).Hours >= 1)
                return false;

            //We try to book previous part of the same trip - new flight solution arrives where existing booking departs
            if (bookingOrigin == flightSolutionDestination && (bookingDepartureDateTime - flightSolutionArrival).Hours >= 1)
                return false;
            
            var deltaDays = Math.Abs((bookingDepartureDateTime - flightSolutionDeparture).TotalDays);

            //any flight within two days is considered a duplicate if not a part on trip
            if (deltaDays <= 2)
                return true;
            
            //When difference between new fs and existing booking is more than 2 days and less than a week
            //check only fully equal legs
            var legsToCheck = bookingLegs.Select(l => (l.Origin, l.Destination))
                .Concat([(Origin: bookingOrigin, Destination: bookingDestination)])
                .Distinct()
                .ToList();
            
            var flightLegsToCheck = segments.Select(l => (l.First().Origin, l.Last().Destination))
                .Concat([(Origin: segments.First().First().Origin, Destination: segments.Last().Last().Destination)])
                .Distinct()
                .ToList();

            foreach (var (legOrigin, legDestination) in legsToCheck)
            {
                if (flightLegsToCheck.Any(leg => legOrigin == leg.Origin && legDestination == leg.Destination))
                {
                    return true;
                }
            }

            return false;
        }

        public async Task<BookingSagaDto> GetBookingSagaAsync(string id)
        {
            var bookingSaga = await _bookingSagaService.GetAsync(id);

            if (bookingSaga == null)
            {
                return null;
            }

            return _mapper.Map<BookingSagaDto>(bookingSaga);
        }

        public async Task<CompleteBookingDto> GetCompleteBookingAsync(string id)
        {
            var booking = await _bookingsRepository.GetAsync(id);
            if (booking == null)
            {
                return null;
            }

            // TODO: consider to inject context into bookings query above
            await ValidateAccessAsync(booking);

            var reservations = (await GetBookingReservationsAsync(id))
                .OrderBy(r => r.DepartureAt).ToList();

            // Enrich reservations with fare rules for complete booking details
            await _fareRulesEnrichmentService.EnrichFareRulesAsync(reservations);

            var response = _mapper.Map<CompleteBookingDto>(booking);
            _mapper.Map(reservations, response, opts => opts.Items["LegsTotal"] = booking.Legs.Count);
            response.Reservations = _mapper.Map<IList<BaseReservationDto>>(reservations);

            // Map Custom fields
            if (booking.Metadata?.CustomFields != null)
            {
                var fieldsToDisplay = await _customFieldsDisplayService.GetCustomFieldsToDisplay(
                    booking.Metadata.CustomFields,
                    booking.TenantId);
                response.Metadata.CustomFields = fieldsToDisplay.ToDictionary(k => k.Key,
                    k => new CustomFieldValue { Title = k.Title, Value = k.Value, Key = k.ValueKey });
            }

            if (booking.FrequentFlyerNumbers?.Count > 0)
            {
                var codes = booking.FrequentFlyerNumbers.Select(x => x.Value.Code);
                var programs = await _frequentFlyerService.GetProgramsAsync(codes);

                foreach (var ffn in booking.FrequentFlyerNumbers)
                {
                    var program = programs?.FirstOrDefault(x => x.Code == ffn.Value.Code);
                    ffn.Value.ProgramName = program?.Name;
                }
            }

            var getFareTermsForTimelineTask = GetFareTermsForTimelineAsync(response.Price.Ccy, id, reservations);
            var getExtraServicesTask = GetExtraServices(id);
            var getBookingInvoiceeTask = GetBookingInvoiceeAsync(booking);
            var getFareServiceBundlesTask = GetFareServiceBundlesAsync(booking.FlightSolutionId);

            await Task.WhenAll(getFareTermsForTimelineTask, getExtraServicesTask, getBookingInvoiceeTask, getFareServiceBundlesTask);

            var fareResponse = await getFareTermsForTimelineTask;
            response.FareConditions = fareResponse.Terms?.ToFareTermsModel();
            
            var cancellations = fareResponse.Terms?.Cancellations.Unused;
            response.Terms.Cancellations = EnumHelper.ToString(cancellations?.Conditions ?? RefundCondition.Unknown);
            response.Terms.CancellationFee = cancellations?.Fee?.Amount;
            response.Terms.CancellationDeadline = cancellations?.DeadlineTimestampUtc?.ToDateTimeUtc();
            response.Terms.Changes = EnumHelper.ToString(response.FareConditions?.Changes?.Conditions);
            response.Terms.ChangeFee = response.FareConditions?.Changes?.Fee;
            response.Terms.ChangeDeadline = response.FareConditions?.Changes?.DeadlineTimestampUtc?.ToDateTimeUtc();

            response.ExtraServices = await getExtraServicesTask;
            response.Invoicee = await getBookingInvoiceeTask;
            response.FareServiceBundles = await getFareServiceBundlesTask;
            return response;
        }

        private async Task<InvoiceeDto> GetBookingInvoiceeAsync(Models.Booking booking)
        {
            if (booking.InvoiceeId <= 0)
            {
                return null;
            }
            var invoicee = await _billingClient.GetExtendedInvoiceeAsync(booking.InvoiceeId);
            if (invoicee is null)
            {
                return null;
            }

            var bookingInvoicee = new InvoiceeDto
            {
                Name = invoicee.Name,
            };

            switch (booking.PaymentMethod)
            {
                case PaymentMethod.BankTransfer:
                    bookingInvoicee.PaymentMethod = invoicee.PaymentMethods.FirstOrDefault(x =>
                    x.Type == BillingPaymentMethod.BankTransfer);
                    break;

                case PaymentMethod.CreditCard:
                    bookingInvoicee.PaymentMethod = invoicee.PaymentMethods.FirstOrDefault(x =>
                    x.Type == BillingPaymentMethod.CreditCard && x.Card?.Id == booking.PaymentMethodId);
                    break;

                case PaymentMethod.AmexBta:
                    bookingInvoicee.PaymentMethod = invoicee.PaymentMethods.FirstOrDefault(x =>
                    x.Type == BillingPaymentMethod.AmexBta && x.Card?.Id == booking.PaymentMethodId);
                    break;
            }

            return bookingInvoicee;
        }

        private async Task<IList<ExtraServiceDto>> GetExtraServices(string bookingId)
        {
            try
            {
                var extraServices = await _extraServiceManagementClient.GetExtraServicesByBooking(bookingId);
                return _mapper.Map<IList<ExtraServiceDto>>(extraServices) ?? Array.Empty<ExtraServiceDto>();
            }
            catch (Exception e)
            {
                _logger.Error(e, "Error occured while retrieving extra services");
                return Array.Empty<ExtraServiceDto>();
            }
        }

        private async Task<IReadOnlyCollection<FareServiceBundleDto>> GetFareServiceBundlesAsync(string flightSolutionId)
        {
            try
            {
                return await _searchClient.GetFareServiceBundles(flightSolutionId);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error occured while retrieving fare services");
                return default;
            }
        }

        private async Task<OnPostBookingResponse> GetFareTermsForTimelineAsync(string targetCurrency,
            string correlationId, List<Reservation> reservations)
        {
            try
            {
                var couponsDict = await GetCouponsAsync(reservations);
                var fareRequest = new OnPostBookingRequest
                {
                    CalculateAt = reservations.Max(r => r.CancelledAt),
                    TargetCurrency = targetCurrency,
                    CorrelationId = correlationId,
                    Timelines = reservations.Select(r => new TicketTimeLine
                    {
                        Source = r.Source,
                        TotalPrice = r.Price.Total,
                        LastVoidAt = GetLastVoidAt(r),
                        CancellationTimeline = r.CancellationTimeline,
                        ChangeTimeline = r.ChangeTimeline,
                        PartiallyUsedChangeTimeline = r.PartiallyUsedChangeTimeline,
                        Coupons = CollectionExtensions.GetValueOrDefault(couponsDict, r.Id) 
                    }).ToList()
                };
                
                return await _fareTermsService.GetFareTermsByTimelineAsync(fareRequest);
            }
            catch (Exception e)
            {
                _logger.Error(e, e.Message);
                return new OnPostBookingResponse
                {
                    Terms = new FareTermsDto
                    {
                        Cancellations = new CancellationTermsDto
                        {
                            Current = new RefundTermsDto(),
                            Unused = new RefundTermsDto()
                        }
                    }
                };
            }
        }
        
        /// <summary>
        /// Takes a list of reservations and returns a dictionary. Each key is a reservation ID, and each value is the segment coupons of that reservation.    
        /// </summary>
        private async Task<Dictionary<string, List<SegmentCoupon>>> GetCouponsAsync(List<Reservation> reservations)
        {
            var couponsDict = new Dictionary<string, List<SegmentCoupon>>();
            foreach (var reservation in reservations)
            {
                var ticket = reservation.Tickets?.FirstOrDefault(x => x.State is TicketState.Open or TicketState.Used);

                if (ticket == null || reservation.State == ReservationState.Cancelled)
                {
                    couponsDict.Add(reservation.Id, new List<SegmentCoupon>(0));
                }
                else
                {
                    var ticketInfoResponse = await _providerTicketStateService.GetTicketInfoAsync(
                        new ProviderRetrieveTicketRequest
                        {
                            Locators = reservation.Locators,
                            Source = reservation.Source,
                            DocumentNumber = ticket.Number,
                            CorrelationId = reservation.Id
                        });
                    
                    if (ticketInfoResponse.HasError)
                    {
                        _logger.Warning("Unable to retrieve ticket info. {ErrorMessage} {ErrorCode}", 
                            ticketInfoResponse.Error.ErrorMessage, ticketInfoResponse.Error.ErrorCode);
                    }
                    else if(ticketInfoResponse.Ticket?.Coupons?.Any() == true)
                    {
                        if (reservation.TenantId == "cteleport-partially-used" && ticketInfoResponse.Ticket?.Coupons?.Any() == true)
                        {
                            ticketInfoResponse.Ticket.Coupons.First().Status = "USED";
                        }

                        couponsDict.Add(reservation.Id, ticketInfoResponse.Ticket.Coupons.Select(x => x.ToSegmentCoupon()).ToList());
                    }
                }
            }
            
            return couponsDict;
        }

        public DateTime GetLastVoidAt(Reservation reservation, DateTime? issuedAt = null)
        {
            if (!reservation.CanCancel)
                return default(DateTime);

            //TODO: check if this is correct for reissued tickets (change>reissue)
            var ticket = reservation.Tickets.OrderByDescending(t => t.IssuedAt).FirstOrDefault();

            if (ticket != null)
            {
                return ticket.LastVoidAt;
            }

            return _ticketService.CalcLastVoidTimeForReservation(reservation,
                issuedAt ?? DateTime.Now.ToUniversalTime());
        }

        public async Task<PublicBookingDto> GetPublicBookingAsync(string id)
        {
            // TODO: consider refactoring, this needs to be executed within System context
            var booking = await _bookingsRepository.GetAsync(id);

            if (booking == null)
            {
                return null;
            }

            var reservations = (await GetBookingReservationsAsync(id))
                .OrderBy(r => r.DepartureAt).ToList();

            var response = _mapper.Map<PublicBookingDto>(booking);
            _mapper.Map(reservations, response, opts => opts.Items["LegsTotal"] = booking.Legs.Count);
            response.Reservations = _mapper.Map<IList<BaseReservationDto>>(reservations);

            response.ExtraServices = await GetExtraServices(id);

            // Hot-fix!
            // Remove it when E-Ticket service will be able to render PDF with train extra-service type
            if (response.ExtraServices?.Any() ?? false)
            {
                foreach (var service in response.ExtraServices.Where(s => s.Type == ExtraServiceType.Train || s.Type == ExtraServiceType.Ferry))
                {
                    _logger.Information("Replace service type to `Other` for {ServiceId}", service.Id);
                    service.Type = ExtraServiceType.Other;
                }
            }
            
            return response;
        }

        public async Task<ItineraryReceiptBookingDto> GetItineraryReceiptBookingAsync(string id)
        {
            var booking = await _bookingsRepository.GetAsync(id);

            if (booking == null)
            {
                return null;
            }

            var reservations = (await GetBookingReservationsAsync(id))
                .OrderBy(r => r.DepartureAt).ToList();

            var response = _mapper.Map<ItineraryReceiptBookingDto>(booking);
            await MapItineraryReceiptPrice(response, booking);
            
            _mapper.Map(reservations, response, opts => opts.Items["LegsTotal"] = booking.Legs.Count);
            response.Reservations = _mapper.Map<IList<BaseReservationDto>>(reservations);
            
            // Map Custom fields
            if (booking.Metadata?.CustomFields != null)
            {
                var fieldsToDisplay = await _customFieldsDisplayService.GetCustomFieldsToDisplay(
                    booking.Metadata.CustomFields,
                    booking.TenantId);
                response.Metadata.CustomFields = fieldsToDisplay.ToDictionary(k => k.Key,
                    k => new CustomFieldValue { Title = k.Title, Value = k.Value, Key = k.ValueKey });
            }

            var getFareTermsForTimelineTask = GetFareTermsForTimelineAsync(response.Price.Ccy, id, reservations);
            var getExtraServicesTask = GetExtraServices(id);
            var getFareServiceBundlesTask = GetFareServiceBundlesAsync(booking.FlightSolutionId);

            await Task.WhenAll(getFareTermsForTimelineTask, getExtraServicesTask, getFareServiceBundlesTask);

            var fareResponse = await getFareTermsForTimelineTask;
            response.FareConditions = fareResponse.Terms?.ToFareTermsModel();
            response.FareServiceBundles = await getFareServiceBundlesTask;

            response.ExtraServices = await getExtraServicesTask;
            return response;
        }

        private async Task MapItineraryReceiptPrice(ItineraryReceiptBookingDto response, Models.Booking booking)
        {
            var priceDto = new ItineraryReceiptPriceDto
            {
                Ccy = booking.Price.Currency
            };

            var tenant = await _tenantManagementService.GetTenantAsync(booking.TenantId);
            var features = tenant?.TenantFeatures;
            if (features?.EnableEticketTotalPrice != null && features.EnableEticketTotalPrice.Value)
            {
                priceDto.IsPublic = true;
                priceDto.Total = booking.Price.Total;
                priceDto.PerMile = booking.Price.PerMile;
            }
            else
            {
                priceDto.IsPublic = false;
            }

            response.Price = priceDto;
        }

        public async Task<IEnumerable<OfflineBookingDto>> GetArchiveBookingsBatchAsync(IEnumerable<string> ids)
        {
            // TODO: consider refactoring, this needs to be executed within System context
            var bookingIds = ids.ToArray();
            var bookings = await _bookingsRepository.GetByIdsAsync(bookingIds);

            if (bookings == null)
            {
                return null;
            }

            var reservationsByBookingId = (await _reservationsRepository.GetAllAsync(bookingIds))
                .GroupBy(r => r.BookingId)
                .ToDictionary(grp => grp.Key, grp => grp.OrderBy(r => r.DepartureAt).ToList());

            var result = new List<OfflineBookingDto>(bookingIds.Length);

            foreach (var booking in bookings)
            {
                try
                {
                    var response = _mapper.Map<OfflineBookingDto>(booking);
                    if (!reservationsByBookingId.TryGetValue(booking.Id, out var reservations))
                    {
                        throw new InvalidOperationException($"Reservations not found by bookingId {booking.Id}");
                    }

                    _mapper.Map(reservations, response, opts => opts.Items["LegsTotal"] = booking.Legs.Count);
                    response.Reservations = _mapper.Map<IList<BaseReservationDto>>(reservations);

                    // Map Custom fields
                    if (booking.Metadata?.CustomFields != null)
                    {
                        var fieldsToDisplay = await _customFieldsDisplayService.GetCustomFieldsToDisplay(
                            booking.Metadata.CustomFields,
                            booking.TenantId);
                        response.Metadata.CustomFields = fieldsToDisplay.ToDictionary(k => k.Key,
                            k => new CustomFieldValue { Title = k.Title, Value = k.Value, Key = k.ValueKey });
                    }
                    
                    result.Add(response);
                }
                catch (Exception e)
                {
                    _logger.ForContext(nameof(Models.Booking) ,booking, destructureObjects:true)
                        .Error($"{e.Message} | {e.StackTrace}");
                }
            }

            return result;
        }

        public async Task<string> GetFilterHash()
        {
            var filters = (await _authService.GetReadValidationFiltersAsync())
                .SelectMany(f => f)
                .OrderBy(f => f.Operator);

            var filtersString = string.Join("||", filters);
            return MD5Helper.MD5Hash(filtersString);
        }

        public async Task<IEnumerable<Vessel>> GetBookedVesselsAsync(string vesselName, int maxCount)
        {
            var filters = await _authService.GetReadValidationFiltersAsync();
            return await _bookingsRepository.GetBookedVesselsAsync(filters, vesselName, maxCount);
        }

        public async Task SyncBookingPriceAsync(string reservationId)
        {
            var reservation = await _reservationsRepository.GetAsync(reservationId);

            if (reservation == null)
            {
                throw new NotFoundException($"Reservation not found. {reservationId}");
            }
            
            var booking = await _bookingsRepository.GetAsync(reservation.BookingId);
            var newPrice = reservation.GetFullTicketsPrice();

            booking.Price.Total = newPrice.Total;
            booking.Price.Markup = newPrice.TargetMarkup;
            booking.Price.Kickback = newPrice.TargetKickback;
            
            await _bookingsRepository.UpdatePriceAsync(booking.Id, booking.Price);
        }

        public async Task<IEnumerable<Models.Booking>> GetBookingsAsync(string[] bookingIds)
        {
            return await _bookingsRepository.GetByIdsAsync(bookingIds);
        }

        public async Task<IEnumerable<BaseBookingDto>> GetBookingsScopedAsync(DateTime? startFrom, DateTime? endAt = null)
        {
            var filters = await _authService.GetReadValidationFiltersAsync();
            var bookings = await _bookingsRepository.GetAllScopedAsync(filters, startFrom, endAt);
            return _mapper.Map<IEnumerable<BaseBookingDto>>(bookings);
        }

        public async Task<BookingsBatchDto> GetBookingsBatchAsync(BookingsPagingToken startFrom, BookingsPagingToken endAt, int batchSize)
        {
            var filters = await _authService.GetReadValidationFiltersAsync();
            var bookings = await _bookingsRepository.GetBatchScopedAsync(filters, startFrom, endAt, batchSize);

            var bookingsBatch = bookings.ToArray();
            var lastBooking = bookingsBatch.LastOrDefault();

            var iataCodes = bookingsBatch.SelectMany(b => b.Legs.Select(l => l.Origin))
                .Union(bookingsBatch.SelectMany(b => b.Legs.Select(l => l.Destination))).ToArray();

            var airports = await _placesClient.GetAirportsByCodeAsync(iataCodes);
            var overlaySecond = startFrom != null ? -1 : endAt != null ? 1 : 0;

            return new BookingsBatchDto
            {
                Bookings = _mapper.Map<IEnumerable<BaseBookingDto>>(bookingsBatch),
                NextRequestDate = lastBooking != null && bookingsBatch.Length == batchSize
                    ? new BookingsPagingToken
                    {
                        UpdatedAt = (lastBooking.UpdatedAt ?? lastBooking.CancelledAt ?? lastBooking.CreatedAt).AddSeconds(overlaySecond),
                        CreatedAt = lastBooking.CreatedAt.AddSeconds(overlaySecond),
                    }
                    : null,
                Airports = airports.ToDictionary(a => a.IATA)
            };
        }

        public async Task<IEnumerable<BaseBookingDto>> GetBookingsAsync(DateTime startFrom, DateTime endAt, string tenantId = null)
        {
            var bookings = await _bookingsRepository.GetAllAsync(startFrom, endAt, tenantId);
            return _mapper.Map<IEnumerable<BaseBookingDto>>(bookings);
        }

        public async Task<IEnumerable<BaseBookingDto>> GetActiveBookingsForCrewChangeAsync(string vesselName,
            string crewChangeDate)
        {
            // TODO: inject security context into bookings query
            // TODO: refactor to pass crewchange filter
            var bookings = await _bookingsRepository.GetActiveForCrewChangeAsync(vesselName, crewChangeDate);
            return _mapper.Map<IEnumerable<BaseBookingDto>>(bookings);
        }

        public async Task DisableClassDropCheck(string reservationId)
        {
            await _reservationsRepository.SetClassDropIsAllowed(reservationId, false);
        }

        public async Task SetClassDropIsAllowed(string reservationId, bool isAllowed)
        {
            await _reservationsRepository.SetClassDropIsAllowed(reservationId, isAllowed);
        }

        public async Task<decimal> GetScheduledSalesVolumeAsync(string source)
            => await _reservationsRepository.GetScheduledSalesVolumeAsync(source);

        public async Task<decimal> GetScheduledSalesVolumeByTenantAsync(string tenant)
            => await _reservationsRepository.GetScheduledSalesVolumeByTenantAsync(tenant);

        public async Task SetReservationFareIsCheapestAsync(Reservation reservation, bool? value)
            => await _reservationsRepository.SetFareIsCheapest(reservation, value);

        public async Task SetBookingCancelledByAsync(string bookingId, User user)
            => await _bookingsRepository.SetBookingCancelledByAsync(bookingId, user);

        public async Task<IEnumerable<BaseBookingDto>> GetBookingsForSearchIdAsync(string searchId)
        {
            var filters = await _authService.GetReadValidationFiltersAsync();
            var bookings = await _bookingsRepository.GetBySearchIdAsync(filters, searchId);
            return _mapper.Map<IEnumerable<BaseBookingDto>>(bookings);
        }

        public async Task<IEnumerable<CompleteBookingDto>> GetBookingsForTenantAsync(string tenantId)
        {
            // TODO: consider refactoring, this needs to be executed within System context
            var bookings = await _bookingsRepository.GetAllForTenantAsync(tenantId);
            var reservations = await _reservationsRepository.GetAllForTenantAsync(tenantId);

            // Enrich all reservations with fare rules for complete booking details
            await _fareRulesEnrichmentService.EnrichFareRulesAsync(reservations);

            return bookings.Select(booking =>
            {
                var completeBooking = _mapper.Map<CompleteBookingDto>(booking);

                var bookingReservations = reservations.Where(r => r.BookingId == booking.Id).ToList();
                _mapper.Map(bookingReservations, completeBooking);
                completeBooking.Reservations = _mapper.Map<IList<BaseReservationDto>>(bookingReservations);

                return completeBooking;
            }).ToList();
        }

        public async Task<Models.Booking> GetBookingByFlightSolutionAsync(string flightSolutionId)
        {
            var booking = await _bookingsRepository.GetByFlightSolutionAsync(flightSolutionId);

            if (booking != null)
            {
                await ValidateAccessAsync(booking);
            }

            return booking;
        }

        public async Task<IEnumerable<BaseBookingDto>> GetBookingsForPassengerAsync(string firstName, string lastName,
            string departureDate, int range = 2)
        {
            if (string.IsNullOrWhiteSpace(firstName))
            {
                firstName = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(lastName))
            {
                lastName = string.Empty;
            }

            var filters = await _authService.GetReadValidationFiltersAsync();
            var bookings = await _bookingsRepository.GetByPassengerAsync(filters, firstName, lastName);

            var result = bookings
                .Where(b =>
                    b.State != Enums.BookingState.Cancelled &&
                    b.State != Enums.BookingState.Declined &&
                    b.State != Enums.BookingState.RefundPending &&
                    Math.Abs((b.DepartureAt.Date - DateTime.Parse(departureDate, CultureInfo.InvariantCulture).Date)
                        .Days) <= range);

            return _mapper.Map<IEnumerable<BaseBookingDto>>(result);
        }

        public async Task<IEnumerable<Reservation>> GetReservationsToTicketAsync()
            => await _reservationsRepository.GetActiveReservationsToTicketAsync();

        public async Task<IReadOnlyCollection<Reservation>> GetReservationsToTicketAsync(IEnumerable<string> bookingIds)
        {
            var reservations = await _reservationsRepository.GetAllAsync(bookingIds);

            return reservations
                .Where(r =>
                    r.State is ReservationState.Active &&
                    r.TicketingFailed is null &&
                    !r.Ticketless &&
                    !r.ApprovalRequired &&
                    !r.IsVirtual &&
                    !r.PaymentRequired &&
                    !r.Tickets.Any())
                .ToList();
        }

        public async Task<IEnumerable<Reservation>> GetReservationsToRefreshFareAsync()
            => await _reservationsRepository.GetReservationsToRefreshFareAsync();

        public async Task AddTicketToReservationAsync(Reservation reservation, string ticketNumber, decimal ticketPrice,
            string issueDate, string fareCalc, DateTime issuedAt, IDictionary<string, decimal> taxes,
            IList<TaxDetail> taxDetails, string fundingSource)
        {
            var lastVoidAt = _ticketService.CalcLastVoidTimeForReservation(reservation, issuedAt);

            var ticket = new Ticket
            {
                Number = ticketNumber,
                State = TicketState.Open,
                IssueDate = issueDate,
                IssuedAt = issuedAt,
                LastVoidAt = lastVoidAt,
                FundingSource = fundingSource,
            };

            ticket.Price = new TicketPrice
            {
                Net = ticketPrice,
                Taxes = taxes,
                TaxDetails = taxDetails,
          
                Currency = reservation.Fare.Currency,
                FareCalc = fareCalc
            };

            await _reservationsRepository.AddTicketAsync(reservation.Id, ticket, issuedAt);
        }

        public async Task ChangeReservationStateAsync(string reservationId, ReservationState state,
            DateTime? timestamp = null)
            => await _reservationsRepository.SetStateAsync(reservationId, state, timestamp);

        public async Task ChangeBookingStateAsync(string bookingId, BookingState state)
            => await _bookingsRepository.SetStateAsync(bookingId, state);

        public async Task SetTicketAsFailedAsync(Reservation reservation)
            => await _reservationsRepository.SetTicketingFailedAsync(reservation.Id);

        public async Task SetTicketAsVoidedAsync(Reservation reservation, string ticketNumber, DateTime voidedAt)
        {
            var refund = new ReservationRefund
            {
                Net = reservation.Price.Net,
                CurrencyMargin = reservation.Price.TargetCurrencyMargin,
                Total = reservation.Price.Total,
                Currency = reservation.Price.Currency
            };

            await _reservationsRepository.SetTicketAsVoidedAsync(reservation.Id, ticketNumber, refund, voidedAt);
        }

        public async Task SetTicketAsNoShowAsync(Reservation reservation, string ticketNumber)
            => await _reservationsRepository.SetTicketAsNoShowAsync(reservation.Id, ticketNumber);

        public async Task SetTicketAsUsedAsync(Reservation reservation, string ticketNumber)
            => await _reservationsRepository.SetTicketAsUsedAsync(reservation.Id, ticketNumber);

        public async Task AddReservationInvoiceAsync(string reservationId, string invoiceNumber)
            => await _reservationsRepository.AddReservationInvoiceAsync(reservationId, invoiceNumber);

        public async Task AddReservationCreditNoteAsync(string reservationId, string creditNoteNumber)
            => await _reservationsRepository.AddReservationCreditNoteAsync(reservationId, creditNoteNumber);

        public async Task SetReservationInvoicesAsync(string reservationId, ICollection<string> invoiceNumbers)
            => await _reservationsRepository.SetReservationInvoicesAsync(reservationId, invoiceNumbers);

        public async Task SetReservationCreditNotesAsync(string reservationId, ICollection<string> creditNoteNumbers)
            => await _reservationsRepository.SetReservationCreditNotesAsync(reservationId, creditNoteNumbers);

        private async Task ValidateAccessAsync(Models.Booking booking)
        {
            if (_context.IsSystem)
            {
                return;
            }

            if (_context.TenantId == null && !_context.IsSystem)
            {
                throw new Common.Exceptions.AccessViolationException($"Tenant id is not set for non system context");
            }

            await _authService.ValidateIfBookingCanBeReadOrFailAsync(booking);
        }

        public async Task<IEnumerable<ReservationOverview>> GetReservationsAsync(DateTime createdAfter)
        {
            var filters = await _authService.GetReadValidationFiltersAsync();
            return await _reservationsRepository.GetOverviewAsync(filters, createdAfter);
        }

        public async Task<IEnumerable<CompleteReservationDto>> GetDetailedReservationsByBookingIdAsync(string bookingId)
        {
            var reservations = (await _reservationsRepository.GetByBookingIdAsync(bookingId)).ToList();
            
            await _fareRulesEnrichmentService.EnrichFareRulesAsync(reservations);
            
            return _mapper.Map<IEnumerable<CompleteReservationDto>>(reservations);
        }

        public async Task<CompleteReservationDto> GetReservationDetailsAsync(string id)
        {
            var reservation = await _reservationsRepository.GetAsync(id);
            
            if (reservation == null)
            {
                throw new NotFoundException($"Reservation with ID {id} was not found.");
            }
            
            await _fareRulesEnrichmentService.EnrichFareRulesAsync([reservation]);
            
            return _mapper.Map<CompleteReservationDto>(reservation);
        }

        public async Task<IEnumerable<Reservation>> GetActiveReservationsAsync()
        {
            return await _reservationsRepository.GetAllActiveAsync();
        }

        public async Task<IEnumerable<Reservation>> GetAllReservationsWithoutSupplierLocatorsAsync()
            => await _reservationsRepository.GetAllWithoutSupplierLocatorsAsync();

        public async Task SetSupplierLocatorsAsync(string reservationId, Dictionary<string, string> supplierLocators)
            => await _reservationsRepository.SetSupplierLocatorsAsync(reservationId, supplierLocators);

        public async Task SetLocatorsAsync(string reservationId, Dictionary<string, string> locators)
            => await _reservationsRepository.SetLocatorsAsync(reservationId, locators);

        public async Task SetBookingAsNoShowAsync(string bookingId)
            => await _bookingsRepository.SetAsNoShowAsync(bookingId);

        public async Task UpdateReservationFareAsync(Reservation reservation, Fare newFare, FareChangeReason fareChangeReason)
        {
            var currentFare = reservation.Fare;
            
            reservation.OriginalFare ??= reservation.Fare;
            
            reservation.Fare = _mapper.Map<Fare>(newFare);
            reservation.Fare.NonRefAmounts = reservation.OriginalFare.NonRefAmounts;
            reservation.Fare.Cancellations = reservation.OriginalFare.Cancellations;
            reservation.Fare.Changes = reservation.OriginalFare.Changes;
            reservation.Fare.LatestTicketingTime = newFare.LatestTicketingTime ?? reservation.OriginalFare.LatestTicketingTime;
            
            //We don't trust providers to provide correct fare type, so we use the one from the original fare
            reservation.Fare.FareType = currentFare.FareType;
            
            await _reservationsRepository.UpdateFareAsync(reservation);

            await _fareChangeService.AddFareChangeAsync(reservation.Id, currentFare, newFare, fareChangeReason);
        }

        public async Task UpdateBookingPassengerAsync(string bookingId, MutablePassengerDetails passengerDetails)
            => await _bookingsRepository.UpdatePassengerDetails(bookingId, passengerDetails);

        public async Task UpdateBookingComment(string bookingId, string comment, User user)
            => await _bookingsRepository.UpdateComment(bookingId, comment, user);

        public async Task UpdateReservationSegmentsAsync(Reservation reservation)
            => await _reservationsRepository.UpdateSegmentsAsync(reservation);

        public async Task UpdateReservationIrregularitiesAsync(Reservation reservation)
            => await _reservationsRepository.UpdateIrregularitiesAsync(reservation);

        public async Task<Reservation> GetReservationByFareRuleCat16IdAsync(string fareRuleId)
        {
            var reservation = await _reservationsRepository.GetByFareRuleCat16IdAsync(fareRuleId);
            
            if (reservation == null)
            {
                throw new NotFoundException($"Fare Rule with ID '{fareRuleId}' was not found.");
            }
            
            await _fareRulesEnrichmentService.EnrichFareRulesAsync([reservation]);
            
            return reservation;
        }

        public async Task SetProhibitAutoRefund(string reservationId, string ticketNumber, bool prohibitAutoRefund)
            => await _reservationsRepository.SetProhibitAutoRefund(reservationId, ticketNumber, prohibitAutoRefund);

        public Task<long> SetProhibitAutoRefundByFareRuleAsync(string fareRuleId, bool prohibitAutoRefund)
            => _reservationsRepository.SetProhibitAutoRefundByFareRule(fareRuleId, prohibitAutoRefund);

        public async Task<RefundQuote> GetRefundQuoteAsync(string bookingId)
        {
            var watch = System.Diagnostics.Stopwatch.StartNew();

            var booking = await _bookingsRepository.GetAsync(bookingId);

            var reservations = (await _reservationsRepository.GetByBookingIdAsync(bookingId))
                .Where(r => r.State == ReservationState.Active)
                .ToList();

            await _fareRulesEnrichmentService.EnrichFareRulesAsync(reservations);
            
            if (reservations.Count == 0)
                throw new ValidationException("Booking with no active reservations can't be cancelled");

            _logger.Debug("[GetRefundQuote] Retrieved reservations for {BookingId} in {ElapsedMs} ms.", bookingId,
                watch.ElapsedMilliseconds);

            if (reservations.All(x => x.IsVirtual))
            {
                return new RefundQuote
                {
                    TotalPrice = booking.Price.Total,
                    Ccy = booking.Price.Currency,
                    CanCancel = true,
                    Refund = true,
                    RefundAmount = booking.Price.Total,
                    RefundFee = 0
                };
            }

            var fares = await GetFareTermsForTimelineAsync(booking.Price.Currency, bookingId, reservations);
            var cancellation = fares.Terms.Cancellations.Current;

            _logger.Debug("[GetRefundQuote] Got cancellation timestamp for {BookingId} in {ElapsedMs} ms.", bookingId,
                watch.ElapsedMilliseconds);

            var tasks = reservations.Select(r => _ticketUsageService.GetTicketUsageAsync(r)).ToArray();
            await Task.WhenAll(tasks);
            var usages = tasks.Select(t => t.Result).ToArray();

            _logger.Debug("[GetRefundQuote] Got ticket usage state for {BookingId} in {ElapsedMs} ms.", bookingId,
                watch.ElapsedMilliseconds);

            return new RefundQuote(reservations.Any(r => r.CanCancel),
                booking.Price.Total,
                cancellation.Refund,
                cancellation.Fee?.Amount,
                booking.Price.Currency,
                ReservationHelper.IsUsed(usages.Select(q => q.IsUsed)),
                ReservationHelper.IsFullyUsed(usages.Select(q => q.IsFullyUsed)));
        }

        public Task SetNonRefundableAsync(string reservationId, string ticketNumber, bool nonRefundable)
            => _reservationsRepository.SetNonRefundableAsync(reservationId, ticketNumber, nonRefundable);

        public Task SetAsDeclinedByAsync(string bookingId, Search.Shared.Models.User rejectedBy, string reason)
            => _bookingsRepository.SetAsDeclinedByAsync(bookingId, rejectedBy, reason);

        public Task SetAsApprovedByAsync(string bookingId, params Search.Shared.Models.User[] approvedBy)
            => _bookingsRepository.SetAsApprovedByAsync(bookingId, approvedBy);

        public Task UpdateBookingVesselAsync(string bookingId, VesselDetails vessel)
            => _bookingsRepository.UpdateVesselAsync(bookingId, vessel);

        public Task UpdateReservationVesselAsync(string reservationId, VesselDetails vessel)
            => _reservationsRepository.UpdateReservationVesselAsync(reservationId, vessel);

        public async Task<BaseBookingDto> GetTenantFirstBookingAsync(string tenant)
        {
            var booking = await _bookingsRepository.GetTenantFirstBookingAsync(tenant);

            if (booking == null)
            {
                return null;
            }

            return _mapper.Map<BaseBookingDto>(booking);
        }

        public Task UpdateBookingLegsDatesAsync(string bookingId, ICollection<Models.Leg> legs)
            => _bookingsRepository.UpdateLegsAsync(bookingId, legs);

        public Task UpdateReservationLegsDatesAsync(string reservationId, ICollection<Models.Leg> legs)
            => _reservationsRepository.UpdateLegsAsync(reservationId, legs);

        public async Task<UpdatesBatch<BookingUpdateDto>> GetBookingsUpdatesAsync(int limit, UpdatesBatchResumeToken resumeToken)
        {
            var bookings = await _bookingsRepository.GetUpdatedSinceBatchAsync(limit, resumeToken.UpdatedAt, resumeToken.CreatedAt);
            var updates = _mapper.Map<BookingUpdateDto[]>(bookings);
            var last = updates.LastOrDefault();

            return new UpdatesBatch<BookingUpdateDto>
            {
                Updates = updates,
                ResumeToken = CreateResumeToken(last?.UpdatedAt, last?.CreatedAt, resumeToken)
            };
        }

        private UpdatesBatchResumeToken CreateResumeToken(DateTime? updatedAt, DateTime? createdAt, UpdatesBatchResumeToken defaults)
        {
            if (updatedAt.HasValue && createdAt.HasValue)
            {
                return new UpdatesBatchResumeToken(updatedAt.Value, createdAt.Value);
            }

            return defaults;
        }

        public async Task<UpdatesBatch<ReservationUpdateDto>> GetReservationsUpdatesAsync(int limit, UpdatesBatchResumeToken resumeToken)
        {
            var reservations = await _reservationsRepository.GetUpdatedSinceBatchAsync(limit, resumeToken.UpdatedAt, resumeToken.CreatedAt);
            var updates = _mapper.Map<ReservationUpdateDto[]>(reservations);
            var last = updates.LastOrDefault();
            return new UpdatesBatch<ReservationUpdateDto>
            {
                Updates = updates,
                ResumeToken = CreateResumeToken(last?.UpdatedAt, last?.CreatedAt, resumeToken)
            };
        }
    }
}