using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Messages.Commands.Models;
using CTeleport.Messages.Models;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.CustomFields.Dto;
using CTeleport.Services.CustomFields.Models;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.FareCache;
using CTeleport.Services.FrequentFlyer.Models;
using CTeleport.Services.FrequentFlyer.Service;
using CTeleport.Services.Helpers;
using CTeleport.Services.Price.Services;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;
using CTeleport.Services.SearchProxy.Services;
using CTeleport.Services.Settings.Services;
using CTeleport.Services.ApprovalQueueClient.Configuration;
using CTeleport.Services.Helpers.Extensions;
using CTeleport.Services.Providers.Client;
using CTeleport.Services.Search.Shared.Helpers;
using Serilog;
using ServiceStack.Text;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using FareRuleSection = CTeleport.Services.Search.Shared.Models.FareRuleSection;

namespace CTeleport.Services.Booking.Services
{
    public class ReservationBuilder : IReservationBuilder
    {
        private readonly ILogger _logger;
        private readonly IFareRulesCache _fareRulesCache;
        private readonly ICustomFieldsService _customFieldsService;
        private readonly ISettingsService _settingsService;
        private readonly ISearchJobService _searchJobService;
        private readonly ITicketService _ticketService;
        private readonly IPriceService _priceService;
        private readonly IFrequentFlyerService _frequentFlyerService;
        private readonly ApprovalQueueOptions _approvalQueueOptions;
        private readonly IProvidersClient _providersClient;
        private readonly IMapper _mapper;

        public ReservationBuilder(
            ILogger logger,
            IFareRulesCache fareRulesCache,
            ICustomFieldsService customFieldsService,
            ISettingsService settingsService,
            ISearchJobService searchJobService,
            ITicketService ticketService,
            ApprovalQueueOptions approvalQueueOptions,
            IPriceService priceService,
            IFrequentFlyerService frequentFlyerService,
            IMapper mapper, IProvidersClient providersClient)
        {
            _logger = logger;
            _fareRulesCache = fareRulesCache;
            _customFieldsService = customFieldsService;
            _settingsService = settingsService;
            _searchJobService = searchJobService;
            _ticketService = ticketService;
            _approvalQueueOptions = approvalQueueOptions;
            _priceService = priceService;
            _frequentFlyerService = frequentFlyerService;
            _mapper = mapper;
            _providersClient = providersClient;
        }

        public async Task SetCustomFields(CreateReservationParams reservationParams)
        {
            try
            {
                var context = await _customFieldsService.GetContextMetadata(reservationParams.TenantId, reservationParams.Source, reservationParams.Metadata?.VesselName);

                reservationParams.TargetSource = context.TargetSource;
                reservationParams.AgentMetadata = context.AgentMetadata; // TODO: review if we can avoid request mutation here

                var (reservationMetadata, remarksMetadata) = await CalculateCustomFields(context, reservationParams, reservationParams.BookingId);

                reservationParams.ReservationMetadata = reservationMetadata; // Fields to store in DB
                reservationParams.RemarksMetadata = remarksMetadata;
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(reservationParams.BookingId), reservationParams.BookingId).Error(e, "Unhandled Exception SetCustomFields");
            }
        }

        private static Dictionary<string, string> CreateReservationMetadata(Dictionary<string, Dictionary<string, string>> advancedMetadata)
        {
            //Flattering metadata to store in DB
            var reservationMetadata = new Dictionary<string, string>();
            foreach (var groupItem in advancedMetadata)
            {
                if (groupItem.Key == "Common")
                {
                    continue;
                }

                foreach (var item in groupItem.Value)
                {
                    reservationMetadata[groupItem.Key + "_" + item.Key] = item.Value;
                }
            }
            return reservationMetadata;
        }

        private async Task<(Dictionary<string, string> flatAdvancedMetadata, List<RemarkMetadata> remarksMetadata)> CalculateCustomFields(ReservationContext context, IReservationParams reservationParams, string bookingId)
        {
            var request = CustomMetadataRequestBuilder.GetMetadataRequest(reservationParams, context);
            request.Type = CustomMetadataRequestType.Reservation;

            var metadata = await _customFieldsService.CreateMetadata(request);

            var remarksMetadata = new List<RemarkMetadata>();

            try
            {
                var remarksRequest = CustomMetadataRequestBuilder.GetRemarksRequest(context, reservationParams, bookingId, request, metadata);

                remarksMetadata = await _customFieldsService.GenerateRemarks(remarksRequest);

                _logger.ForContext("BookingId", bookingId).Information("Remarks metadata for booking {BookingId}: {@remarks}", bookingId, remarksMetadata);

                return (CreateReservationMetadata(metadata.AdvancedMetadata), remarksMetadata);
            }
            catch (Exception ex)
            {
                _logger.ForContext("BookingId", bookingId).Error(ex, "Building remarks threw an exception.");
            }

            return (CreateReservationMetadata(metadata.AdvancedMetadata), remarksMetadata);
        }

        private static CustomFields.Dto.RemarksBuilderRequest GetRemarksRequest(ReservationContext context, 
            IReservationParams reservationParams, string bookingId, CustomFields.Dto.CustomMetadataRequest request, 
            CustomFields.Dto.CustomMetadataResponse metadata)
        {
            return new CustomFields.Dto.RemarksBuilderRequest()
            {
                AdvancedMetadata = metadata.AdvancedMetadata,
                ReservationContext = context,
                CorporateCodes = reservationParams.CorporateCodes,
                Vessel = request.Vessel,
                Site = request.Site,
                PlatingCarrier = reservationParams.PlatingCarrier,
                LegSegments = reservationParams.LegSegments,
                CorrelationId = bookingId
            };
        }

        private async Task<ReservationPrice> CalculatePrice(CreateReservationParams reservationParams, FlightSolution flightSolution, FareType fareType)
        {
            var price = VirtualReservationPriceBuilder.MapPrice(flightSolution, reservationParams.ProviderKey);

            var priceConverter = await _priceService.CreatePriceConverterAsync(reservationParams.TenantId,
                reservationParams.Metadata?.VesselName,
                price.OriginalCurrency, price.Currency, reservationParams.Source);
            
            var reservationPrice = ReservationMapper.BuildPrice(flightSolution, price.Net, price.Currency, price.OriginalCurrency, fareType, priceConverter);

            if (reservationParams.ExemptLiTax == true)
            {
                reservationPrice.Net -= flightSolution.Price.OriginalLiTax;
                reservationPrice.Total -= flightSolution.Price.LiTax;
            }

            return reservationPrice;
        }

        public async Task<CreateReservationParams> BuildReservationParams(string requestId, string providerKey, FlightSolution flightSolution,
            BookingSaga saga, ReservationEntity sagaReservation, bool exemptLiTax)
        {
            bool approvalRequired = saga.BookingEntity.State is Domain.Aggregates.BookingAggregate.Enums.BookingState.ApprovalRequired;
            bool paymentConfirmedRequired = saga.BillingEntity.PaymentMethod is Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.CreditCard &&
                                            saga.SagaPaymentState is not Domain.Aggregates.BookingAggregate.Enums.BookingSagaPaymentState.PaymentConfirmed;
            
            bool isImmediateTicketing = await _ticketService.IsImmediateTicketing(flightSolution, approvalRequired, providerKey);
            bool createVirtual = IsVirtualReservation(providerKey, approvalRequired, paymentConfirmedRequired, isImmediateTicketing);
            
            _logger.ForContext("Key", "TicketingTime")
                .Information("Is immediate ticketing: {IsImmediateTicketing}, create virtual: {CreateVirtual}", 
                    isImmediateTicketing, createVirtual);

            var legSegments = SegmentExtractor.GetSegmentsByProviderKey(flightSolution, providerKey);
            var fareRulesCache = _fareRulesCache.Get(providerKey);
            var combinedFareRulesCache = new FareRulesCacheEntry
            {
                FareRules = fareRulesCache?.FareRules ?? new Dictionary<string, List<FareRuleSection>>(),
                CancellationTimeline = fareRulesCache?.CancellationTimeline ?? new List<ConditionsTimespan>(),
                ChangeTimeline = fareRulesCache?.ChangeTimeline ?? new List<ConditionsTimespan>(),
                PartiallyUsedChangeTimeline = fareRulesCache?.PartiallyUsedChangeTimeline ?? new List<ConditionsTimespan>(),
                Cancellations = fareRulesCache?.Cancellations is null or RefundCondition.Unknown
                    ? flightSolution.Terms?.Cancellations ?? RefundCondition.NonRefundable
                    : fareRulesCache.Cancellations,
                Changes = fareRulesCache?.Changes is null or ChangeCondition.Unknown
                    ? flightSolution.Terms?.Changes ?? ChangeCondition.NonChangeable
                    : fareRulesCache.Changes
            };

            var reservationParams = new CreateReservationParams
            {
                SearchJobId = flightSolution.SearchJobId,
                ProviderKey = providerKey,
                Source = providerKey.GetSourceFromProviderKey(),
                ReservationId = createVirtual ? Id.New() : sagaReservation.Id,
                BookingId = saga.Id,
                TenantId = saga.BookingEntity.TenantId,
                Passenger = _mapper.Map<Models.PassengerDetails>(saga.FlightSolutionEntity.Passenger),
                Metadata = MapMetadata(saga),
                ReservationMetadata = new Dictionary<string, string>(),
                RemarksMetadata = new List<Messages.Commands.Models.RemarkMetadata>(),
                LegSegments = legSegments,
                BaggageAllowances = GetProviderBaggageAllowances(providerKey, flightSolution),
                CreatedBy = _mapper.Map<CTeleport.Services.Search.Shared.Models.User>(saga.BookingEntity.CreatedBy),
                RequestId = requestId,
                IsVirtual = createVirtual,
                ApprovalRequired = approvalRequired,
                PaymentRequired = paymentConfirmedRequired,
                OriginalReservationId = sagaReservation.OriginalReservationId,
                ExemptLiTax = exemptLiTax,
                PrivateFareCode = flightSolution.PrivateFareCode,
                IsImmediateTicketing = isImmediateTicketing,
                FaresCache = combinedFareRulesCache,
                Site = _mapper.Map<CTeleport.Services.Booking.Models.Site>(saga.BookingEntity.Site)
            };

            if (flightSolution.SplitProviderODs != null)
            {
                var splitInfo = flightSolution.SplitProviderODs.Single(s => s.ProviderKey == providerKey);

                reservationParams.PlatingCarrier = splitInfo.PlatingCarrier;

                reservationParams.CanCancel = splitInfo.CanCancel;
                reservationParams.Ticketless = splitInfo.Ticketless;
                reservationParams.FareCalc = splitInfo.FareCalc;
                reservationParams.BaseCurrency = splitInfo.OriginalCcy;
                reservationParams.LatestTicketingTime = splitInfo.LatestTicketingTime;
                reservationParams.FundingSource = await GetFundingSource(splitInfo.FundingSource);

                if (flightSolution.Terms.Components.TryGetValue(splitInfo.OriginDestination, out var componentTerms))
                {
                    reservationParams.FareType = componentTerms.FareType;
                }
                else
                {
                    reservationParams.FareType = flightSolution.Terms.FareType;
                }
                reservationParams.PrivateFareCode = splitInfo.PrivateFareCode;
            }
            else
            {
                reservationParams.PlatingCarrier = flightSolution.PlatingCarrier;
                reservationParams.FareType = flightSolution.Terms.FareType;
                reservationParams.FareCalc = flightSolution.FareCalc;
                reservationParams.LatestTicketingTime = flightSolution.LatestTicketingTime;
                reservationParams.BaseCurrency = flightSolution.Price.OriginalCcy;
                reservationParams.Ticketless = !flightSolution.Terms.CanCancel;
                reservationParams.CanCancel = providerKey.IsCancelable() && flightSolution.Terms.CanCancel;
                reservationParams.FundingSource = await GetFundingSource(flightSolution.FundingSource);
            }

            reservationParams.DepartureAt = legSegments.SelectMany(s => s).Min(s => s.DepartureTimestampUtc)
                .FromUnixTime();

            var searchJob = await _searchJobService.GetSearchJobAsync(flightSolution.SearchJobId);
            if (searchJob != null)
            {
                reservationParams.SearchJobMetadata = new SearchJobMetadata
                {
                    ProhibitedPenaltyFares = searchJob.ProhibitPenaltyFares,
                    SearchJobId = searchJob.Id,
                    SearchVersion = searchJob.SearchVersion
                };
            }

            exemptLiTax = exemptLiTax && NeedExcludeLiTaxFromTotalPrice(providerKey, flightSolution.SplitProviderODs);

            var priceMetadata = CalculatePrice(providerKey, flightSolution, exemptLiTax);

            reservationParams.ExemptLiTax = exemptLiTax;

            reservationParams.Price = priceMetadata;

            reservationParams.ReservationPrice = await CalculatePrice(reservationParams, flightSolution, reservationParams.FareType);

            var frequentFlyerNumberInfo = await GetFrequentFlyerInfoAsync(saga, reservationParams, legSegments);
            reservationParams.FrequentFlyerNumbers = frequentFlyerNumberInfo.FrequentFlyerNumbers;

            if (frequentFlyerNumberInfo.AllowCorporateCodes)
            {
                reservationParams.CorporateCodes = await GetCorporatesCodesAsync(saga.BookingEntity.TenantId);
            }
            else
            {
                _logger.ForContext(nameof(reservationParams.BookingId), reservationParams.BookingId)
                    .ForContext(nameof(reservationParams.ReservationId), reservationParams.ReservationId)
                    .Information("Corporate codes are not allowed");
            }

            await SetCustomFields(reservationParams);

            reservationParams.Ancillaries = MapAncillaries(sagaReservation);

            return reservationParams;
        }

        private async Task<FundingSource> GetFundingSource(string fundingSourceId)
        {
            if (fundingSourceId is null)
            {
                return null;
            }
            
            var fundingSource = await _providersClient.GetFundingSourceAsync(fundingSourceId);
            return new FundingSource
            {
                Id = fundingSource.FundingSource,
                Type = fundingSource.FundingSourceType.ToString()
            };
        }

        private bool IsVirtualReservation(string providerKey, bool approvalRequired, bool paymentConfirmedRequired, bool isImmediateTicketing)
        {
            // if AGW and approval required, always create virtual
            if (providerKey.IsAirgateway() && approvalRequired)
            {
                return true;
            }
            
            return _approvalQueueOptions.SkipApprovalRequiredFlow
                ? approvalRequired
                : (paymentConfirmedRequired || approvalRequired) && isImmediateTicketing;
        }

        private static Metadata MapMetadata(BookingSaga saga)
            => new()
            {
                VesselName = saga.FlightSolutionEntity.VesselName,
                VesselFlag = saga.FlightSolutionEntity.VesselFlag,
                CrewChangeAirport = saga.FlightSolutionEntity.CrewChangeAirport,
                CrewChangeDate = saga.FlightSolutionEntity.CrewChangeDate,
                CrewChangeMember = saga.FlightSolutionEntity.CrewChangeMember,
                ExemptLiTax = saga.BookingEntity.Price.IsLiTaxApplied,
                CustomFields = saga.FlightSolutionEntity.CustomFields.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            };

        private static List<Models.Ancillary> MapAncillaries(ReservationEntity reservation)
            => reservation.Ancillaries
                .Select(x => new Models.Ancillary
                {
                    Key = x.AncillaryKey,
                    Description = x.Description,
                    Ccy = x.Currency,
                    Price = x.Price,
                    TotalPrice = x.TotalPrice,
                    UserCurrency = x.UserCurrency,
                    Markup = x.Markup,
                    Type = x.Type.ToString(),
                    Weight = x.Weight,
                    SegmentRefs = x.SegmentRefs,
                    SolutionProviderKey = reservation.ProviderKey
                })
                .ToList();

        private static IDictionary<string, CTeleport.Services.FrequentFlyer.Models.FrequentFlyerNumber> MapFrequentFlyerNumbers(
            IReadOnlyCollection<FreqFlyerNumEntity> freqFlyerNumEntities)
            => freqFlyerNumEntities
                .ToDictionary(
                    x => x.ProgramComponent,
                    x => new CTeleport.Services.FrequentFlyer.Models.FrequentFlyerNumber
                    {
                        Code = x.Code,
                        Carrier = x.Carrier,
                        Number = x.Number,
                        ProgramName = x.ProgramName
                    });

        private async Task<FrequentFlyerInfoForReservation> GetFrequentFlyerInfoAsync(BookingSaga saga, CreateReservationParams reservationParams, IList<IList<FlightSegment>> legSegments)
        {
            var frequentFlyerInfo = await _frequentFlyerService.GetForReservationAsync(new FrequentFlyerForReservationRequest
                {
                    NumbersByComponentKey = MapFrequentFlyerNumbers(saga.BookingEntity.FreqFlyerNums),
                    PlatingCarrier = reservationParams.PlatingCarrier,
                    LegSegments = legSegments
                });

            if (!frequentFlyerInfo.FrequentFlyerNumbers.Any())
            {
                _logger.ForContext(nameof(reservationParams.BookingId), reservationParams.BookingId)
                    .ForContext(nameof(reservationParams.ReservationId), reservationParams.ReservationId)
                    .Information("Frequent flyer info is empty for reservation");
            }

            return frequentFlyerInfo;
        }

        private Dictionary<string, BaggageAllowance> GetProviderBaggageAllowances(string providerKey, FlightSolution flightSolution)
        {
            if (flightSolution.Terms.Splitting)
            {
                var providerOD = flightSolution
                    .SplitProviderODs
                    .Where(od => od.ProviderKey == providerKey)
                    .Select(od => od.OriginDestination)
                    .First();

                return new Dictionary<string, BaggageAllowance> { { providerOD, flightSolution.Baggage[providerOD] } };
            }
            else
            {
                return flightSolution.Baggage;
            }
        }

        private decimal GetValue(string key, decimal value, ICollection<Component> components)
        {
            return components == null || !components.Any(c => c.Key.Equals(key))
                ? value
                : components.First(c => c.Key.Equals(key)).Amount;
        }

        private async Task<Dictionary<string, string>> GetCorporatesCodesAsync(string tenantId)
        {
            try
            {
                return await _settingsService.GetTenantCorporateCodesAsync(tenantId);
            }
            catch (Exception ex)
            {
                // NOTE: as a safety measure, just log the error, but let create reservation transaction continue
                _logger.Error(ex, "Could not get corporates codes for {Tenant}", tenantId);
                return new Dictionary<string, string>();
            }
        }
        private CreateReservationPrice CalculatePrice(string providerKey, FlightSolution flightSolution, bool exemptLiTax)
        {
            var markup = GetValue(providerKey, flightSolution.Price.Markup, flightSolution.Price.MarkupComponents);

            var quotedPrice = GetValue(providerKey, flightSolution.Price.Total, flightSolution.Price.TotalPriceComponents);

            var totalTaxes = GetValue(providerKey, flightSolution.Price.TotalTaxes ?? 0, flightSolution.Price.TotalTaxesComponents);

            var providerOD = flightSolution.SplitProviderODs?.FirstOrDefault(od => od.ProviderKey == providerKey);

            if (exemptLiTax)
            {
                quotedPrice -= flightSolution.Price.LiTax;
                totalTaxes -= flightSolution.Price.LiTax;
            }

            return new CreateReservationPrice
            {
                Currency = flightSolution.Price.Ccy,
                OriginalCurrency = providerOD?.OriginalCcy ?? flightSolution.Price.OriginalCcy,
                TotalTaxes = totalTaxes,
                Markup = markup,
                MarkupCurrency = flightSolution.Price.MarkupCurrency,
                QuotedPrice = quotedPrice
            };
        }

        private bool NeedExcludeLiTaxFromTotalPrice(string providerKey, List<SplitProviderOD> providerODs)
        {
            if (providerODs == null || providerODs.Count == 1)
            {
                return true;
            }
            var providerOD = providerODs.FirstOrDefault(x => x.ProviderKey == providerKey);
            if (providerOD?.OriginDestination != null)
            {
                return providerOD.OriginDestination.StartsWith("MNL");
            }

            return false;
        }
    }
}
