using System.Threading.Tasks;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Search.Shared.Models;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Services
{
    public interface IReservationBuilder
    {
        Task<CreateReservationParams> BuildReservationParams(string requestId, string providerKey, FlightSolution flightSolution, BookingSaga saga,
            ReservationEntity sagaReservation, bool exemptLiTax);
    }
}