using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Helpers;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Services
{
    public class RetrieveSupplierLocatorsHandlerService : IRetrieveSupplierLocatorsHandlerService
    {
        private readonly IProviderReservationInfoService _providerReservationService;
        private readonly IBookingMetricsService _metricsService;
        private readonly IBookingService _bookingService;
        private readonly ILogger _logger;

        public RetrieveSupplierLocatorsHandlerService(
            IProviderReservationInfoService providerReservationService,
            IBookingMetricsService metricsService,
            IBookingService bookingService,
            ILogger logger)
        {
            _providerReservationService = providerReservationService;
            _metricsService = metricsService;
            _bookingService = bookingService;
            _logger = logger;
        }

        public async Task SetSupplierLocatorsForAllReservationsWithoutItAsync()
        {
            var reservations = (await _bookingService.GetAllReservationsWithoutSupplierLocatorsAsync()).ToArray();

            foreach (var reservation in reservations)
            {
                await SetSupplierLocatorAsync(reservation);
            }
        }

        private async Task SetSupplierLocatorAsync(Reservation reservation)
        {
            try
            {
                var providerCode = LocatorsHelper.GetProviderCode(reservation.Locators);
                _logger.Information("Setting supplier locator for reservation {Locator} (TenantId: {TenantId}, Source: {Source})",
                    providerCode, reservation.TenantId, reservation.Source);

                var request = new ProviderRetrieveReservationRequest
                {
                    Source = reservation.Source,
                    Locators = reservation.Locators
                };

                var flightReservation = await _providerReservationService.GetReservationAsync(request);

                if (flightReservation == null)
                {
                    _logger.Warning("Could not retrieve reservation");
                    return;
                }

                _metricsService.IncrementProviderValidationRequestCounter(
                reservation?.TenantId ?? string.Empty, reservation?.Source ?? string.Empty, flightReservation?.HasError ?? false);

                if (flightReservation.FlightReservation.SupplierLocators.Any(x => x.Value != null))
                {
                    await _bookingService.SetSupplierLocatorsAsync(reservation.Id,
                        flightReservation.FlightReservation.SupplierLocators);
                }
                else
                {
                    _logger.Warning("Could not retrieve supplier locators for reservation");
                }
            }
            catch (Exception e)
            {
                _logger.Error(e, "[{RetrieveSupplierLocatorsHandlerService}] An error occoured when setting the supplier locator", nameof(RetrieveSupplierLocatorsHandlerService));
            }
        }
    }
}
