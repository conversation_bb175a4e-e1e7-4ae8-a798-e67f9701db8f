using CTeleport.Services.Booking.Configuration;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Events;

namespace CTeleport.Services.Booking.Services;

public sealed class ClassDropOptionsService : IClassDropOptionsService
{
    private readonly IOptions<ClassDropOptions> _classDropOptions;
    private readonly ILogger _logger;

    public ClassDropOptionsService(IOptions<ClassDropOptions> classDropOptions, ILogger logger)
    {
        _classDropOptions = classDropOptions;
        _logger = logger;
    }

    public bool CanRebookTicketedPnr(string carrier)
    {
        var options = _classDropOptions.Value;
        if (options.Ticketed?.AllowedCarriers is null)
        {
            _logger.Warning("Configuration for ticketed class drop is not set");
            return false;
        }
        
        var isAllowed = options.Ticketed.AllowedCarriers.Contains(carrier);
        
        WriteVerboseLog(carrier, isAllowed);
        
        return isAllowed;
    }

    private void WriteVerboseLog(string carrier, bool isAllowed)
    {
        if (_logger.IsEnabled(LogEventLevel.Verbose))
        {
            if (isAllowed)
            {
                _logger.Verbose("Carrier {Carrier} is allowed to rebook ticketed PNR", carrier);
            }
            else
            {
                _logger.Verbose("Carrier {Carrier} is not allowed to rebook ticketed PNR", carrier);
            }
        }
    }
}