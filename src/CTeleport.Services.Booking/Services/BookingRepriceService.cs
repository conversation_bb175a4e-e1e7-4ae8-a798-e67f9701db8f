using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using System.Threading.Tasks;
using CTeleport.Common.Authorization.Constants;
using CTeleport.Common.Exceptions;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Search.Shared.Models;
using Serilog;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands;
using CTeleport.Common.Helpers;
using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.SearchProxy.Services;

namespace CTeleport.Services.Booking.Services
{
    public class BookingRepriceService : IBookingRepriceService
    {
        private readonly IFlightSolutionService _flightSolutionService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;
        private readonly IBookingService _bookingService;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IMapper _mapper;

        private const string _rejectBookingReason = "reprice_failed";

        public BookingRepriceService(IFlightSolutionService flightSolutionService, IMessageDispatcher dispatcher, ILogger logger,
            IBookingService bookingService, IBookingSagaService bookingSagaService, IMapper mapper)
        {
            _flightSolutionService = flightSolutionService;
            _dispatcher = dispatcher;
            _logger = logger;
            _bookingService = bookingService;
            _bookingSagaService = bookingSagaService;
            _mapper = mapper;
        }

        public async Task<FlightSolutionDetails> RepriceBooking(string searchAuthToken, string bookingId)
        {
            var booking = await _bookingService.GetBookingAsync(bookingId);

            if (booking == null)
            {
                throw new ValidationException($"Couldn't find booking {bookingId}");
            }

            var reservations = (await GetActiveReservations(bookingId)).ToArray();
            var providerAlternativeRequests = _mapper.Map<IList<AlternativeFlightSolutionsRequest>>(reservations.ToArray());

            _logger.Information("Repricing Booking {BookingId}. {Requests} Alternative Flight Solution Requests.", bookingId, providerAlternativeRequests.Count);

            var bookingSaga = await _bookingSagaService.GetAsync(bookingId);

            var sourceFlightSolution = new FlightSolution
            {
                LegSegments = _mapper.Map<FlightSolution>(bookingSaga).LegSegments,
                SearchId = booking.SearchId,
                SearchJobId = reservations.First().SearchJobMetadata?.SearchJobId,
                RouteId = booking.RouteId,
                Price = new FlightSolutionPrice()
                {
                    // TODO: Add support for multiple OriginialCcy (For cross PCC spliting)
                    OriginalCcy = reservations.First().Price.OriginalCurrency,
                    Ccy = booking.Price.Currency
                },
                Terms = new FlightSolutionTerms()
                {
                    Splitting = booking.Terms.Splitting
                }
            };

            var searchContext = new SearchContext
            {
                NeedTravelPolicy = false,
                Tenant = booking.TenantId,
                VesselName = booking.Metadata.VesselName,
                OriginalCcy = reservations.First().Price.OriginalCurrency,
                Ccy = booking.Price.Currency,
                Source = providerAlternativeRequests.First().Source
            };

            var result = await _flightSolutionService.RunFlightSolutionAlternativeAsync(searchAuthToken, providerAlternativeRequests, sourceFlightSolution, searchContext);
            var resultFlightSolution = result.First();
            var errorResult = result.FirstOrDefault(e => !string.IsNullOrEmpty(e.ErrorCode));

            if (errorResult != null)
            {
                _logger.ForContext("BookingId", booking.Id).ForContext("ErrorDetails", errorResult)
                    .Warning("Repricing has failed for booking");

                await _dispatcher.DispatchWithSystemContextAsync(new RejectBooking
                {
                    Request = new Request { Id = Id.New() },
                    BookingId = booking.Id,
                    Reason = _rejectBookingReason,
                    Comment = errorResult.ErrorMessage,
                    User = new Messages.Commands.Models.User
                    {
                        Id = UserRoles.System,
                        Name = UserRoles.System,
                        Email = $"{UserRoles.System}@cteleport.com",
                        TenantId = booking.TenantId
                    }
                });

                return resultFlightSolution;
            }

            // Remove LiTax from TotalPrice if needed
            if (booking.Metadata?.ExemptLiTax == true && resultFlightSolution.FlightSolution.CanExemptLiTax == true)
            {
                resultFlightSolution.FlightSolution.Price.Net -= resultFlightSolution.FlightSolution.Price.OriginalLiTax;
                resultFlightSolution.FlightSolution.Price.Total -= resultFlightSolution.FlightSolution.Price.LiTax;
            }

            return resultFlightSolution;
        }

        private async Task<IEnumerable<Reservation>> GetActiveReservations(string bookingId)
        {
            var reservations = (await _bookingService.GetBookingReservationsAsync(bookingId)).ToArray();

            return reservations.Where(r => r.State == Enums.ReservationState.Active);

            // return reservations.GroupBy(i => i.LegSegments.SelectMany(l => l).Select(s => $"{s.Origin}-{s.Destination}"))
            //     .SelectMany(t => t)
            //     .Where(y => !y.IsVirtual);
        }
    }
}