using System;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Models;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Services
{
    public class BookingPaymentService : IBookingPaymentService
    {
        private readonly IBookingService _bookingService;
        private readonly IBillingClient _billingClient;
        private readonly ILogger _logger;

        private const string _paymentFailedCode = "payment_failed";

        public BookingPaymentService(IBookingService bookingService, IBillingClient billingClient, ILogger logger)
        {
            _bookingService = bookingService;
            _billingClient = billingClient;
            _logger = logger;
        }

        public async Task<BookingPaymentDetails> CreatePaymentAsync(BookingSaga saga)
        {
            var activeRes = saga.Reservations.FirstOrDefault(r => r.State is ReservationState.Active);
            var virtualRes = saga.Reservations.FirstOrDefault(r => r.State is ReservationState.Virtual);

            var tenantId = saga.BookingEntity.TenantId;
            var bookingLocator = activeRes?.Locator ?? virtualRes?.OriginalLocator; // TODO: multiple locators support?
            var paymentMethodId = saga.BillingEntity.PaymentMethodId;
            var requestOrigin = saga.BookingEntity.RequestInfo;
            var price = saga.BookingEntity.Price;
            var screenResolution = saga.BillingEntity.ScreenResolution is not null
                ? new ScreenResolutionDto(saga.BillingEntity.ScreenResolution.Width, saga.BillingEntity.ScreenResolution.Height)
                : null;

            var ancillaries = saga.Reservations
                .Where(r => r.Ancillaries?.Any() ?? false)
                .SelectMany(r => r.Ancillaries)
                .ToList();

            var ancillariesTotal = ancillaries.Sum(a => a.TotalPrice);

            if(ancillaries.Any())
            {
                _logger.Information(
                "Adding to payment {SelectedAncillariesCount} ancillaries with {SelectedAncillariesTotalAmount} {Currency}, markup must be included",
                ancillaries.Count, ancillariesTotal, ancillaries.FirstOrDefault()?.UserCurrency);
                
            }

            var totalAmount = price.Total + ancillariesTotal;

            try
            {
                var payment = await _billingClient.MakeCreditCardPaymentAsync(new MakeCreditCardPaymentRequest
                {
                    TenantId = tenantId,
                    CardId = paymentMethodId,
                    Amount = totalAmount,
                    Currency = price.Currency,
                    Reference = bookingLocator,
                    IpAddress = requestOrigin.IpAddress,
                    ScreenResolution = screenResolution,
                });

                return new BookingPaymentDetails
                {
                    Id = payment.Id,
                    PaymentConfirmationRequired = payment.PaymentConfirmationRequired,
                    ExpiresAt = payment.ExpiresAt
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to create credit card payment for {Tenant} with card {CardId}", tenantId, paymentMethodId);
                if (ex is ExternalServiceException se)
                    throw new ServiceException(_paymentFailedCode, se.Message);
                else
                    throw new ServiceException(_paymentFailedCode, "Credit card payment was declined.");
            }
        }

        public async Task<BookingPaymentDetailsDto> GetPaymentDetailsAsync(string bookingId)
        {
            var booking = await GetBookingAsync(bookingId);

            if (booking.PaymentMethod != Enums.PaymentMethod.CreditCard)
                return new BookingPaymentDetailsDto
                {
                    PaymentMethod = booking.PaymentMethod
                };
  
            var payment = await _billingClient.GetCreditCardPaymentDetailsAsync(booking.PaymentId);
            return new BookingPaymentDetailsDto
            {
                PaymentMethod = booking.PaymentMethod,
                PaymentConfirmationRequired = payment.PaymentConfirmationRequired,
                PaymentId = payment.Id
            };
        }

        public async Task ConfirmPaymentAsync(string bookingId)
        {
            var booking = await GetBookingAsync(bookingId);

            if (booking.PaymentMethod == Enums.PaymentMethod.CreditCard)
            {
                try
                {
                    await _billingClient.ConfirmCreditCardPaymentAsync(booking.PaymentId);
                    return;
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Failed to confirm credit card payment for {BookingId}", bookingId);
                    if (ex is ExternalServiceException se)
                        throw new ServiceException(_paymentFailedCode, se.Message);
                    else
                        throw new ServiceException(_paymentFailedCode, "Credit card payment was declined.");
                }
            }

            throw new NotSupportedException($"Payment confirmation is not supported for {booking.PaymentMethod}");
        }

        public async Task CancelPaymentAsync(string bookingId)
        {
            var booking = await GetBookingAsync(bookingId);

            if (booking.PaymentMethod == Enums.PaymentMethod.CreditCard)
            {
                await CancelPaymentByPaymentIdAsync(booking.PaymentId);
                return;
            }

            throw new NotSupportedException($"Payment cancellation is not supported for {booking.PaymentMethod}");
        }

        public async Task CancelPaymentByPaymentIdAsync(string paymentId)
        {
            await _billingClient.CancelCreditCardPaymentAsync(paymentId);
        }

        private async Task<Models.Booking> GetBookingAsync(string bookingId)
        {
            var booking = await _bookingService.GetBookingAsync(bookingId);
            if (booking == null) throw new NotFoundException("Booking not found");
            return booking;
        }
    }
}