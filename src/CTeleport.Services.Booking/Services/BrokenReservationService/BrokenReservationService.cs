using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Helpers;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Models;
using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Helpers;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Services
{
    public class BrokenReservationService : IBrokenReservationService
    {
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IBookingService _bookingService;
        private readonly ILogger _logger;

        public BrokenReservationService(
            IBookingSagaService bookingSagaService,
            IMessageDispatcher dispatcher,
            IBookingService bookingService,
            ILogger logger)
        {
            _bookingSagaService = bookingSagaService;
            _dispatcher = dispatcher;
            _bookingService = bookingService;
            _logger = logger;
        }

        public async Task ReservationBroken(string bookingId, string reservationId, string requestId,
            Messages.Commands.Models.User user)
        {
            var saga = await _bookingSagaService.GetAsync(bookingId);

            var brokenReservation = saga.Reservations.FirstOrDefault(r => r.Id == reservationId);
            if (brokenReservation != null)
            {
                _logger
                    .ForContext("BookingId", saga.Id)
                    .ForContext("ReservationId", brokenReservation.Id)
                    .Information("Processing broken reservation flow...");

                await _bookingSagaService.RemoveReservationAsync(saga.Id, brokenReservation.Id);
                await _bookingSagaService.AddReservationAsync(new AddSagaReservationRequest
                {
                    BookingSagaId = saga.Id,
                    ProviderKey = brokenReservation.ProviderKey,
                    Ancillaries = brokenReservation.Ancillaries
                });
            }

            await _dispatcher.DispatchAsync(new CancelReservation
            {
                Request = Request.New<CancelReservation>(requestId),
                ReservationId = reservationId,
                User = user
            });
        }

        private async Task<Reservation> CreateVirtualCopy(Reservation reservation)
        {
            var clone = reservation.Clone();

            clone.Id = Id.New();
            clone.IsVirtual = true;
            clone.ApprovalRequired = true;
            clone.SupplierLocators = null;
            clone.Locators = null;
            await _bookingService.CreateReservation(clone);

            return clone;
        }
    }
}