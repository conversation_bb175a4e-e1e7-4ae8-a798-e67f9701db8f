using System;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Models;

namespace CTeleport.Services.Booking.Services;

public interface IReservationRefundService
{
    /// <summary>
    /// Calculates refund amount for reservation. 
    /// </summary>
    /// <param name="refundedReservation">Refunded reservation</param>
    /// <param name="ticketNumber">Refunded ticket number</param>
    /// <param name="ticketRefundDto">Ticket refund data to be processed</param>
    /// <param name="refundedAt">Refund date</param>
    Task SetTicketAsRefundedAsync(Reservation refundedReservation, 
        string ticketNumber,
        TicketRefundDto ticketRefundDto,
        DateTime refundedAt);
}