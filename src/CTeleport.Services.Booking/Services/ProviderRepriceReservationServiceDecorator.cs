using System.Threading.Tasks;
using Autofac.Features.Indexed;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;

namespace CTeleport.Services.Booking.Services
{
    public class ProviderRepriceReservationServiceDecorator : IProviderRepriceReservationService
    {
        private readonly IIndex<string, IProviderRepriceReservationService> _serviceFactory;

        public ProviderRepriceReservationServiceDecorator(IIndex<string, IProviderRepriceReservationService> connectionFactory)
        {
            _serviceFactory = connectionFactory;
        }

        public Task<ProviderFareRefreshResponse> RefreshFareMaskAsync(ProviderFareRefreshRequest request)
        {
            var service = _serviceFactory[request.Source.Substring(0, 2)];
            return service.RefreshFareMaskAsync(request);
        }

        public Task<ProviderRepriceReservationResponse> RepriceReservationAsync(ProviderRepriceReservationRequest request)
        {
            var service = _serviceFactory[request.Source.Substring(0, 2)];
            return service.RepriceReservationAsync(request);
        }
    }
}
