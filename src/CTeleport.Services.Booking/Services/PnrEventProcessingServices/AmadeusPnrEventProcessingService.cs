using CTeleport.Messages;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Constants;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Helpers;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Services.PnrEventProcessingServices
{
    public class AmadeusPnrEventProcessingService : PnrEventProcessingServiceBase, IPnrEventProcessingService
    {
        public AmadeusPnrEventProcessingService(
            IProviderReservationInfoService providerReservationService,
            IBookingMetricsService metricsService,
            IBookingService bookingService,
            ILogger logger)
            : base(providerReservationService, metricsService, bookingService, logger)
        {
        }

        public async Task<IList<IMessage>> ProcessPnrEventAsync(PNRPlacedOnQueue @event, Reservation reservation)
        {
            var messagesToDispatch = new List<IMessage>();
            if (@event.TicketingAt.HasValue)
            {
                var locator = LocatorsHelper.GetProviderCode(reservation.Locators);
                var ticketingAtEvent = await base.SetTicketingTimeAsync(locator, @event.TicketingAt.Value, reservation);
                
                if (ticketingAtEvent != null)
                    messagesToDispatch.Add(ticketingAtEvent);
            }

            var hasFlightTimeChanged = HasFlightTimeChanged(@event);

            if (hasFlightTimeChanged)
            {
                var timeChangedMessages = await base.CreateTimeChangedMessagesAsync(@event, reservation, false);
                messagesToDispatch.AddRange(timeChangedMessages);
            }
            
            return messagesToDispatch;
        }

        private bool HasFlightTimeChanged(PNRPlacedOnQueue @event)
            => @event.Queue.Equals(AmadeusQueueNumbers.SCHEDULED_CHANGED)
                && @event.QueueTitle.Equals(AmadeusQueueTitles.SCHEDULED_CHANGED_TITLE)
                && !IsFlightCancelled(@event);

        private bool IsFlightCancelled(PNRPlacedOnQueue @event)
            => @event.VendorRemarks?.Any(i => i.Item2?.Contains(AmadeusQueueDescriptionKeywords.FLIGHT_CANCELLED) ?? false) ?? false;
    }
}
