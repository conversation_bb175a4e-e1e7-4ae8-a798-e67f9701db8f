using CTeleport.Messages;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Services.PnrEventProcessingServices
{
    /// <summary>
    /// This service contains logic related with PNR event processing.
    /// </summary>
    public interface IPnrEventProcessingService
    {
        /// <summary>
        /// Process PNR event which results in a list of messages to be dispatched.
        /// </summary>
        /// <param name="event">PNRPlacedOnQueue event</param>
        /// <param name="reservation">Reservation object</param>
        /// <returns>List of messages to be dispatched</returns>
        Task<IList<IMessage>> ProcessPnrEventAsync(PNRPlacedOnQueue @event, Reservation reservation);
    }
}
