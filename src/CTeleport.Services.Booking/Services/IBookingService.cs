using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Search.Shared.Models;
using ProviderFlightReservation = CTeleport.Services.Booking.Shared.Models.ProviderFlightReservation;
using User = CTeleport.Services.Search.Shared.Models.User;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Services
{
    public interface IBookingService
    {
        Task AddBookingAsync(Models.Booking booking);

        /// <summary>
        /// Set property PaymentRequired for given reservation
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="paymentRequired"></param>
        Task SetPaymentRequiredAsync(string reservationId, bool paymentRequired);

        /// <summary>
        /// Sets ticketing time for specified reservation
        /// </summary>
        /// <param name="reservationId">Reservation id</param>
        /// <param name="ticketingTime">Ticketing time, UTC</param>
        Task SetTicketingTimeAsync(string reservationId, DateTime? ticketingTime);

        /// <summary>
        /// Sets refresh fare time for specified reservation
        /// </summary>
        /// <param name="reservationId">Reservation id</param>
        /// <param name="refreshFareAt">Refresh fare time, UTC</param>
        /// <param name="adjustToOfficeHours">When true fare refresh time will be adjusted to office hours</param>
        Task SetRefreshFareTimeAsync(string reservationId, DateTime refreshFareAt, bool adjustToOfficeHours = true);

        /// <summary>
        /// Returns booking by id
        /// </summary>
        /// <param name="id">Booking id</param>
        /// <returns>Booking</returns>
        Task<Models.Booking> GetBookingAsync(string id);

        /// <summary>
        /// Returns active bookings with issued tickets, with scheduled departure time more than specified number of minutes in the past
        /// </summary>
        /// <param name="minutes">Number of minutes in the past for booking's scheduled departure</param>
        /// <returns>List of bookings</returns>
        Task<IEnumerable<Models.Booking>> GetRecentlyDepartedBookingsAsync(int minutes);

        /// <summary>
        /// Returns reservations belonging to a booking
        /// </summary>
        /// <param name="bookingId">Booking id</param>
        /// <returns>List of reservations</returns>
        Task<IEnumerable<Models.Reservation>> GetBookingReservationsAsync(string bookingId);

        /// <summary>
        /// Returns reservation specified by id
        /// </summary>
        /// <returns>Reservation</returns>
        Task<Models.Reservation> GetReservationAsync(string id);
        
        /// <summary>
        /// Returns reservation specified by id with enriched data
        /// </summary>
        /// <param name="id">ID of the reservation</param>
        /// <returns>Reservation</returns>
        Task<Models.Reservation> GetEnrichedReservationAsync(string id);

        /// <summary>
        /// Returns reservation by ticket number
        /// </summary>
        /// <param name="ticketNumber">Ticket number</param>
        /// <returns>Reservation</returns>
        Task<Models.Reservation> GetReservationByTicketNumberAsync(string ticketNumber);

        /// <summary>
        /// Returns reservation by provider locator and passenger name. 
        /// Provider locator can be repeated for different reservations. It is necessary to check passenger last name to get the right reservation.
        /// If passenger last name is not provider, the method will respond with most recent reservation matching locator
        /// </summary>
        /// <param name="locator">Provider locator</param>
        /// <param name="lastname">Passenger last name (optional)</param>
        Task<Reservation> GetReservationByLocatorAsync(string locator, string lastname = null);

        /// <summary>
        /// Returns essential booking details by booking id
        /// </summary>
        /// <param name="id">Booking id></param>
        /// <returns>Booking with essential details</returns>
        Task<Dto.BaseBookingDto> GetBaseBookingAsync(string id);
        
        /// <summary>
        /// Returns booking saga by booking id
        /// </summary>
        /// <param name="id">Booking id></param>
        /// <returns>Booking saga with essential details</returns>
        Task<BookingSagaDto> GetBookingSagaAsync(string id);

        /// <summary>
        /// Returns all bookings with essential details. The result will be filtered by auth scopes
        /// /// </summary>
        /// <param name="startFrom"></param>
        /// <param name="endAt"></param>
        /// <returns>List of bookings</returns>
        Task<IEnumerable<BaseBookingDto>> GetBookingsScopedAsync(DateTime? startFrom, DateTime? endAt = null);

        /// <summary>
        /// Returns bookings batch with essential details. The result will be filtered by auth scopes
        /// /// </summary>
        /// <param name="startFrom"></param>
        /// <param name="endAt"></param>
        /// <param name="batchSize"></param>
        /// <returns>List of bookings with next request date</returns>
        Task<BookingsBatchDto> GetBookingsBatchAsync(BookingsPagingToken startFrom, BookingsPagingToken endAt, int batchSize);


        /// <summary>
        /// Returns all bookings with essential details. For internal usage only
        /// </summary>
        /// <param name="startFrom"></param>
        /// <param name="endAt"></param>
        /// <param name="tenantId">Optional argument to retrieve the specified tenant bookings</param>
        /// <returns>List of bookings</returns>
        Task<IEnumerable<BaseBookingDto>> GetBookingsAsync(DateTime startFrom, DateTime endAt, string tenantId = null);

        /// <summary>
        /// Returns booking updates
        /// </summary>
        Task<UpdatesBatch<BookingUpdateDto>> GetBookingsUpdatesAsync(int limit, UpdatesBatchResumeToken resumeToken);

        /// <summary>
        /// Returns reservation updates
        /// </summary>
        Task<UpdatesBatch<ReservationUpdateDto>> GetReservationsUpdatesAsync(int limit, UpdatesBatchResumeToken resumeToken);

        /// <summary>
        /// Returns all bookings made within search specified by searchId
        /// </summary>
        /// <returns>List of bookings</returns>
        Task<IEnumerable<Dto.BaseBookingDto>> GetBookingsForSearchIdAsync(string searchId);

        /// <summary>
        /// Returns all bookings for specific tenant
        /// </summary>
        /// <returns>List of bookings</returns>
        Task<IEnumerable<Dto.CompleteBookingDto>> GetBookingsForTenantAsync(string tenantId);

        /// <summary>
        /// Returns first booking with specified flight solution
        /// </summary>
        /// <param name="flightSolutionId">Flight solution id</param>
        /// <returns>Booking or <c>null</c> if it hasn't been found</returns>
        Task<Models.Booking> GetBookingByFlightSolutionAsync(string flightSolutionId);

        /// <summary>
        /// Returns active bookings for a passenger with specific name and last name around specific departure date
        /// </summary>
        /// <param name="firstName">Passenger first name</param>
        /// <param name="lastName">Passenger last name</param>
        /// <param name="departureDate">Departure date, in 'yyyy-MM-dd' format</param>
        /// <param name="range">No. of days to look before and after the departure date</param>
        /// <returns>List of active bookings</returns>
        Task<IEnumerable<Dto.BaseBookingDto>> GetBookingsForPassengerAsync(string firstName, string lastName, string departureDate, int range = 2);

        /// <summary>
        /// Returns full booking details by booking id
        /// </summary>
        /// <param name="id">Booking id></param>
        /// <returns>Booking with complete details including reservations, tickets, fare rules</returns>
        Task<Dto.CompleteBookingDto> GetCompleteBookingAsync(string id);

        /// <summary>
        /// Get Booking by reservationId.
        /// </summary>
        /// <param name="id">Reservation Id</param>
        /// <returns>Booking</returns>
        Task<BaseBookingDto> GetByReservationId(string id);

        /// <summary>
        /// Get void time for given reservation, in case of no ticket, project void time is returned as
        /// if ticket will be issued now
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <param name="issuedAt">NOTE: param to support unit test scenario with reservation with no ticket</param>
        /// <returns>Actual or projected last void time</returns>
        DateTime GetLastVoidAt(Reservation reservation, DateTime? issuedAt = null);

        /// <summary>
        /// Returns booking by id with minimum details that can be enclosed with public
        /// </summary>
        /// <param name="id">Booking id></param>
        /// <returns>Booking with some details including reservations and ticket numbers</returns>
        Task<Dto.PublicBookingDto> GetPublicBookingAsync(string id);
        
        /// <summary>
        /// Returns booking by id with details needed to build itinerary receipt
        /// </summary>
        /// <param name="id">Booking id></param>
        /// <returns>Booking with details needed to build itinerary receipt</returns>
        Task<Dto.ItineraryReceiptBookingDto> GetItineraryReceiptBookingAsync(string id);

        /// <summary>
        /// Returns booking by id with minimum details that can be enclosed with public
        /// </summary>
        /// <param name="ids">Booking ids</param>
        /// <returns>Booking with some details including reservations and ticket numbers</returns>
        Task<IEnumerable<OfflineBookingDto>> GetArchiveBookingsBatchAsync(IEnumerable<string> ids);

        /// <summary>
        /// Returns all reservations to be ticketed at this moment of time
        /// (active reservations with TicketingAt before System.Date.UtcNow)
        /// </summary>
        /// <returns>List of reservations for ticketing</returns>
        Task<IEnumerable<Models.Reservation>> GetReservationsToTicketAsync();

        /// <summary>
        /// Retrieves all possible reservations for which a ticket may be issued within given bookings
        /// </summary>
        /// <param name="bookingIds">Booking ids</param>
        /// <returns>Collection of reservations</returns>
        Task<IReadOnlyCollection<Reservation>> GetReservationsToTicketAsync(IEnumerable<string> bookingIds);

        /// <summary>
        /// Returns all reservations with fare to be updated
        /// (active reservations with RefreshFareAt before System.Date.UtcNow)
        /// </summary>
        /// <returns>List of reservations scheduled for fare update</returns>
        Task<IEnumerable<Models.Reservation>> GetReservationsToRefreshFareAsync();

        /// <summary>
        /// Add newly issued ticket to the reservation
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <param name="ticketNumber">Ticket number</param>
        /// <param name="ticketPrice">Net ticket price</param>
        /// <param name="issueDate">Ticket issue date in yyyy-mm-dd format</param>
        /// <param name="fareCalc">Ticket fare calc</param>
        /// <param name="issuedAt">Ticket issue date and time, UTC</param>
        /// <param name="taxes">Ticket tax rates grouped by tax category</param>
        /// <param name="taxDetails">PFC tax details</param>
        /// <param name="fundingSource"></param>
        Task AddTicketToReservationAsync(Reservation reservation, string ticketNumber, decimal ticketPrice,
            string issueDate, string fareCalc, DateTime issuedAt, IDictionary<string, decimal> taxes,
            IList<TaxDetail> taxDetails, string fundingSource);

        /// <summary>
        /// Sets ticket as used for specified reservation 
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <param name="ticketNumber">Ticket number</param>
        Task SetTicketAsUsedAsync(Models.Reservation reservation, string ticketNumber);

        /// <summary>
        /// Marks reservation with failed ticketing
        /// </summary>
        /// <param name="reservation">Reservation</param>
        Task SetTicketAsFailedAsync(Models.Reservation reservation);

        /// <summary>
        /// Sets ticket as voided for specified reservation 
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <param name="ticketNumber">Ticket number</param>
        /// <param name="voidedAt"></param>
        Task SetTicketAsVoidedAsync(Reservation reservation, string ticketNumber, DateTime voidedAt);

        /// <summary>
        /// Sets ticket as no-show for specified reservation 
        /// </summary>
        /// <param name="reservation">Reservation</param>
        /// <param name="ticketNumber">Ticket number</param>
        Task SetTicketAsNoShowAsync(Reservation reservation, string ticketNumber);

        /// <summary>
        /// Associates specified invoice number with a ticket
        /// </summary>
        /// <param name="reservationId">Reservation id</param>
        /// <param name="invoiceNumber">Invoice number</param>
        Task AddReservationInvoiceAsync(string reservationId, string invoiceNumber);

        /// <summary>
        /// Associates specified credit note number with a ticket
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="creditNoteNumber"></param>
        Task AddReservationCreditNoteAsync(string reservationId, string creditNoteNumber);

        /// <summary>
        /// Set the collection of invoices associated with specific ticket
        /// </summary>
        /// <param name="reservationId">Reservation id</param>
        /// <param name="invoiceNumbers">Invoice numbers</param>
        Task SetReservationInvoicesAsync(string reservationId, ICollection<string> invoiceNumbers);

        /// <summary>
        /// Set the collection of invoices credit notes with specific ticket
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="creditNoteNumbers"></param>
        Task SetReservationCreditNotesAsync(string reservationId, ICollection<string> creditNoteNumbers);

        /// <summary>
        /// Changes state for specified reservation
        /// </summary>
        /// <param name="reservationId">Reservation id</param>
        /// <param name="state">Reservation state</param>
        /// <param name="timestamp">State timestamp</param>
        Task ChangeReservationStateAsync(string reservationId, ReservationState state, DateTime? timestamp = null);

        /// <summary>
        /// Changes state for specified booking
        /// </summary>
        /// <param name="bookingId">Booking id</param>
        /// <param name="state">Booking state</param>
        /// <param name="user"></param>
        Task ChangeBookingStateAsync(string bookingId, BookingState state);

        Task<IEnumerable<ReservationOverview>> GetReservationsAsync(DateTime createdAfter);
        
        Task<IEnumerable<Dto.CompleteReservationDto>> GetDetailedReservationsByBookingIdAsync(string bookingId);
        Task<Dto.CompleteReservationDto> GetReservationDetailsAsync(string id);

        Task<IEnumerable<Reservation>> GetActiveReservationsAsync();

        /// <summary>
        /// Returns all reservation without supplier locators
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<Reservation>> GetAllReservationsWithoutSupplierLocatorsAsync();

        /// <summary>
        /// Sets supplier locatos for specified reservation
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="supplierLocators"></param>
        /// <returns></returns>
        Task SetSupplierLocatorsAsync(string reservationId, Dictionary<string, string> supplierLocators);

        /// <summary>
        /// Set reservation's locators.
        /// </summary>
        /// <param name="reservationId">ReservationId</param>
        /// <param name="locators">Locators object.</param>
        Task SetLocatorsAsync(string reservationId, Dictionary<string, string> locators);

        /// <summary>
        /// Mark specified booking as No-Show
        /// </summary>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task SetBookingAsNoShowAsync(string bookingId);

        /// <summary>
        /// Update reservation fare
        /// </summary>
        /// <param name="reservation"></param>
        /// <param name="newFare"></param>
        /// <param name="fareChangeReason">Reason why fare is changed</param>
        /// <returns></returns>
        Task UpdateReservationFareAsync(Reservation reservation, Fare newFare, FareChangeReason fareChangeReason);
        
        /// <summary>
        /// Update passenger details for booking
        /// </summary>
        /// <param name="bookingId"></param>
        /// <param name="passengerDetails"></param>
        /// <returns></returns>
        Task UpdateBookingPassengerAsync(string bookingId, MutablePassengerDetails passengerDetails);

        /// <summary>
        /// Update a booking comment
        /// </summary>
        /// <param name="bookingId"></param>
        /// <param name="comment"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task UpdateBookingComment(string bookingId, string comment, User user);

        /// <summary>
        /// Update reservation segments
        /// </summary>
        /// <param name="reservation"></param>
        /// <returns></returns>
        Task UpdateReservationSegmentsAsync(Reservation reservation);

        /// <summary>
        /// Update reservation irregularities
        /// </summary>
        /// <param name="reservation"></param>
        Task UpdateReservationIrregularitiesAsync(Reservation reservation);

        /// <summary>
        /// Returns active bookings for specified vessel and for specified date
        /// </summary>
        /// <param name="vesselName"></param>
        /// <param name="crewChangeDate">Date string, eg. 2018-11-06</param>
        /// <returns></returns>
        Task<IEnumerable<BaseBookingDto>> GetActiveBookingsForCrewChangeAsync(string vesselName, string crewChangeDate);

        /// <summary>
        /// Disable reservation for class drop checks
        /// </summary>
        /// <param name="reservationId"></param>
        /// <returns></returns>
        Task DisableClassDropCheck(string reservationId);

        /// <summary>
        /// Set property isAllowed for reservation on class drop
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="isAllowed"></param>
        /// <returns></returns>
        Task SetClassDropIsAllowed(string reservationId, bool isAllowed);

        /// <summary>
        /// Returns scheduled sales amount for specified source
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        Task<decimal> GetScheduledSalesVolumeAsync(string source);

        /// <summary>
        /// Returns scheduled sales amount for specified tenant
        /// </summary>
        /// <param name="tenant"></param>
        /// <returns></returns>
        Task<decimal> GetScheduledSalesVolumeByTenantAsync(string tenant);

        /// <summary>
        /// Sets reservation fare IsCheapest
        /// </summary>
        /// <param name="reservation"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task SetReservationFareIsCheapestAsync(Reservation reservation, bool? value);

        /// <summary>
        /// Sets CancelledBy for specified booking
        /// </summary>
        /// <param name="bookingId"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task SetBookingCancelledByAsync(string bookingId, User user);

        /// <summary>
        /// Create New reservation Command and Save reservation info.
        /// </summary>
        /// <param name="reservationParams"></param>
        /// <returns></returns>
        Task<ProviderCreateReservation> CreateReservationCommand(CreateReservationParams reservationParams);

        Task<Reservation> CreateReservation(Reservation draftReservation);

        Task<Reservation> UpdateReservationAsync(Reservation reservation);

        /// <summary>
        /// Creates a new virtual reservation
        /// </summary>
        /// <param name="providerKey"></param>
        /// <param name="draftReservation"></param>
        /// <param name="flightSolution"></param>
        /// <param name="exemptLiTax"></param>
        /// <param name="reservationParamsExemptLiTax"></param>
        /// <returns></returns>
        Task<Reservation> CreateVirtualReservation(string providerKey, Reservation draftReservation,
            FlightSolution flightSolution, CreateReservationParams reservationParams);

        /// <summary>
        /// Removes draft reservation that has been failed to be booked 
        /// </summary>
        Task RemoveRejectedReservation(string reservationId);

        /// <summary>
        /// Replaces draft reservation in status New with complete model
        /// </summary>
        Task<Reservation> SaveCompletedReservation(Models.Reservation reservation, ProviderFlightReservation providerReservation, BookingSaga saga, ReservationState state = ReservationState.Active);

        /// <summary>
        /// Update Pending Reservation data.
        /// </summary>
        Task<Reservation> UpdatePendingReservation(Models.Reservation reservation, ProviderFlightReservation providerReservation, BookingSaga saga);

        /// <summary>
        /// Builds a draft reservation object which can be used for further processing
        /// </summary>
        /// <param name="reservationParams"></param>
        /// <returns></returns>
        Task<Reservation> BuildDraftReservation(CreateReservationParams reservationParams);

        /// <summary>
        /// Find reservation by farerule hash
        /// </summary>
        /// <param name="fareRuleId"></param>
        /// <returns></returns>
        Task<Reservation> GetReservationByFareRuleCat16IdAsync(string fareRuleId);

        /// <summary>
        /// Set property prohibitAutoRefund for particular ticket
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="ticketNumber"></param>
        /// <param name="prohibitAutoRefund"></param>
        /// <returns></returns>
        Task SetProhibitAutoRefund(string reservationId, string ticketNumber, bool prohibitAutoRefund);

        /// <summary>
        /// Reset autorefund for reservations with specified fare rule. Returns affected reservations count.
        /// </summary>
        /// <param name="fareRuleId"></param>
        /// <param name="prohibitAutoRefund"></param>
        /// <returns></returns>
        Task<long> SetProhibitAutoRefundByFareRuleAsync(string fareRuleId, bool prohibitAutoRefund);

        /// <summary>
        /// Gets the refund/cancellation quotation for the given booking
        /// </summary>
        /// <param name="bookingId"></param>
        /// <returns></returns>
        Task<RefundQuote> GetRefundQuoteAsync(string bookingId);

        /// <summary>
        /// Set property NonRefundable for particular ticket
        /// </summary>
        /// <param name="reservationId"></param>
        /// <param name="ticketNumber"></param>
        /// <param name="nonRefundable"></param>
        /// <returns></returns>
        Task SetNonRefundableAsync(string reservationId, string ticketNumber, bool nonRefundable);

        /// <summary>
        /// Set booking's State to Rejected and save information about user who rejected it
        /// </summary>
        /// <param name="bookingId"></param>
        /// <param name="rejectedBy"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        Task SetAsDeclinedByAsync(string bookingId, User rejectedBy, string reason);

        /// <summary>
        /// Set booking's State to Confirmed and save information about user who approved it
        /// </summary>
        /// <param name="bookingId"></param>
        /// <param name="approvedBy"></param>
        /// <returns></returns>
        Task SetAsApprovedByAsync(string bookingId, params User[] approvedBy);

        /// <summary>
        /// Update Vessel data in booking.
        /// </summary>
        /// <param name="bookingId">BookingId.</param>
        /// <param name="vessel">New Vessel data.</param>
        Task UpdateBookingVesselAsync(string bookingId, VesselDetails vessel);

        /// <summary>
        /// Update Vessel details within a Reservation.
        /// </summary>
        /// <param name="reservationId">Reservation Id.</param>
        /// <param name="vessel">New vessel data.</param>
        Task UpdateReservationVesselAsync(string reservationId, VesselDetails vessel);

        Task<BaseBookingDto> GetTenantFirstBookingAsync(string tenant);

        /// <summary>
        /// Update booking's legs dates
        /// </summary>
        Task UpdateBookingLegsDatesAsync(string bookingId, ICollection<Leg> legs);

        /// <summary>
        /// Update reservation's legs dates
        /// </summary>
        Task UpdateReservationLegsDatesAsync(string reservationId, ICollection<Leg> legs);

        /// <summary>
        /// GetHashCode of current user filters
        /// </summary>
        Task<string> GetFilterHash();

        /// <summary>
        /// Gets booked vessels
        /// </summary>
        /// <param name="vesselName"></param>
        /// <param name="maxCount"></param>
        /// <returns></returns>
        Task<IEnumerable<Vessel>> GetBookedVesselsAsync(string vesselName, int maxCount);

        /// <summary>
        /// Updates booking prices depending on updated reservation 
        /// </summary>
        /// <param name="reservationId"></param>
        Task SyncBookingPriceAsync(string reservationId);

        /// <summary>
        /// Returns Bookings by Ids
        /// </summary>
        /// <param name="bookingIds"></param>
        /// <returns></returns>
        Task<IEnumerable<Models.Booking>> GetBookingsAsync(string[] bookingIds);

        Task<IEnumerable<BaseBookingDto>> GetDuplicateBookingsBookingAsync(IReadOnlyCollection<IReadOnlyCollection<FlightSegment>> segments,
            string firstName, string lastName, string departure);
    }
}
