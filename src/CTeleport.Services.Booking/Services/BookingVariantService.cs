using System;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Orchestrator.Client;
using CTeleport.Services.Booking.Orchestrator.Models;
using CTeleport.Services.Booking.Orchestrator.Models.Enums;
using CTeleport.Services.Booking.Orchestrator.Models.Requests;
using CTeleport.Services.Search.Shared.Helpers;
using CTeleport.Services.Search.Shared.Models;
using Refit;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Services;

/// <inheritdoc />
public class BookingVariantService : IBookingVariantService
{
    private readonly IBookingService _bookingService;
    private readonly IReservationBuilder _reservationBuilder;
    private readonly IOrchestratorApi _orchestratorApi;
    private readonly ILogger _logger;

    public BookingVariantService(IBookingService bookingService, IReservationBuilder reservationBuilder, IOrchestrator<PERSON>pi orchestratorApi, ILogger logger)
    {
        _bookingService = bookingService;
        _reservationBuilder = reservationBuilder;
        _orchestratorApi = orchestratorApi;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task StartBooking(BookingSaga saga, FlightSolution flightSolution)
    {
        try
        {
            var airReservations = await Task.WhenAll(saga.Reservations.Select(reservation => GetAirReservationInfo(saga, reservation, flightSolution)));

            var request = new TriggerBookingStartRequest
            {
                BookingId = saga.Id,
                TenantId = saga.BookingEntity.TenantId,
                InvoiceeId = saga.BillingEntity.InvoiceeId,
                IsApprovalRequired = saga.BookingEntity.IsApprovalRequired,
                Traveller = new TravellerInfo
                {
                    FirstName = saga.FlightSolutionEntity.Passenger.FirstName,
                    LastName = saga.FlightSolutionEntity.Passenger.LastName,
                    SingleNameOnly = saga.FlightSolutionEntity.Passenger.SingleNameOnly,
                    Nationality = saga.FlightSolutionEntity.Passenger.Nationality,
                    Email = saga.FlightSolutionEntity.Passenger.Email,
                    Phone = saga.FlightSolutionEntity.Passenger.Phone,
                    UserId = saga.FlightSolutionEntity.Passenger.UserId,
                    ExternalId = saga.FlightSolutionEntity.Passenger.ExternalId,
                    Gender = (Gender)saga.FlightSolutionEntity.Passenger.Gender,
                    DateOfBirth = DateOnly.Parse(saga.FlightSolutionEntity.Passenger.DateOfBirth),
                    Document = new DocumentInfo
                    {
                        Number = saga.FlightSolutionEntity.Passenger.DocumentNumber,
                        Country = saga.FlightSolutionEntity.Passenger.DocumentCountry,
                        Type = (DocumentType)saga.FlightSolutionEntity.Passenger.DocumentType,
                        ExpiryDate = DateOnly.Parse(saga.FlightSolutionEntity.Passenger.DocumentExpire)
                    }
                },
                CreatedBy = new UserInfo
                {
                    Name = saga.BookingEntity.CreatedBy.Name,
                    Email = saga.BookingEntity.CreatedBy.Email,
                    UserIdentity = saga.BookingEntity.CreatedBy.UserIdentity,
                    Roles = saga.BookingEntity.CreatedBy.Roles
                },
                RequestDetails = new RequestDetailsInfo
                {
                    UserAgent = saga.BookingEntity.RequestInfo.UserAgent,
                    IpAddress = saga.BookingEntity.RequestInfo.IpAddress
                },
                ScreenResolution = new ScreenResolutionInfo
                {
                    Width = saga.BillingEntity.ScreenResolution!.Width,
                    Height = saga.BillingEntity.ScreenResolution.Height
                },
                AirReservations = airReservations,
                CrewChange = saga.FlightSolutionEntity.CrewChangeAirport is null
                    ? default
                    : new CrewChangeInfo
                    {
                        Airport = saga.FlightSolutionEntity.CrewChangeAirport,
                        Member = saga.FlightSolutionEntity.CrewChangeMember!,
                        Date = DateOnly.Parse(saga.FlightSolutionEntity.CrewChangeDate!)
                    },
                Vessel = saga.FlightSolutionEntity.VesselName is null
                    ? default
                    : new VesselInfo { Name = saga.FlightSolutionEntity.VesselName, Flag = saga.FlightSolutionEntity.VesselFlag! },
                FreqFlyerNumbers = saga.BookingEntity.FreqFlyerNums
                    .Select(fn => new FreqFlyerNumberInfo
                    {
                        Number = fn.Number,
                        Carrier = fn.Carrier,
                        Code = fn.Code,
                        ProgramComponent = fn.ProgramComponent,
                        ProgramName = fn.ProgramName
                    })
                    .ToArray(),
                CustomFields = saga.FlightSolutionEntity.CustomFields.Select(cf => new CustomFieldInfo { Name = cf.Key, Value = cf.Value }).ToArray()
            };

            await _orchestratorApi.TriggerBookingStartAsync(request, Id.New());
        }
        catch (ApiException apiException)
        {
            _logger.ForContext("BookingId", saga.Id).Warning(apiException, "Booking variant: {Content}", apiException.Content);
        }
        catch (Exception e)
        {
            _logger.ForContext("BookingId", saga.Id).Warning(e, "Booking variant: Exception occured while starting booking");
        }
    }

    /// <inheritdoc />
    public async Task ApproveBooking(string id, CTeleport.Messages.Commands.Models.User user)
    {
        try
        {
            await _orchestratorApi.TriggerBookingApprovalAsync(new TriggerBookingApprovalRequest
            {
                BookingId = id,
                Approvers = new[] { new UserInfo { Name = user.Name, Email = user.Email, UserIdentity = user.Id } }
            }, Id.New());
        }
        catch (ApiException apiException)
        {
            _logger.ForContext("BookingId", id).Warning(apiException, "Booking variant: {Content}", apiException.Content);
        }
        catch (Exception e)
        {
            _logger.ForContext("BookingId", id).Warning(e, "Booking variant: Exception occured while approving booking");
        }
    }

    /// <inheritdoc />
    public async Task RejectBooking(string id, string reason)
    {
        try
        {
            await _orchestratorApi.TriggerBookingRejectionAsync(new TriggerBookingRejectionRequest { BookingId = id, Reason = reason }, Id.New());
        }
        catch (ApiException apiException)
        {
            _logger.ForContext("BookingId", id).Warning(apiException, "Booking variant: {Content}", apiException.Content);
        }
        catch (Exception e)
        {
            _logger.ForContext("BookingId", id).Warning(e, "Booking variant: Exception occured while rejecting booking");
        }
    }

    private async Task<AirReservationInfo> GetAirReservationInfo(BookingSaga saga, ReservationEntity reservation, FlightSolution flightSolution)
    {
        var reservationParams = await _reservationBuilder.BuildReservationParams(string.Empty, reservation.ProviderKey, flightSolution, saga, reservation, saga.BookingEntity.Price.IsLiTaxApplied);
        var draftReservation = await _bookingService.BuildDraftReservation(reservationParams);

        return new AirReservationInfo
        {
            ReservationId = reservation.Id,
            FlightSolutionId = flightSolution.Id,
            IsInstantCreationAllowed = !reservationParams.IsVirtual,
            ProviderKey = new ProviderKeyInfo { Key = reservation.ProviderKey, ProviderType = ToProviderType(reservation.ProviderType) },
            Price = new PriceInfo
            {
                Total = new MoneyInfo { Amount = reservationParams.ReservationPrice.Total, Currency = reservationParams.ReservationPrice.Currency },
                Markup = new MoneyInfo { Amount = reservationParams.ReservationPrice.Markup, Currency = reservationParams.ReservationPrice.Currency },
                Kickback = new MoneyInfo { Amount = reservationParams.ReservationPrice.Kickback, Currency = reservationParams.ReservationPrice.Currency }
            },
            Terms = new TermsInfo
            {
                FareType = ToFareType(draftReservation.Fare.FareType),
                Cancellations = (RefundCondition)draftReservation.Fare.Cancellations,
                Changes = (ChangeCondition)draftReservation.Fare.Changes,
                CanCancel = reservationParams.CanCancel
            },
            Legs = reservationParams.LegSegments
                .Select(leg => new ItineraryInfo
                {
                    Segments = leg
                        .Select(s => new SegmentInfo
                        {
                            Origin = s.Origin,
                            Destination = s.Destination,
                            FlightNumber = s.FlightNumber,
                            Carrier = s.Carrier,
                            Operator = s.Operator,
                            BookingCode = s.BookingCode,
                            FareBasis = s.FareBasis,
                            CabinClass = s.CabinClass,
                            FareTier = s.FareTier,
                            FareFamilyName = s.FareFamilyName,
                            EquipmentCode = s.EquipmentCode,
                            DepartureTimestampUtc = s.DepartureTimestampUtc,
                            DepartureTimestamp = s.DepartureTimestamp,
                            ArrivalTimestampUtc = s.ArrivalTimestampUtc,
                            ArrivalTimestamp = s.ArrivalTimestamp,
                            DepartureDate = DateOnly.Parse(s.DepartureDate),
                            ArrivalDate = DateOnly.Parse(s.ArrivalDate)
                        })
                        .ToArray()
                })
                .ToArray(),
            Ancillaries = reservationParams.Ancillaries
                .Select(a => new AncillaryInfo
                {
                    Key = a.Key,
                    Type = Enum.Parse<AncillaryType>(a.Type),
                    Price = new PriceInfo
                    {
                        Total = new MoneyInfo { Amount = a.TotalPrice, Currency = a.UserCurrency },
                        Markup = new MoneyInfo { Amount = a.Markup, Currency = a.Ccy },
                        Kickback = new MoneyInfo { Amount = default, Currency = a.Ccy }
                    },
                    Description = a.Description,
                    Weight = a.Weight,
                    SegmentRefs = a.SegmentRefs
                })
                .ToArray()
        };
    }

    private static FareType ToFareType(Search.Shared.Enums.FareType fareType)
        => fareType switch
        {
            Search.Shared.Enums.FareType.Public => FareType.Public,
            Search.Shared.Enums.FareType.Marine => FareType.Marine,
            Search.Shared.Enums.FareType.Mixed => FareType.Mixed,
            _ => FareType.Unknown
        };

    private static ProviderType ToProviderType(Domain.Aggregates.BookingAggregate.Enums.ProviderType providerType)
        => providerType switch
        {
            _ when providerType.Equals(Domain.Aggregates.BookingAggregate.Enums.ProviderType.Amadeus) => ProviderType.Amadeus,
            _ when providerType.Equals(Domain.Aggregates.BookingAggregate.Enums.ProviderType.Travelfusion) => ProviderType.Travelfusion,
            _ when providerType.Equals(Domain.Aggregates.BookingAggregate.Enums.ProviderType.Travelport) => ProviderType.Travelport,
            _ when providerType.Equals(Domain.Aggregates.BookingAggregate.Enums.ProviderType.Airgateway) => ProviderType.AirGateway,
            _ => ProviderType.Unknown
        };
}