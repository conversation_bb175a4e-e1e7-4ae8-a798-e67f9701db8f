using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using CTeleport.Services.Booking.Clients.Authz;

namespace CTeleport.Services.Booking.Services
{
    public class AuthzQueryParserService : IAuthzQueryParserService
    {
        public enum Operator
        {
            Equal,
            NotEqual,
            GreaterThen,
            LessThan,
            GreaterOrEqual,
            LessOrEqual,
            Contains,
            RegexpMatch,
            StartsWith,
        }

        private static readonly IReadOnlyDictionary<string, Operator> OperatorsMap = new Dictionary<string, Operator>
        {
            { "eq", Operator.Equal },
            { "equal", Operator.Equal },
            { "neq", Operator.NotEqual },
            { "lt", Operator.LessThan },
            { "gt", Operator.GreaterThen },
            { "lte", Operator.LessOrEqual },
            { "gte", Operator.GreaterOrEqual },
            { "contains", Operator.Contains },
            { "re_match", Operator.RegexpMatch },
            { "startswith", Operator.StartsWith }
        };

        public IEnumerable<IEnumerable<FilterCondition>> Parse(PartialResult partialResult)
        {
            if (partialResult.Queries == null)
            {
                throw new AuthzQueryParsingException("Failed to process partial result: Queries are null");
            }

            var result = partialResult.Queries.Select(body =>
                body.Select(expr =>
                {
                    if (!expr.IsCall()) return null;

                    if (expr.Operands().Count() > 3)
                    {
                        throw new AuthzQueryParsingException(
                            "Failed to process partial result: Invalid amount of arguments");
                    }

                    var rawOperator = expr.Operator();
                    if (!OperatorsMap.TryGetValue(rawOperator, out var @operator))
                    {
                        throw new AuthzQueryParsingException(
                            $"Failed to process partial result: Unknown operator '{rawOperator}'");
                    }

                    var value = expr.Operands()
                        .Where(x => x.IsConstant())
                        .Select(x => x as ValueTerm)
                        .FirstOrDefault()
                        ?.Value;

                    var field = expr.Operands()
                        .Where(x => !x.IsConstant())
                        .Select(x => GetFieldName(x.ToString()))
                        .FirstOrDefault();

                    return new FilterCondition(@operator, field, value);
                }).Where(x => x != null)).Where(b => b.Any());

            return result;
        }


        private string GetFieldName(string query)
        {
            var splitted = query.Split('.')
                .Select(x => x.Split('[').First())
                .ToArray();

            if (splitted.Length == 3)
            {
                return splitted[2];
            }

            return string.Join(".", splitted.Skip(2));
        }
    }

    public class AuthzQueryParsingException : Exception
    {
        public AuthzQueryParsingException(string message) : base(message) { }
    }

    public class FilterCondition
    {
        public FilterCondition(AuthzQueryParserService.Operator @operator, string field, object value)
        {
            Operator = @operator;
            Field = field;
            Value = value;
        }

        public AuthzQueryParserService.Operator Operator { get; }
        public string Field { get; }
        public object Value { get; }

        public override string ToString() => $"{Operator}::{Field}::{Value}";
    }
}