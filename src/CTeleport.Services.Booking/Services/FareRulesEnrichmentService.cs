using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.FareRules.Shared.Dto;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.FareRules.Client.Interfaces;
using CTeleport.Services.Search.Shared.Models;
using Serilog;

namespace CTeleport.Services.Booking.Services
{
    /// <summary>
    /// Service for enriching FareRules property in Reservation objects by calling the FareRules API
    /// </summary>
    public class FareRulesEnrichmentService : IFareRulesEnrichmentService
    {
        private readonly IFareRulesApiClient _fareRulesClient;
        private readonly ILogger _logger;

        public FareRulesEnrichmentService(IFareRulesApiClient fareRulesClient, ILogger logger)
        {
            _fareRulesClient = fareRulesClient ?? throw new ArgumentNullException(nameof(fareRulesClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Enriches the FareRules property of multiple reservations by fetching fare rule details from the FareRules service
        /// </summary>
        /// <param name="reservations">The reservations to enrich</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        public async Task EnrichFareRulesAsync(IEnumerable<Reservation> reservations, CancellationToken cancellationToken = default)
        {
            if (reservations == null)
            {
                _logger.Information("No reservations provided for FareRules enrichment");
                return;
            }

            // Filter valid reservations
            var validReservations = reservations.Where(ValidateReservationForEnrichment).ToList();
            if (validReservations.Count == 0)
            {
                _logger.Information("No valid reservations found for FareRules enrichment");
                return;
            }

            try
            {
                _logger.Information("Starting FareRules enrichment for {Count} reservations", validReservations.Count);

                // Collect all unique fare rule IDs from all reservations
                var allUniqueFareRuleIds = validReservations
                    .SelectMany(r => ExtractUniqueFareRuleIds(r.FareRulesIds))
                    .Distinct()
                    .ToList();

                if (allUniqueFareRuleIds.Count == 0)
                {
                    _logger.Information("No valid FareRule IDs found across all reservations");
                    return;
                }

                // Fetch all fare rules in a single API call
                var fareRuleSections = await FetchFareRulesFromApiAsync(allUniqueFareRuleIds, "batch", cancellationToken);
                if (fareRuleSections.Count == 0)
                    return;

                var fareRuleLookup = CreateValidatedFareRuleLookup(fareRuleSections, allUniqueFareRuleIds, "batch");

                // Enrich each reservation with the shared lookup
                foreach (var reservation in validReservations)
                {
                    if (reservation.FareRules is { Count: > 0 })
                    {
                        _logger.Information("FareRules already enriched for reservation {ReservationId}", reservation.Id);
                        continue;
                    }
                    
                    try
                    {
                        EnrichReservationWithFareRules(reservation, fareRuleLookup);
                        _logger.Debug("Successfully enriched FareRules for reservation {ReservationId} with {Count} O-D pairs",
                            reservation.Id, reservation.FareRules?.Count ?? 0);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error enriching FareRules for individual reservation {ReservationId}", reservation.Id);
                        // Continue with other reservations instead of failing the entire batch
                    }
                }

                _logger.Information("Successfully completed FareRules enrichment for {Count} reservations", validReservations.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error enriching FareRules for batch of reservations");
                throw;
            }
        }

        /// <summary>
        /// Validates the reservation for fare rules enrichment
        /// </summary>
        private bool ValidateReservationForEnrichment(Reservation reservation)
        {
            if (reservation == null)
            {
                _logger.Warning("Reservation is null, skipping FareRules enrichment");
                return false;
            }

            if (reservation.FareRulesIds == null)
            {
                _logger.Information("No FareRulesIds found for reservation {ReservationId}, skipping enrichment", reservation.Id);
                return false;
            }

            return true;
        }

        private static IEnumerable<string> ExtractUniqueFareRuleIds(Dictionary<string, List<string>> fareRulesIds)
        {
            if (fareRulesIds == null)
                return new List<string>();

            return fareRulesIds.Values
                .Where(ids => ids != null) // Filter nulls before SelectMany
                .SelectMany(ids => ids)
                .Where(id => !string.IsNullOrWhiteSpace(id))
                .Distinct();
        }

        /// <summary>
        /// Fetches fare rules from the API with proper logging
        /// </summary>
        private async Task<List<FareRuleSectionDto>> FetchFareRulesFromApiAsync(
            List<string> fareRuleIds, 
            string reservationId, 
            CancellationToken cancellationToken)
        {
            _logger.Information("Requesting {Count} fare rules for reservation {ReservationId}", fareRuleIds.Count, reservationId);

            var fareRuleSections = await _fareRulesClient.QueryFareRuleSectionsByIdsAsync(fareRuleIds, cancellationToken);

            _logger.Information("Received {Count} fare rule sections from API for reservation {ReservationId}", 
                fareRuleSections?.Count ?? 0, reservationId);

            if (fareRuleSections == null || fareRuleSections.Count == 0)
            {
                _logger.Warning("No fare rule sections returned from API for reservation {ReservationId}", reservationId);
                return [];
            }

            return fareRuleSections;
        }

        /// <summary>
        /// Creates a lookup dictionary from fare rule sections using ID-based mapping
        /// </summary>
        private Dictionary<string, FareRuleSection> CreateValidatedFareRuleLookup(
            List<FareRuleSectionDto> fareRuleSections, 
            List<string> requestedIds, 
            string reservationId)
        {
            var lookup = new Dictionary<string, FareRuleSection>();

            // Build lookup dictionary using ID-based mapping
            foreach (var dto in fareRuleSections)
            {
                if (dto == null)
                {
                    _logger.Warning("Received null fare rule section in API response for reservation {ReservationId}", reservationId);
                    continue;
                }

                if (string.IsNullOrWhiteSpace(dto.Id))
                {
                    _logger.Warning("Received fare rule section with null/empty ID for reservation {ReservationId}", reservationId);
                    continue;
                }

                var fareRuleSection = MapDtoToFareRuleSection(dto);
                lookup[dto.Id] = fareRuleSection;

                _logger.Debug("Mapped fare rule section with ID {FareRuleId} for reservation {ReservationId}", 
                    dto.Id, reservationId);
            }

            // Validate that all requested IDs are present in the response
            var missingIds = requestedIds.Where(id => !lookup.ContainsKey(id)).ToList();
    
            if (missingIds.Any())
            {
                _logger.Warning("Missing fare rule sections for reservation {ReservationId}. Missing IDs: {MissingFareRuleIds}", 
                    reservationId, missingIds);
            }

            _logger.Information("Created lookup with {ReceivedCount} fare rules out of {RequestedCount} requested for reservation {ReservationId}", 
                lookup.Count, requestedIds.Count, reservationId);

            return lookup;
        }

        /// <summary>
        /// Maps DTO to domain model (Id from DTO is used for lookup mapping)
        /// </summary>
        private static FareRuleSection MapDtoToFareRuleSection(FareRuleSectionDto dto)
        {
            return new FareRuleSection
            {
                Category = dto.Category,
                Title = dto.Title,
                Text = dto.Text
            };
        }

        /// <summary>
        /// Enriches the reservation with fare rules data
        /// </summary>
        private void EnrichReservationWithFareRules(
            Reservation reservation, 
            Dictionary<string, FareRuleSection> fareRuleLookup)
        {
            // Initialize FareRules dictionary if it doesn't exist
            reservation.FareRules ??= new Dictionary<string, List<FareRuleSection>>();

            // Enrich FareRules for each O-D pair
            foreach (var (odPair, fareRuleIds) in reservation.FareRulesIds)
            {
                if (fareRuleIds == null || fareRuleIds.Count == 0)
                    continue;

                var fareRulesForOdPair = CollectFareRulesForOdPair(fareRuleIds, fareRuleLookup, odPair, reservation.Id);

                if (fareRulesForOdPair.Count == 0) 
                    continue;
                
                reservation.FareRules[odPair] = fareRulesForOdPair;
                _logger.Debug("Enriched {Count} fare rules for O-D pair {OdPair}", fareRulesForOdPair.Count, odPair);
            }
        }

        /// <summary>
        /// Collects fare rules for a specific O-D pair with detailed context logging
        /// </summary>
        private List<FareRuleSection> CollectFareRulesForOdPair(
            List<string> fareRuleIds, 
            Dictionary<string, FareRuleSection> fareRuleLookup, 
            string odPair,
            string reservationId)
        {
            var fareRulesForOdPair = new List<FareRuleSection>();

            foreach (var fareRuleId in fareRuleIds)
            {
                if (fareRuleLookup.TryGetValue(fareRuleId, out var fareRuleSection))
                {
                    fareRulesForOdPair.Add(fareRuleSection);
                }
                else
                {
                    _logger
                        .ForContext("ReservationId", reservationId)
                        .ForContext("FareRuleId", fareRuleId)
                        .ForContext("ODPair", odPair)
                        .Warning("Fare rule section not found");
                }
            }

            return fareRulesForOdPair;
        }
    }
}