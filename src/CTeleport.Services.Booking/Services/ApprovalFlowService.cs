using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Messages;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Search.Shared.Models;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using ReservationState = CTeleport.Services.Booking.Enums.ReservationState;
using CommonReservation = CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.Reservation;
using CommonBooking = CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.Booking;

namespace CTeleport.Services.Booking.Services;

public class ApprovalFlowService : IApprovalFlowService
{
    private readonly IBookingApprovalItemsService _bookingApprovalItemsService;
    private readonly IBrokenReservationService _brokenReservationService;
    private readonly IBookingSagaService _bookingSagaService;
    private readonly IBookingMetricsService _metricsService;
    private readonly IBookingService _bookingService;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    
    public ApprovalFlowService(
        IBookingApprovalItemsService bookingApprovalItemsService,
        IBrokenReservationService brokenReservationService,
        IBookingSagaService bookingSagaService,
        IBookingMetricsService metricsService,
        IBookingService bookingService,
        IMapper mapper,
        ILogger logger)
    {
        _bookingApprovalItemsService = bookingApprovalItemsService;
        _brokenReservationService = brokenReservationService;
        _bookingSagaService = bookingSagaService;
        _metricsService = metricsService;
        _bookingService = bookingService;
        _mapper = mapper;
        _logger = logger;
    }
    
    public async Task<List<IMessage>> ApproveBookingAsync(BookingApprovedRequest request)
    {
        if (HasNonVirtualReservationsToApprove(request.Reservations))
        {
            // Main approval flow processes bookings with non-virtual and virtual reservations
            return await ProcessBookingApprovalFlowAsync(request);
        }
        
        // Virtual flow processes booking with only virtual reservations
        return await ProcessVirtualApprovalFlowAsync(request);
    }
    
    public bool HasNonVirtualReservationsToApprove(Reservation[] reservations) 
        => reservations.Any(r => r.ApprovalRequired && !r.IsVirtual);

    private async Task<List<IMessage>> RemoveApprovalRequiredFromReservationsAsync(string bookingId, Reservation[] reservations)
    {
        var messagesToDispatch = new List<IMessage>();
        var approvalRequiredReservations = reservations
            .Where(r => r.ApprovalRequired)
            .ToList();
        
        _logger.Information("{ServiceName} - Setting ApprovalRequired false for {ReservationCount} Reservations (BookingId: {BookingId})", 
            nameof(ApprovalFlowService), approvalRequiredReservations.Count, bookingId);
        
        foreach (var reservation in approvalRequiredReservations)
        {
            reservation.ApprovalRequired = false;
            
            await _bookingService.UpdateReservationAsync(reservation);
            messagesToDispatch.Add(new ResetTicketingTime(reservation.Id));
            
            _logger.Information("{ServiceName} - ApprovalRequired set as false for Reservation {ReservationId} (BookingId: {BookingId})",
                nameof(ApprovalFlowService), reservation.Id, bookingId);
        }

        return messagesToDispatch;
    }
    
    private async Task<List<IMessage>> ProcessBookingApprovalFlowAsync(BookingApprovedRequest request)
    {
        _logger.Information("{ServiceName} - Processing booking flow for booking {BookingId} with alternative solution {AlternativeSolution}", 
            nameof(ApprovalFlowService),request.Booking.Id, request.ApprovedSolution?.Id ?? "null");
        
        var messagesToDispatch = new List<IMessage>();
        var userIdentity = request.BookingSaga.BookingEntity.CreatedBy.UserIdentity;
        var approvedBy = _mapper.Map<User>(request.ApprovedBy);
        var saga = request.BookingSaga;
        var nonVirtualApprovalRequiredReservations = request.Reservations
            .Where(i => !i.IsVirtual && i.ApprovalRequired)
            .ToArray();
        
        if (await IsRepricingRequiredAsync(request.Booking.Id))
        {
            // If repricing required and its not an alternative solution, request approval to user first
            if (request.ApprovedSolution == null)
            {
                return ProcessApproverActionRequiredFlow(request.Booking.Id, request.RequestId, request.TenantId, request.ApprovedBy);
            }
            
            // If this is already an alternative solution and requires repricing then set PNR as broken (double repricing)
            // Inconsistent if have non-virtual reservations
            saga = await SetNonVirtualReservationsAsBrokenAsync(request);
        }
        else if (nonVirtualApprovalRequiredReservations.Any())
        {
            messagesToDispatch.AddRange(
                await RemoveApprovalRequiredFromReservationsAsync(request.Booking.Id, nonVirtualApprovalRequiredReservations));
        
            await _bookingService.SetAsApprovedByAsync(request.BookingSaga.Id, approvedBy);

            _logger.Information("{ServiceName} - Booking {BookingId} approved by {User}", 
                nameof(ApprovalFlowService), request.Booking.Id, approvedBy.Id);
        }
        
        saga = await _bookingSagaService.ApproveAsync(new ApproveSagaRequest
        {
            BookingSagaId = saga.Id,
            EffectiveFlightSolutionId = request.ApprovedSolution?.Id ?? request.Booking.FlightSolutionId,
            EffectiveProviderKeys = GetProviderKeyToOriginalReservation(saga, request.ApprovedSolution, request.OriginalSolution),
            ApprovedBy = new[] { approvedBy }
        });
        
        // Send CreateReservation command if there is any virtual reservation to create
        if (HasReservationsToCreate(saga))
        {
            messagesToDispatch.Add(PrepareCreateReservationCommand(saga, request.Booking.Id, request.RequestId));
        }
        else
        {
            await CompleteBookingSagaAsync(saga);

            // Send booking confirmed notification after completing saga
            try
            {
                var mappedReservations = _mapper.Map<CommonReservation[]>(request.Reservations);
                var mappedBooking = _mapper.Map<CommonBooking>(request.Booking);

                // TODO: move to mapping
                if (string.IsNullOrWhiteSpace(mappedBooking.Passenger?.Email))
                {
                    mappedBooking.Passenger.Email = request.Booking.Passenger.Email
                        ?? request.BookingSaga?.FlightSolutionEntity?.Passenger.Email;
                }

                messagesToDispatch.Add(new BookingConfirmed(userIdentity, request.RequestId, request.Booking.Id, request.TenantId)
                {
                    Reservations = mappedReservations,
                    Booking = mappedBooking
                });
            }
            catch (Exception e)
            {
                _logger.ForContext("CorrelationId", saga.Id)
                    .Error(e, "{EventName} Error occured while mapping booking data", nameof(BookingApprovedRequest));
                messagesToDispatch.Add(new BookingConfirmed(userIdentity, request.RequestId, request.Booking.Id, request.TenantId));
            }
        }
        
        _logger.Information("{ServiceName} - BookingSaga {BookingSagaId} approved by {User}", 
            nameof(ApprovalFlowService), saga.Id, approvedBy.Id);
        
        return messagesToDispatch;
    }

    private IMessage PrepareCreateReservationCommand(BookingSaga saga, string bookingId, string requestId)
    {
        _logger.Information("{ServiceName} - Booking {BookingId} has {NumberReservationsToCreate} reservations to create", 
            nameof(ApprovalFlowService), bookingId, saga.ReservationsToCreate.Count);
            
        // Send only the first provider key of the list of reservations to create
        // (it will create the following reservations in the CreateReservation logic)
        var firstProviderKey = saga.ReservationsToCreate.First().ProviderKey;
            
        return new CreateReservation { RequestId = requestId, BookingId = bookingId, ProviderKey = firstProviderKey };
    }

    private async Task<BookingSaga> SetNonVirtualReservationsAsBrokenAsync(BookingApprovedRequest request)
    {
        _logger.Warning("{ServiceName} - Double repricing of non-virtual reservations detected in Booking {BookingId}", 
            nameof(ApprovalFlowService), request.Booking.Id);
            
        foreach (var reservation in request.Reservations.Where(r => !r.IsVirtual && r.State == ReservationState.Active))
        {
            await _brokenReservationService.ReservationBroken(request.Booking.Id, reservation.Id,
                request.RequestId, request.ApprovedBy);
        }

        // Need to reread saga after ReservationBroken
        return await _bookingSagaService.GetAsync(request.Booking.Id);
    }
    
    private async Task<List<IMessage>> ProcessVirtualApprovalFlowAsync(BookingApprovedRequest request)
    {
        _logger.Information("{ServiceName} - Processing virtual approval flow for booking {BookingId}", 
            nameof(ApprovalFlowService),request.Booking.Id);
        
        var messagesToDispatch = new List<IMessage>();
        
        var approvedBy = _mapper.Map<User>(request.ApprovedBy);
        
        var saga = await _bookingSagaService.ApproveAsync(new ApproveSagaRequest
        {
            BookingSagaId = request.BookingSaga.Id,
            EffectiveFlightSolutionId = request.ApprovedSolution?.Id ?? request.Booking.FlightSolutionId,
            EffectiveProviderKeys = GetProviderKeyToOriginalReservation(request.BookingSaga, request.ApprovedSolution, request.OriginalSolution),
            ApprovedBy = new[] { approvedBy }
        });
        
        _logger.Information("{ServiceName} - Booking {BookingId} has {NumberReservationsToCreate} reservations to create", 
            nameof(ApprovalFlowService), request.Booking.Id, saga.ReservationsToCreate.Count);
        
        if (HasReservationsToCreate(saga))
        {
            messagesToDispatch.Add(PrepareCreateReservationCommand(
                saga, request.Booking.Id, request.RequestId));
        }
        
        _logger.Information("{ServiceName} - BookingSaga {BookingSagaId} approved by {User}", 
            nameof(ApprovalFlowService), saga.Id, approvedBy.Id);

        return messagesToDispatch;
    }
    
    private async Task CompleteBookingSagaAsync(BookingSaga saga)
    {
        saga = await _bookingSagaService.ChangeStateAsync(saga.Id, BookingSagaState.Completed);
        _metricsService.DecrementActiveBookingSagaCounter(saga.BookingEntity.TenantId);
        
        _logger.Information("{ServiceName} - Booking Saga {SagaId} completed", nameof(ApprovalFlowService), saga.Id);
    }
    
    private List<IMessage> ProcessApproverActionRequiredFlow(string bookingId, string requestId, string tenantId, 
        Messages.Commands.Models.User approvedBy)
        => new List<IMessage> { new ApproverActionRequired { Approver = approvedBy, RequestId = requestId, BookingId = bookingId, TenantId = tenantId } };

    private async Task<bool> IsRepricingRequiredAsync(string bookingId)
        => await _bookingApprovalItemsService.GetApprovalQueueItemRepricingRequiredAsync(bookingId, checkOnlyForRealPnr: true);

    private bool HasReservationsToCreate(BookingSaga saga)
        => saga.ReservationsToCreate.Any();
    
    private Dictionary<string, string> GetProviderKeyToOriginalReservation(
        BookingSaga saga, FlightSolution approvedSolution, FlightSolution originalSolution)
    {
        if (approvedSolution?.SplitProviderODs is null || !approvedSolution.SplitProviderODs.Any())
            return saga.Reservations.ToDictionary(r => r.Id, r => r.ProviderKey);

        var providerKeyToOriginalReservationDict = new Dictionary<string, string>();
        for (var i = 0; i < approvedSolution.SplitProviderODs.Count; i++)
        {
            var approvedOD = approvedSolution.SplitProviderODs[i];

            var originalSplitOD = originalSolution.SplitProviderODs
                .FirstOrDefault(originalOD => originalOD.OriginDestination == approvedOD.OriginDestination);

            if (originalSplitOD == null)
            {
                _logger.Warning("Approved split FlightSolution does not contain original solution OD: {OriginDestination}", 
                    approvedOD.OriginDestination);
            }

            originalSplitOD = originalSolution
                .SplitProviderODs
                .ElementAtOrDefault(i);

            if (originalSplitOD == null)
            {
                throw new NotFoundException($"Approved split FlightSolution does not contain original solution OD on index: {i}");
            }

            var originalProviderKey = originalSplitOD.ProviderKey;
            var originalReservation = saga.Reservations
                .Single(r => r.ProviderKey == originalProviderKey);

            providerKeyToOriginalReservationDict.TryAdd(originalReservation.Id, approvedOD.ProviderKey);
        }

        return providerKeyToOriginalReservationDict;
    }
}
