using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using CTeleport.Services.Ancillary.Shared;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Enums;
using CTeleport.Services.ExtraServiceManagement.Models;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using BookingSagaChangeCondition = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.ChangeCondition;
using BookingSagaFareType = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.FareType;
using BookingSagaRefundCondition = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.RefundCondition;
using BookingSagaAncillaryType = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.AncillaryType;
using BookingSagaBookingState = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.BookingState;
using BookingSagaReservationState = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.ReservationState;
using BookingSagaPaymentMethod = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.PaymentMethod;
using BookingSagaGender = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.Gender;
using BookingSagaDocumentType = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums.DocumentType;
using Site = CTeleport.Services.Booking.Models.Site;

namespace CTeleport.Services.Booking;

public class BookingSagaMapperProfile : Profile
{
    public BookingSagaMapperProfile()
    {
        #region Models

        CreateMap<BookingSaga, Models.Booking>()
            .ForMember(dest => dest.TenantId, opt => opt.MapFrom(src => src.BookingEntity.TenantId))
            .ForMember(dest => dest.OriginalBookingId, opt => opt.MapFrom(src => src.BookingEntity.OriginalBookingId))
            .ForMember(dest => dest.InitialBookingId, opt => opt.MapFrom(src => src.BookingEntity.InitialBookingId))
            .ForMember(dest => dest.InitialLocator, opt => opt.MapFrom(src => src.BookingEntity.InitialBookingLocators.FirstOrDefault()))
            .ForMember(
                dest => dest.Locator,
                opt => opt.MapFrom(src =>
                    src.Reservations.FirstOrDefault(r => r.State == BookingSagaReservationState.Active ||
                                                         r.State == BookingSagaReservationState.Cancelled) == null
                        ? src.Reservations.FirstOrDefault(r => r.State == BookingSagaReservationState.Virtual) == null
                            ? default
                            : src.Reservations.FirstOrDefault(r => r.State == BookingSagaReservationState.Virtual).OriginalLocator
                        : src.Reservations.FirstOrDefault(r => r.State == BookingSagaReservationState.Active ||
                                                               r.State == BookingSagaReservationState.Cancelled).Locator))
            .ForMember(dest => dest.SearchId, opt => opt.MapFrom(src => src.FlightSolutionEntity.SearchId))
            .ForMember(dest => dest.RouteId, opt => opt.MapFrom(src => src.FlightSolutionEntity.RouteId))
            .ForMember(dest => dest.FlightSolutionId, opt => opt.MapFrom(src => src.FlightSolutionEntity.FlightSolutionId))
            .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.BookingEntity.State))
            .ForMember(dest => dest.Passenger, opt => opt.MapFrom(src => src.FlightSolutionEntity.Passenger))
            .ForMember(dest => dest.Legs, opt => opt.MapFrom<LegResolver>())
            .ForMember(dest => dest.NoShow, opt => opt.MapFrom(src => false))
            .ForMember(dest => dest.Site, opt => opt.MapFrom(src => src.BookingEntity.Site))
            .ForMember(
                dest => dest.DepartureAt,
                opt => opt.MapFrom(src => !src.Reservations.Any()
                    ? DateTime.MinValue
                    : DateTimeOffset.FromUnixTimeSeconds(src.Reservations.First().ProviderItinerary.DepartureTimestampUtc ?? default).DateTime))
            .ForMember(
                dest => dest.CommentedBy,
                opt => opt.MapFrom(src => string.IsNullOrWhiteSpace(src.BookingEntity.Comment) ? null : src.BookingEntity.CreatedBy))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.BookingEntity.CreatedBy))
            .ForMember(dest => dest.ApprovedBy, opt => opt.MapFrom(src => src.BookingEntity.ApprovedBy))
            .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.BookingEntity.Comment))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTimeOffset.FromUnixTimeSeconds(src.UpdatedAtTimestamp).DateTime))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTimeOffset.FromUnixTimeSeconds(src.CreatedAtTimestamp).DateTime))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => src.BillingEntity.PaymentMethod))
            .ForMember(dest => dest.PaymentMethodId, opt => opt.MapFrom(src => src.BillingEntity.PaymentMethodId))
            .ForMember(dest => dest.PaymentId, opt => opt.MapFrom(src => src.BillingEntity.PaymentId))
            .ForMember(dest => dest.PaymentExpiresAt, opt => opt.MapFrom(src => src.BillingEntity.PaymentExpiry))
            .ForMember(dest => dest.InvoiceeId, opt => opt.MapFrom(src => src.BillingEntity.InvoiceeId))
            .ForMember(dest => dest.Terms, opt => opt.MapFrom<TermsResolver>())
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.BookingEntity.Price))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => new Metadata
            {
                VesselName = src.FlightSolutionEntity.VesselName,
                VesselFlag = src.FlightSolutionEntity.VesselFlag,
                CrewChangeAirport = src.FlightSolutionEntity.CrewChangeAirport,
                CrewChangeDate = src.FlightSolutionEntity.CrewChangeDate,
                CrewChangeMember = src.FlightSolutionEntity.CrewChangeMember,
                ExemptLiTax = src.BookingEntity.Price.IsLiTaxApplied,
                CustomFields = src.FlightSolutionEntity.CustomFields.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            }))
            .ForMember(
                dest => dest.FrequentFlyerNumbers,
                opt => opt.MapFrom(src => src.BookingEntity.FreqFlyerNums.ToDictionary(
                    ffn => ffn.ProgramComponent,
                    ffn => new CTeleport.Services.FrequentFlyer.Models.FrequentFlyerNumber
                    {
                        Code = ffn.Code,
                        Carrier = ffn.Carrier,
                        Number = ffn.Number,
                        ProgramName = ffn.ProgramName
                    })))
            .ForMember(dest => dest.CancelledAt, opt => opt.Ignore())
            .ForMember(dest => dest.CancelledBy, opt => opt.Ignore())
            .ForMember(dest => dest.ApprovedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RejectedBy, opt => opt.Ignore())
            .ForMember(dest => dest.RejectedAt, opt => opt.Ignore())
            .ForMember(dest => dest.RejectReason, opt => opt.Ignore());

        CreateMap<BookingSaga, BaseBookingDto>()
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.BookingEntity.CreatedBy))
            .ForMember(dest => dest.TenantId, opt => opt.MapFrom(src => src.BookingEntity.TenantId))
            .ForMember(
                dest => dest.PaxName,
                opt => opt.MapFrom(src => $"{src.FlightSolutionEntity.Passenger.LastName} {src.FlightSolutionEntity.Passenger.FirstName}".Trim()))
            .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.BookingEntity.State.ToString()))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => new BookingMetadataDto
            {
                VesselName = src.FlightSolutionEntity.VesselName,
                VesselFlag = src.FlightSolutionEntity.VesselFlag
            }))
            .ForMember(dest => dest.Price, opt => opt.MapFrom(src => new BookingPriceDto
            {
                Total = src.BookingEntity.Price.Total,
                Ccy = src.BookingEntity.Price.Currency,
                PerMile = src.BookingEntity.Price.PerMile ?? default
            }))
            .ForMember(dest => dest.NoShow, opt => opt.Ignore())
            .ForMember(dest => dest.ArrivalUtc, opt => opt.Ignore())
            .ForMember(dest => dest.Locator, opt => opt.Ignore())
            .ForMember(dest => dest.InitialLocator, opt => opt.Ignore())
            .ForMember(dest => dest.Terms, opt => opt.Ignore())
            .ForMember(dest => dest.DepartureAt, opt => opt.Ignore())
            .ForMember(dest => dest.Legs, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.Autofilled, opt => opt.Ignore())
            .ForMember(dest => dest.FrequentFlyerNumbers, opt => opt.Ignore());

        CreateMap<BookingSaga, BookingSagaDto>()
            .IncludeBase<BookingSaga, BaseBookingDto>()
            .ForMember(dest => dest.Passenger, opt => opt.MapFrom(src => src.FlightSolutionEntity.Passenger))
            .ForMember(dest => dest.InvoiceeId, opt => opt.MapFrom(src => src.BillingEntity.InvoiceeId))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => src.BillingEntity.PaymentMethod))
            .ForMember(dest => dest.PaymentMethodId, opt => opt.MapFrom(src => src.BillingEntity.PaymentMethodId))
            .ForMember(dest => dest.PaymentId, opt => opt.MapFrom(src => src.BillingEntity.PaymentId))
            .ForMember(dest => dest.FlightSolutionId, opt => opt.MapFrom(src => src.FlightSolutionEntity.FlightSolutionId))
            .ForMember(dest => dest.SearchId, opt => opt.MapFrom(src => src.FlightSolutionEntity.SearchId))
            .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<BookingLegSegmentsResolver>())
            .ForMember(dest => dest.RouteId, opt => opt.Ignore())
            .ForMember(dest => dest.OriginalBookingId, opt => opt.Ignore())
            .ForMember(dest => dest.Site, opt => opt.MapFrom(src => src.BookingEntity.Site));

        CreateMap<SiteEntity, BookingSagaDto.SiteDto>().ReverseMap();
        
        CreateMap<ReservationEntity, BookingSagaDto.BookingReservation>()
            .ForMember(dest => dest.ReservationId, opt => opt.MapFrom(src => src.Id))
            .ForMember(
                dest => dest.IsVirtual,
                opt => opt.MapFrom(src => src.State == Domain.Aggregates.BookingAggregate.Enums.ReservationState.Virtual))
            .ForMember(dest => dest.ReservationId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.SelectedAncillaries, opt => opt.MapFrom(src => src.Ancillaries));

        CreateMap<AncillaryEntity, BookingSagaDto.Ancillary>()
            .ForMember(dest => dest.Key, opt => opt.MapFrom(src => src.AncillaryKey))
            .ForMember(dest => dest.Ccy, opt => opt.MapFrom(src => src.Currency))
            .ForMember(dest => dest.SolutionProviderKey, opt => opt.Ignore());

        CreateMap<Models.Ancillary, AncillaryEntity>()
            .ForMember(dest => dest.AncillaryKey, opt => opt.MapFrom(src => src.Key))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Ccy))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type == "BaggageCabin"
                ? Domain.Aggregates.BookingAggregate.Enums.AncillaryType.BaggageCabin
                : src.Type == "BaggageCheckedIn"
                    ? Domain.Aggregates.BookingAggregate.Enums.AncillaryType.BaggageCheckedIn
                    : Domain.Aggregates.BookingAggregate.Enums.AncillaryType.Unknown));

        CreateMap<AncillaryModel, AncillaryEntity>()
            .ForMember(dest => dest.AncillaryKey, opt => opt.MapFrom(src => src.Key))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Ccy));

        CreateMap<Site, SiteEntity>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type))
            .ForMember(dest => dest.CustomFields, opt => opt.MapFrom(src => src.CustomFields))
            .ReverseMap();
        
        CreateMap<UserEntity, User>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.UserIdentity));

        CreateMap<User, UserEntity>()
            .ForMember(dest => dest.UserIdentity, opt => opt.MapFrom(src => src.Id));

        CreateMap<Messages.Commands.Models.User, UserEntity>()
            .ForMember(dest => dest.UserIdentity, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Roles, opt => opt.Ignore());

        CreateMap<PriceEntity, BookingPrice>();

        CreateMap<SiteEntity, Site>();
        
        CreateMap<PassengerEntity, PassengerDetails>()
            .ForMember(dest => dest.DocType, opt => opt.MapFrom(src => src.DocumentType))
            .ForMember(dest => dest.DocNumber, opt => opt.MapFrom(src => src.DocumentNumber))
            .ForMember(dest => dest.DocCountry, opt => opt.MapFrom(src => src.DocumentCountry))
            .ForMember(dest => dest.DocExpire, opt => opt.MapFrom(src => src.DocumentExpire))
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ExternalId))
            .ForMember(dest => dest.Autofilled, opt => opt.Ignore());

        CreateMap<PassengerEntity, BookingSagaDto.PassengerDetails>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ExternalId));

        CreateMap<CTeleport.Messages.Commands.Models.Passenger, PassengerEntity>()
            .ForMember(
                dest => dest.Gender,
                opt => opt.MapFrom(src => src.Gender == "M" || src.Gender == "Male"
                    ? BookingSagaGender.Male
                    : src.Gender == "F" || src.Gender == "Female"
                        ? BookingSagaGender.Female
                        : BookingSagaGender.Unknown))
            .ForMember(dest => dest.DocumentType, opt => opt.MapFrom(src => BookingSagaDocumentType.Passport))
            .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.DocNumber))
            .ForMember(dest => dest.DocumentCountry, opt => opt.MapFrom(src => src.DocCountry))
            .ForMember(dest => dest.DocumentExpire, opt => opt.MapFrom(src => src.DocExpire))
            .ForMember(dest => dest.ExternalId, opt => opt.MapFrom(src => src.Id));

        CreateMap<FlightSegment, FlightSegmentEntity>();

        CreateMap<FlightSegmentEntity, FlightSegment>()
            .ForMember(dest => dest.SplitOD, opt => opt.Ignore())
            .ForMember(dest => dest.OperatorName, opt => opt.Ignore())
            .ForMember(dest => dest.FareComponent, opt => opt.Ignore())
            .ForMember(dest => dest.BookingCount, opt => opt.Ignore())
            .ForMember(dest => dest.Splitting, opt => opt.Ignore())
            .ForMember(dest => dest.ConnectionDuration, opt => opt.Ignore())
            .ForMember(dest => dest.Connecting, opt => opt.Ignore())
            .ForMember(dest => dest.DestinationTerminal, opt => opt.Ignore())
            .ForMember(dest => dest.OriginTerminal, opt => opt.Ignore())
            .ForMember(dest => dest.ArrivalTime, opt => opt.Ignore())
            .ForMember(dest => dest.DepartureTime, opt => opt.Ignore())
            .ForMember(dest => dest.AvailabilitySource, opt => opt.Ignore())
            .ForMember(dest => dest.EquipmentCode, opt => opt.Ignore())
            .ForMember(dest => dest.TechnicalStops, opt => opt.Ignore());

        CreateMap<BookingSaga, FlightSolution>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.FlightSolutionEntity.FlightSolutionId))
            .ForMember(dest => dest.SearchId, opt => opt.MapFrom(src => src.FlightSolutionEntity.SearchId))
            .ForMember(dest => dest.RouteId, opt => opt.MapFrom(src => src.FlightSolutionEntity.RouteId))
            .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<FlightSolutionLegSegmentsResolver>())
            .ForAllOtherMembers(opt => opt.Ignore());

        #endregion

        #region Enums

        CreateMap<PaymentMethod, BookingSagaPaymentMethod>().ConvertUsing((value, _) =>
            value switch
            {
                PaymentMethod.AmexBta => BookingSagaPaymentMethod.AmexBta,
                PaymentMethod.BankTransfer => BookingSagaPaymentMethod.BankTransfer,
                PaymentMethod.CreditCard => BookingSagaPaymentMethod.CreditCard,
                _ => BookingSagaPaymentMethod.Unknown
            });

        CreateMap<BookingSagaPaymentMethod, PaymentMethod>().ConvertUsing((value, _) =>
            value switch
            {
                BookingSagaPaymentMethod.AmexBta => PaymentMethod.AmexBta,
                BookingSagaPaymentMethod.BankTransfer => PaymentMethod.BankTransfer,
                BookingSagaPaymentMethod.CreditCard => PaymentMethod.CreditCard,
                _ => PaymentMethod.BankTransfer
            });

        CreateMap<BaggageType, BookingSagaAncillaryType>().ConvertUsing((value, _) =>
            value switch
            {
                BaggageType.BaggageCabin => BookingSagaAncillaryType.BaggageCabin,
                BaggageType.BaggageCheckedIn => BookingSagaAncillaryType.BaggageCheckedIn,
                _ => BookingSagaAncillaryType.Unknown
            });

        CreateMap<BookingSagaAncillaryType, BaggageType>().ConvertUsing((value, _) =>
            value switch
            {
                BookingSagaAncillaryType.BaggageCabin => BaggageType.BaggageCabin,
                BookingSagaAncillaryType.BaggageCheckedIn => BaggageType.BaggageCheckedIn,
                _ => BaggageType.Unknown
            });

        CreateMap<BookingSagaReservationState, ReservationState>().ConvertUsing((value, _) =>
            value switch
            {
                BookingSagaReservationState.New => ReservationState.New,
                BookingSagaReservationState.Active => ReservationState.Active,
                BookingSagaReservationState.Cancelled => ReservationState.Cancelled,
                _ => ReservationState.New
            });

        CreateMap<BookingSagaBookingState, BookingState>().ConvertUsing((value, _) =>
            value switch
            {
                BookingSagaBookingState.ApprovalRequired => BookingState.ApprovalRequired,
                BookingSagaBookingState.Confirmed => BookingState.Confirmed,
                BookingSagaBookingState.Cancelled => BookingState.Cancelled,
                _ => BookingState.Confirmed
            });

        CreateMap<BookingSagaGender, Gender>().ConvertUsing((value, _) =>
            value switch
            {
                BookingSagaGender.Male => Gender.Male,
                BookingSagaGender.Female => Gender.Female,
                _ => Gender.Male
            });

        CreateMap<BookingSagaDocumentType, DocumentType>().ConvertUsing((value, _) =>
            value switch
            {
                BookingSagaDocumentType.Passport => DocumentType.Passport,
                _ => DocumentType.Passport
            });

        #endregion
    }

    #region Resolvers

    public class TermsResolver : IValueResolver<BookingSaga, Models.Booking, BookingTerms>
    {
        public BookingTerms Resolve(BookingSaga source, Models.Booking destination, BookingTerms bookingTerms, ResolutionContext context)
            => new()
            {
                Changes = BookingSagaChangeCondition.ToChangeConditionExt(source.BookingEntity.Terms.Changes),
                Cancellations = BookingSagaRefundCondition.ToRefundConditionExt(source.BookingEntity.Terms.Cancellations),
                FareType = BookingSagaFareType.ToFareTypeExt(source.BookingEntity.Terms.FareType),
                CanCancel = source.BookingEntity.Terms.CanCancel,
                Splitting = source.Reservations.Count > 1
            };
    }

    public class FlightSolutionLegSegmentsResolver : BaseLegSegmentsResolver, IValueResolver<BookingSaga, FlightSolution, IList<IList<FlightSegment>>>
    {
        public IList<IList<FlightSegment>> Resolve(BookingSaga source, FlightSolution destination, IList<IList<FlightSegment>> member,
            ResolutionContext context)
            => ResolveSegments(source, context);
    }

    public class BookingLegSegmentsResolver : BaseLegSegmentsResolver, IValueResolver<BookingSaga, BookingSagaDto, IList<IList<FlightSegment>>>
    {
        public IList<IList<FlightSegment>> Resolve(BookingSaga source, BookingSagaDto destination, IList<IList<FlightSegment>> member,
            ResolutionContext context)
            => ResolveSegments(source, context);
    }

    public class LegResolver : BaseLegSegmentsResolver, IValueResolver<BookingSaga, Models.Booking, ICollection<Leg>>
    {
        public ICollection<Leg> Resolve(BookingSaga source, Models.Booking destination, ICollection<Leg> member, ResolutionContext context)
            => ResolveSegments(source, context)
                .Select(l => new Leg
                {
                    Origin = l[0].Origin,
                    Destination = l[^1].Destination,
                    Departure = l[0].DepartureTimestamp,
                    DepartureUtc = l[0].DepartureTimestampUtc,
                    Arrival = l[^1].ArrivalTimestamp,
                    ArrivalUtc = l[^1].ArrivalTimestampUtc
                })
                .ToList();
    }

    public class BaseLegSegmentsResolver
    {
        protected static IList<IList<FlightSegment>> ResolveSegments(BookingSaga saga, ResolutionContext context)
        {
            if (saga.FlightSolutionEntity.SearchRouteLegs.Count is 1)
                return new List<IList<FlightSegment>> { context.Mapper.Map<IList<FlightSegment>>(saga.FlightSolutionEntity.Itinerary.Segments) };

            var result = new List<IList<FlightSegment>>();

            return saga.Reservations
                .Aggregate(result, (current, reservation) => current
                    .Concat(reservation.ProviderItinerary.Legs
                        .Select(l => context.Mapper.Map<IList<FlightSegment>>(l.Segments))
                        .ToList())
                    .ToList());
        }
    }

    #endregion
}