using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AutoMapper;
using CTeleport.Common.Authorization.Models;
using CTeleport.Common.Helpers;
using CTeleport.Messages.Commands.Enums;
using CTeleport.Services.ApprovalQueueClient.Models;
using CTeleport.Services.Booking.Clients;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.ExtraServiceManagement.Shared;
using CTeleport.Services.ExtraServiceManagement.Shared.Metadata;
using CTeleport.Services.ExtraServiceManagement.Shared.Price;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;
using ServiceStack;
using ServiceStack.Text;
using BaggageAllowance = CTeleport.Services.Search.Shared.Models.BaggageAllowance;
using BookingState = CTeleport.Services.Booking.Enums.BookingState;
using FlightSegment = CTeleport.Services.Search.Shared.Models.FlightSegment;
using FrequentFlyerNumber = CTeleport.Services.FrequentFlyer.Models.FrequentFlyerNumber;
using Leg = CTeleport.Services.Booking.Models.Leg;
using TechnicalStop = CTeleport.Services.Booking.Shared.Models.TechnicalStop;
using LegacyModels = CTeleport.Services.CustomFields.Models.Legacy;

namespace CTeleport.Services.Booking
{
    public class BookingMapperProfile : Profile
    {
        public BookingMapperProfile()
        {

            CreateMap<Reservation, Dto.ExtendedStructuredReport.ReservationDto>()
                .ForMember(d => d.Legs, e => e.Ignore());

            CreateMap<Segment, SegmentDto>()
                .ForMember(dest => dest.Locator, opt => opt.Ignore())
                .ForMember(dest => dest.Cat16Id, opt => opt.Ignore())
                .ForMember(dest => dest.FareComponent, opt => opt.Ignore())
                .ForMember(dest => dest.Splitting, opt => opt.Ignore());

            //CreateMap<Search.Shared.Models.Vessel, Vessel>()
            //    .ForMember(dest => dest.Flag, opt => opt.MapFrom(src => src.Flag.Code));

            //CreateMap<Metadata, Vessel>()
            //    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.VesselName))
            //    .ForMember(dest => dest.Flag, opt => opt.MapFrom(src => src.VesselFlag));

            //CreateMap<PassengerDetails, Traveler>()
            //    .ForMember(dest => dest.Prefix, opt => opt.ResolveUsing<TravelerPrefixResolver>())
            //    .ForMember(dest => dest.DOB, opt => opt.MapFrom(src => DateTime.Parse(src.DateOfBirth, CultureInfo.InvariantCulture).ToString("ddMMMyy", CultureInfo.InvariantCulture).ToUpperInvariant()))
            //    .ForMember(dest => dest.Gender, opt => opt.MapFrom(src => EnumHelper.ToString(src.Gender)))
            //    .ForMember(dest => dest.DocType, opt => opt.MapFrom(src => EnumHelper.ToString(src.DocType)))
            //    .ForMember(dest => dest.DocExpire, opt => opt.MapFrom(src => DateTime.Parse(src.DocExpire, CultureInfo.InvariantCulture).ToString("ddMMMyy", CultureInfo.InvariantCulture).ToUpperInvariant()));

            CreateMap<PassengerDetails, CTeleport.Messages.Commands.Models.PassengerDetails>()
                .ForMember(dest => dest.DateOfBirth,
                    opt => opt.MapFrom(src =>
                        DateTime.Parse(src.DateOfBirth, CultureInfo.InvariantCulture)
                            .ToString("ddMMMyy", CultureInfo.InvariantCulture).ToUpperInvariant()))
                .ForMember(dest => dest.Gender, opt => opt.MapFrom(src => EnumHelper.ToString(src.Gender)))
                .ForMember(dest => dest.DocType, opt => opt.MapFrom(src => EnumHelper.ToString(src.DocType)))
                .ForMember(dest => dest.DocExpire,
                    opt => opt.MapFrom(src =>
                        DateTime.Parse(src.DocExpire, CultureInfo.InvariantCulture)
                            .ToString("ddMMMyy", CultureInfo.InvariantCulture).ToUpperInvariant()));

            CreateMap<Models.Booking, BaseBookingDto>()
                .ForMember(dest => dest.PaxName,
                    opt => opt.MapFrom(src => $"{src.Passenger.LastName} {src.Passenger.FirstName}".Trim()))
                .ForMember(dest => dest.Autofilled,
                    opt => opt.MapFrom(src => src.Passenger.Autofilled.HasValue && src.Passenger.Autofilled.Value))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.ArrivalUtc, opt => opt.MapFrom<ArrivalUtcResolver>());

            CreateMap<Models.Booking, BookingUpdateDto>()
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.VesselName, opt => opt.MapFrom(src => src.Metadata.VesselName))
                .ForMember(dest => dest.CreatedById, opt => opt.MapFrom(src => src.CreatedBy.Id))
                .ForMember(dest => dest.Autofilled, opt => opt.MapFrom(src => src.Passenger.Autofilled));
            CreateMap<Models.Reservation, ReservationUpdateDto>()
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.Markup, opt => opt.MapFrom(src => src.Price.Markup));

            CreateMap<Models.Booking, CompleteBookingDto>()
                .IncludeBase<Models.Booking, BaseBookingDto>()
                .ForMember(dest => dest.LegDurations, opt => opt.Ignore())
                .ForMember(dest => dest.LegSegments, opt => opt.Ignore())
                .ForMember(dest => dest.Baggage, opt => opt.Ignore())
                .ForMember(dest => dest.FareRules, opt => opt.Ignore())
                .ForMember(dest => dest.Reservations, opt => opt.Ignore())
                .ForMember(dest => dest.Refund, opt => opt.Ignore())
                .ForMember(dest => dest.FareConditions, opt => opt.Ignore())
                .ForMember(dest => dest.ExtraServices, opt => opt.Ignore())
                .ForMember(dest => dest.Invoicee, opt => opt.Ignore())
                .ForMember(dest => dest.FareServiceBundles, opt => opt.Ignore())
                .ForMember(dest => dest.RepricingRequired, opt => opt.Ignore());
            
            CreateMap<PassengerDetails, BookingSagaDto.PassengerDetails>();

            CreateMap<List<Reservation>, CompleteBookingDto>()
                .BeforeMap((reservations, dto) => SetupReservationsForVirtualBooking(reservations))
                .ForMember(dest => dest.LegDurations, opt => opt.MapFrom<LegDurationsResolver>())
                .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<SegmentsResolver>())
                .ForMember(dest => dest.FareRules, opt => opt.MapFrom<FareRulesResolver>())
                .ForMember(dest => dest.Baggage, opt => opt.MapFrom<BaggageResolver>())
                .ForMember(dest => dest.Price, opt => opt.MapFrom<PriceComponentsResolver>())
                .ForMember(dest => dest.Terms, opt => opt.MapFrom<BookingTermComponentsResolver>())
                .ForMember(dest => dest.Refund, opt => opt.MapFrom<BookingRefundResolver>())
                .AfterMap(SetupSupplierLocators)
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<Models.Booking, PublicBookingDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => src.Passenger.FirstName))
                .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => src.Passenger.LastName))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.LegDurations, opt => opt.Ignore())
                .ForMember(dest => dest.LegSegments, opt => opt.Ignore())
                .ForMember(dest => dest.Baggage, opt => opt.Ignore())
                .ForMember(dest => dest.Reservations, opt => opt.Ignore())
                .ForMember(dest => dest.ExtraServices, opt => opt.Ignore())
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.Terms.FareType)));

            CreateMap<Models.Booking, ItineraryReceiptBookingDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => src.Passenger.FirstName))
                .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => src.Passenger.LastName))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.LegDurations, opt => opt.Ignore())
                .ForMember(dest => dest.FareConditions, opt => opt.Ignore())
                .ForMember(dest => dest.LegSegments, opt => opt.Ignore())
                .ForMember(dest => dest.Baggage, opt => opt.Ignore())
                .ForMember(dest => dest.Reservations, opt => opt.Ignore())
                .ForMember(dest => dest.ExtraServices, opt => opt.Ignore())
                .ForMember(dest => dest.FareServiceBundles, opt => opt.Ignore())
                .ForMember(dest => dest.Price, opt => opt.Ignore())
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.Terms.FareType)));

            CreateMap<Models.Booking, OfflineBookingDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.LegDurations, opt => opt.Ignore())
                .ForMember(dest => dest.LegSegments, opt => opt.Ignore())
                .ForMember(dest => dest.Baggage, opt => opt.Ignore())
                .ForMember(dest => dest.Reservations, opt => opt.Ignore())
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.Terms.FareType)))
                .ForMember(dest => dest.PaxName,
                    opt => opt.MapFrom(src => $"{src.Passenger.LastName} {src.Passenger.FirstName}".Trim()));

            CreateMap<List<Reservation>, PublicBookingDto>()
                .BeforeMap((reservations, dto) => SetupReservationsForVirtualBooking(reservations))
                .ForMember(dest => dest.LegDurations, opt => opt.MapFrom<LegDurationsResolver>())
                .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<SegmentsResolver>())
                .ForMember(dest => dest.Baggage, opt => opt.MapFrom<BaggageResolver>())
                .AfterMap(SetupSupplierLocators)
                .ForAllOtherMembers(opt => opt.Ignore());
            
            CreateMap<List<Reservation>, ItineraryReceiptBookingDto>()
                .BeforeMap((reservations, dto) => SetupReservationsForVirtualBooking(reservations))
                .ForMember(dest => dest.LegDurations, opt => opt.MapFrom<LegDurationsResolver>())
                .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<SegmentsResolver>())
                .ForMember(dest => dest.Baggage, opt => opt.MapFrom<BaggageResolver>())
                .AfterMap(SetupSupplierLocators)
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<List<Reservation>, OfflineBookingDto>()
                .BeforeMap((reservations, dto) => SetupReservationsForVirtualBooking(reservations))
                .ForMember(dest => dest.LegDurations, opt => opt.MapFrom<LegDurationsResolver>())
                .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<SegmentsResolver>())
                .ForMember(dest => dest.Baggage, opt => opt.MapFrom<BaggageResolver>())
                .ForMember(dest => dest.Price, opt => opt.MapFrom<PriceArchiveComponentsResolver>())
                .ForMember(dest => dest.Terms, opt => opt.MapFrom<BookingTermArchiveComponentsResolver>())
                .AfterMap(SetupSupplierLocators)
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<Metadata, BookingMetadataDto>()
                .ForMember(dest => dest.CustomFields, opt => opt.MapFrom<CustomFieldsResolver>());

            CreateMap<BookingTerms, BookingTermsDto>()
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.FareType)))
                .ForMember(dest => dest.Cancellations,
                    opt => opt.MapFrom(src => EnumHelper.ToString(src.Cancellations)))
                .ForMember(dest => dest.Changes, opt => opt.MapFrom(src => EnumHelper.ToString(src.Changes)))
                .ForMember(dest => dest.CancellationFee, opt => opt.Ignore())
                .ForMember(dest => dest.ChangeFee, opt => opt.Ignore())
                .ForMember(dest => dest.CancellationDeadline, opt => opt.Ignore())
                .ForMember(dest => dest.ChangeDeadline, opt => opt.Ignore())
                .ForMember(dest => dest.Components, opt => opt.Ignore());

            CreateMap<BookingPrice, BookingPriceDto>()
                .ForMember(dest => dest.Ccy, opt => opt.MapFrom(src => src.Currency))
                .ForMember(dest => dest.Components, opt => opt.Ignore());

            CreateMap<Leg, BookingLegDto>();

            CreateMap<Reservation, BaseReservationDto>()
                .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<SegmentsResolver>())
                .ForMember(dest => dest.Locator,
                    opt => opt.MapFrom(src => LocatorsHelper.GetProviderCode(src.Locators)))
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.PlatingCarrier, opt => opt.MapFrom(src => src.Fare.PlatingCarrier))
                .AfterMap(SetupSupplierLocators);

            CreateMap<Reservation, BriefReservationDto>()
                .IncludeBase<Reservation, BaseReservationDto>();

            CreateMap<Reservation, CompleteReservationDto>()
                .ForMember(dest => dest.CancellationTimeline,
                    opt => opt.PreCondition(r => r.CancellationTimeline != null))
                .ForMember(dest => dest.ChangeTimeline, opt => opt.PreCondition(r => r.ChangeTimeline != null))
                .ForMember(dest => dest.PartiallyUsedChangeTimeline, opt => opt.PreCondition(r => r.PartiallyUsedChangeTimeline != null))
                .ForMember(dest => dest.Locators, opt => opt.MapFrom<CompleteReservationLocatorsResolver>())
                .ForMember(dest => dest.Baggage, opt => opt.MapFrom<LegacyBaggageResolver>())
                .IncludeBase<Reservation, BriefReservationDto>()
                .AfterMap((r, dto) =>
                {
                    SetupSupplierLocators(dto.LegSegments, dto.Locators.SupplierLocators);
                    SetupCat16(dto.LegSegments, dto.FareRules, dto.FareRuleCat16Ids);
                });

            CreateMap<Reservation, CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.Reservation>()
                .ForMember(dest => dest.CancellationTimelines, opt => opt.MapFrom(src => src.CancellationTimeline));
            CreateMap<ReservationPrice,
                CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationPrice>();
            CreateMap<ReservationRefund,
                CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationRefund>();
            CreateMap<BaggageAllowance,
                CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.BaggageAllowance>();
            CreateMap<BaggageDetails, CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.BaggageDetails>();
            CreateMap<Ticket, CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationTicket>();
            CreateMap<Models.TicketPrice,
                CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationTicketPrice>();
            CreateMap<Models.TicketRefund,
                CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationTicketRefund>();
            CreateMap<TicketChanges,
                CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationTicketChanges>();
            CreateMap<ReservationPrice, CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.
                ReservationTicketChangePrice>();
            CreateMap<FrequentFlyerNumber, CTeleport.Messages.Commands.Models.FrequentFlyerNumber>();
            CreateMap<CTeleport.Services.Booking.Models.Ancillary, CTeleport.Messages.Commands.Models.Ancillary>();
            CreateMap<Models.Booking, CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.Booking>();
            CreateMap<BookingTerms, CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.BookingTerms>();
            CreateMap<Metadata, CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.BookingMetadata>();
            CreateMap<BookingState, CTeleport.Messages.Commands.Enums.BookingState>().ConvertUsing((value, _) =>
                value switch
                {
                    BookingState.Confirmed => Messages.Commands.Enums.BookingState.Confirmed,
                    BookingState.Cancelled => Messages.Commands.Enums.BookingState.Cancelled,
                    BookingState.Issued => Messages.Commands.Enums.BookingState.Issued,
                    BookingState.Used => Messages.Commands.Enums.BookingState.Used,
                    BookingState.RefundPending => Messages.Commands.Enums.BookingState.RefundPending,
                    BookingState.ChangePending => Messages.Commands.Enums.BookingState.ChangePending,
                    BookingState.ApprovalRequired => Messages.Commands.Enums.BookingState.ApprovalRequired,
                    BookingState.Declined => Messages.Commands.Enums.BookingState.Declined,
                    BookingState.PaymentRequired => Messages.Commands.Enums.BookingState.PaymentRequired,
                    _ => throw new ArgumentOutOfRangeException(nameof(value), value, "Mapping is not provided")
                });
            
            CreateMap<PaymentMethod, PaymentMethodType>().ConvertUsing((value, _) =>
                value switch
                {
                    PaymentMethod.BankTransfer => PaymentMethodType.BankTransfer,
                    PaymentMethod.AmexBta => PaymentMethodType.AmexBta,
                    PaymentMethod.CreditCard => PaymentMethodType.CreditCard,
                    _ => throw new ArgumentOutOfRangeException(nameof(value), value, "Mapping is not provided")
                });
            
            CreateMap<BookingPrice, CTeleport.Messages.Commands.Bookings.CommonObjects.Booking.BookingPrice>();
            CreateMap<Leg, CTeleport.Messages.Commands.Models.Leg>();
            CreateMap<Search.Shared.Models.User, CTeleport.Messages.Commands.Models.User>()
                .ForMember(x => x.TenantId, opt => opt.Ignore());

            CreateMap<Shared.Models.FareSurcharge, FareSurchargeDto>();

            CreateMap<Shared.Models.TaxDetail, TaxDetailDto>().ReverseMap();

            CreateMap<Shared.Models.Fare, FareDto>()
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.FareType)))
                .ForMember(dest => dest.Cancellations,
                    opt => opt.MapFrom(src => EnumHelper.ToString(src.Cancellations)))
                .ForMember(dest => dest.Changes, opt => opt.Ignore());

            CreateMap<Models.Ticket, TicketDto>()
                .ForMember(dest => dest.State, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)));

            CreateMap<Models.Ticket, CompleteTicketDto>()
                .IncludeBase<Models.Ticket, TicketDto>();
            CreateMap<ConditionsTimespan, CTeleport.Messages.Commands.Bookings.CommonObjects.Reservation.ReservationConditionsTimespan>()
                .ForMember(dest => dest.UntilDate, opt => opt.MapFrom(src => src.Until != null ? DateTimeOffset.FromUnixTimeSeconds(src.Until.Value).UtcDateTime : (DateTime?)null));

            CreateMap<Models.Booking, BaseBookingExportDto>()
                .ForMember(dest => dest.PaxFirstName, opt => opt.MapFrom(src => src.Passenger.FirstName))
                .ForMember(dest => dest.PaxLastName, opt => opt.MapFrom(src => src.Passenger.LastName))
                .ForMember(dest => dest.Nationality, opt => opt.MapFrom(src => src.Passenger.Nationality))
                .ForMember(dest => dest.CrewMember, opt => opt.MapFrom(src => src.Metadata.CrewChangeMember ?? "-"))
                .ForMember(dest => dest.VesselName, opt => opt.MapFrom(src => src.Metadata.VesselName ?? "-"))
                .ForMember(dest => dest.VesselFlag, opt => opt.MapFrom(src => src.Metadata.VesselFlag ?? "-"))
                .ForMember(dest => dest.PriceTotal, opt => opt.MapFrom(src => src.Price.Total))
                .ForMember(dest => dest.PricePerMile, opt => opt.MapFrom(src => src.Price.PerMile))
                .ForMember(dest => dest.PriceCcy, opt => opt.MapFrom(src => src.Price.Currency))
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.Terms.FareType)))
                .ForMember(dest => dest.CabinClass, opt => opt.Ignore())
                .ForMember(dest => dest.Itinerary, opt => opt.Ignore())
                .ForMember(dest => dest.DepartureDate, opt => opt.Ignore())
                .ForMember(dest => dest.TicketNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.InvoiceNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.CreditNoteNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.Invoicee, opt => opt.Ignore())
                .ForMember(dest => dest.CustomFields, opt => opt.Ignore())
                .ForMember(dest => dest.CO2Emissions, opt => opt.Ignore())
                .ForMember(dest => dest.BookingWindow,
                    opt => opt.MapFrom(src => src.DepartureAt.Subtract(src.CreatedAt).Days))
                .ForMember(dest => dest.BookedBy, opt => opt.MapFrom(src => src.CreatedBy.Name ?? src.CreatedBy.Email))
                .ForMember(dest => dest.BookedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)));

            CreateMap<Models.Booking, ExtendedBookingExportDto>()
                .ForMember(dest => dest.PaxFirstName, opt => opt.MapFrom(src => src.Passenger.FirstName))
                .ForMember(dest => dest.PaxLastName, opt => opt.MapFrom(src => src.Passenger.LastName))
                .ForMember(dest => dest.Nationality, opt => opt.MapFrom(src => src.Passenger.Nationality))
                .ForMember(dest => dest.CrewMember, opt => opt.MapFrom(src => src.Metadata.CrewChangeMember ?? "-"))
                .ForMember(dest => dest.VesselName, opt => opt.MapFrom(src => src.Metadata.VesselName ?? "-"))
                .ForMember(dest => dest.VesselFlag, opt => opt.MapFrom(src => src.Metadata.VesselFlag ?? "-"))
                .ForMember(dest => dest.PriceTotal, opt => opt.MapFrom(src => src.Price.Total))
                .ForMember(dest => dest.PricePerMile, opt => opt.MapFrom(src => src.Price.PerMile))
                .ForMember(dest => dest.PriceCcy, opt => opt.MapFrom(src => src.Price.Currency))
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => EnumHelper.ToString(src.Terms.FareType)))
                .ForMember(dest => dest.CabinClass, opt => opt.Ignore())
                .ForMember(dest => dest.Itinerary, opt => opt.Ignore())
                .ForMember(dest => dest.DepartureDate, opt => opt.Ignore())
                .ForMember(dest => dest.TicketNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.InvoiceNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.CreditNoteNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.Invoicee, opt => opt.Ignore())
                .ForMember(dest => dest.CustomFields, opt => opt.Ignore())
                .ForMember(dest => dest.BookingWindow,
                    opt => opt.MapFrom(src => src.DepartureAt.Subtract(src.CreatedAt).Days))
                .ForMember(dest => dest.BookedBy, opt => opt.MapFrom(src => src.CreatedBy.Name ?? src.CreatedBy.Email))
                .ForMember(dest => dest.BookedAt, opt => opt.MapFrom(src => src.CreatedAt))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => EnumHelper.ToString(src.State)))
                .ForMember(dest => dest.BookingType, opt => opt.MapFrom(src => "Air"))
                .ForMember(dest => dest.AirlineName, opt => opt.Ignore())
                .ForMember(dest => dest.IATAUsed, opt => opt.Ignore())
                .ForMember(dest => dest.OfficeId, opt => opt.Ignore())
                .ForMember(dest => dest.OriginAirport, opt => opt.Ignore())
                .ForMember(dest => dest.OriginCity, opt => opt.Ignore())
                .ForMember(dest => dest.OriginCountry, opt => opt.Ignore())
                .ForMember(dest => dest.DestinationAirport, opt => opt.Ignore())
                .ForMember(dest => dest.DestinationCity, opt => opt.Ignore())
                .ForMember(dest => dest.DestinationCountry, opt => opt.Ignore())
                .ForMember(dest => dest.CO2Emissions, opt => opt.Ignore());

            CreateMap<List<Reservation>, BaseBookingExportDto>()
                .BeforeMap((reservations, dto) => SetupReservationsForVirtualBooking(reservations))
                .ForMember(dest => dest.CabinClass, opt => opt.MapFrom<CabinClassResolver>())
                .ForMember(dest => dest.Itinerary, opt => opt.MapFrom<ItineraryStringResolver>())
                .ForMember(dest => dest.DepartureDate, opt => opt.MapFrom<DepartureDateResolver>())
                .ForMember(dest => dest.TicketNumbers, opt => opt.MapFrom<TicketNumbersResolver>())
                .ForMember(dest => dest.InvoiceNumbers, opt => opt.MapFrom<InvoiceNumbersResolver>())
                .ForMember(dest => dest.CreditNoteNumbers, opt => opt.MapFrom<CreditNoteNumbersResolver>())
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<List<Reservation>, ExtendedBookingExportDto>()
                .BeforeMap((reservations, dto) => SetupReservationsForVirtualBooking(reservations))
                .ForMember(dest => dest.CabinClass, opt => opt.MapFrom<CabinClassResolver>())
                .ForMember(dest => dest.Itinerary, opt => opt.MapFrom<ItineraryStringResolver>())
                .ForMember(dest => dest.DepartureDate, opt => opt.MapFrom<DepartureDateResolver>())
                .ForMember(dest => dest.TicketNumbers, opt => opt.MapFrom<TicketNumbersResolver>())
                .ForMember(dest => dest.InvoiceNumbers, opt => opt.MapFrom<InvoiceNumbersResolver>())
                .ForMember(dest => dest.CreditNoteNumbers, opt => opt.MapFrom<CreditNoteNumbersResolver>())
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<IEnumerable<InvoiceDto>, BaseBookingExportDto>()
                .ForMember(dest => dest.Invoicee, opt => opt.MapFrom<InvoiceeResolver>())
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<IEnumerable<InvoiceDto>, ExtendedBookingExportDto>()
                .ForMember(dest => dest.Invoicee, opt => opt.MapFrom<InvoiceeResolver>())
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<IEnumerable<LegacyModels.CustomOption>, BaseBookingExportDto>()
                .ForMember(dest => dest.CustomFields, opt => opt.MapFrom<CustomFieldsesolver>())
                .ForAllOtherMembers(opt => opt.Ignore());

            CreateMap<IEnumerable<LegacyModels.CustomOption>, ExtendedBookingExportDto>()
                .ForMember(dest => dest.CustomFields, opt => opt.MapFrom<CustomFieldsesolver>())
                .ForAllOtherMembers(opt => opt.Ignore());

            //CreateMap<CTeleport.Services.Travelport.Models.ReservationPassenger, Shared.Models.ReservationPassenger>();

            CreateMap<CTeleport.Services.Search.Shared.Models.TechnicalStop, TechnicalStop>();

            CreateMap<CreateReservationParams, Reservation>()
                .BeforeMap((@params, reservation) =>
                {
                    if (reservation.Fare == null)
                    {
                        reservation.Fare = new CTeleport.Services.Booking.Shared.Models.Fare();
                    }
                })
                .ForMember(dest => dest.Id, opt => opt.MapFrom(y => y.ReservationId))
                .ForMember(dest => dest.IsVirtual, opt => opt.MapFrom(y => y.IsVirtual))
                .ForMember(dest => dest.ApprovalRequired, opt => opt.MapFrom(y => y.ApprovalRequired))
                .ForMember(dest => dest.PaymentRequired, opt => opt.MapFrom(y => y.PaymentRequired))
                .ForMember(dest => dest.OriginalReservationId, opt => opt.MapFrom(y => y.OriginalReservationId))
                .ForMember(dest => dest.CanCancel, opt => opt.MapFrom(y => y.CanCancel))
                .ForMember(dest => dest.Ticketless, opt => opt.MapFrom(y => y.Ticketless))
                .ForMember(dest => dest.DepartureAt, opt => opt.MapFrom(y => y.DepartureAt))
                .ForMember(dest => dest.LegDurations, opt => opt.MapFrom(y => y.LegDurations))
                .ForMember(dest => dest.State, opt => opt.Ignore())
                .ForMember(dest => dest.Irregularities, opt => opt.Ignore())
                .ForMember(dest => dest.Price, opt => opt.Ignore())
                .ForMember(dest => dest.Refund, opt => opt.Ignore())
                .ForMember(dest => dest.Fare, opt => opt.Ignore())
                .ForMember(dest => dest.Source, opt => opt.MapFrom(y => y.ProviderKey.GetSourceFromProviderKey()))
                .ForMember(dest => dest.OriginalFare, opt => opt.Ignore())
                .ForMember(dest => dest.RefreshFareAt, opt => opt.Ignore())
                .ForMember(dest => dest.TicketingAt, opt => opt.Ignore())
                .ForMember(dest => dest.TicketingFailed, opt => opt.Ignore())
                .ForMember(dest => dest.Tickets, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Locators, opt => opt.Ignore())
                .ForMember(dest => dest.SupplierLocators, opt => opt.Ignore())
                .ForMember(dest => dest.Baggage, opt => opt.Ignore())
                .ForMember(dest => dest.Metadata, opt => opt.MapFrom(y => y.ReservationMetadata))
                .ForMember(dest => dest.LegSegments,
                    opt => opt.MapFrom<LegSegmentsFromCreateReservationParamsResolver>())
                .ForMember(dest => dest.Legs, opt => opt.MapFrom<LegsFromCreateReservationParamsResolver>())
                .ForMember(dest => dest.FareRules, opt => opt.Ignore())
                .ForMember(dest => dest.FareRulesIds, opt => opt.Ignore())
                .ForMember(dest => dest.InvoiceNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.CreditNoteNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.ClassDropIsAllowed, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.CancelledAt, opt => opt.Ignore())
                .ForMember(dest => dest.CancellationTimeline, opt => opt.Ignore())
                .ForMember(dest => dest.ChangeTimeline, opt => opt.Ignore())
                .ForMember(dest=> dest.PartiallyUsedChangeTimeline, opt=> opt.Ignore())
                .ForMember(dest => dest.FareRuleCat16Ids, opt => opt.Ignore())
                .ForMember(dest => dest.Changes, opt => opt.Ignore())
                .ForMember(dest => dest.FrequentFlyerNumbers, opt => opt.Ignore())
                .ForMember(dest => dest.FareChanges, opt => opt.Ignore())
                .AfterMap((@params, reservation) =>
                {
                    reservation.LegDurations = ReservationMapper.MapDuration(@params.LegSegments);
                    reservation.Fare.BaseCurrency = @params.BaseCurrency;
                    reservation.Fare.Calc = @params.FareCalc;
                    reservation.Fare.LatestTicketingTime = @params.LatestTicketingTime;
                    reservation.Fare.FareType = @params.FareType;
                    reservation.Fare.PlatingCarrier = @params.PlatingCarrier;
                    reservation.Fare.PrivateFareCode = @params.PrivateFareCode;
                });

            CreateMap<IList<IList<FlightSegment>>, ICollection<ICollection<Segment>>>();
            CreateMap<ICollection<ICollection<Segment>>, IList<IList<FlightSegment>>>();
            CreateMap<Shared.Models.Segment, CTeleport.Messages.Commands.Models.Segment>();

//                CreateMap<FlightReservation, Reservation>()
//                    .ForMember(dest => dest.Id, opt => opt.Ignore())
//                    .ForMember(dest => dest.Metadata, opt => opt.Ignore())
//                    .ForMember(dest => dest.BookingId, opt => opt.Ignore())
//                    .ForMember(dest => dest.State, opt => opt.Ignore())
//                    .ForMember(dest => dest.Irregularities, opt => opt.Ignore())
//                    .ForMember(dest => dest.Price, opt => opt.Ignore())
//                    .ForMember(dest => dest.Refund, opt => opt.Ignore())
//                    .ForMember(dest => dest.FareRules, opt => opt.Ignore())
//                    .ForMember(dest => dest.FareComponents, opt => opt.Ignore())
//                    .ForMember(dest => dest.OriginalFare, opt => opt.Ignore())
//                    .ForMember(dest => dest.RefreshFareAt, opt => opt.Ignore())
//                    .ForMember(dest => dest.TicketingAt, opt => opt.Ignore())
//                    .ForMember(dest => dest.TicketingFailed, opt => opt.Ignore())
//                    .ForMember(dest => dest.Tickets, opt => opt.Ignore())
//                    .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
//                    .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
//                    .ForMember(dest => dest.TenantId, opt => opt.Ignore())
//                    .ForMember(dest => dest.ApprovalRequired, opt => opt.Ignore())
//                    .ForMember(dest => dest.PaymentRequired, opt => opt.Ignore())
//                    .ForMember(dest => dest.Locators, opt => opt.ResolveUsing<ReservationLocatorsResolver>())
//                    .ForMember(dest => dest.SupplierLocators, opt => opt.MapFrom(s => s.Locators.SupplierLocators))
//                    .ForMember(dest => dest.Legs, opt => opt.ResolveUsing<LegsFromLegSegmentsResolver>())
//#pragma warning disable CS0612 // Type or member is obsolete
//                    .ForMember(dest => dest.Baggage, opt => opt.Ignore())
//#pragma warning restore CS0612 // Type or member is obsolete
//                    .ForMember(dest => dest.BaggageAllowances, opt => opt.ResolveUsing<BaggageAllowancesFromFlightReservationResolver>())
//                    .ForMember(dest => dest.DepartureAt, opt => opt.ResolveUsing<DepartureFromFlightReservationAtResolver>())
//                    .ForMember(dest => dest.LegDurations, opt => opt.ResolveUsing<LegDurationsFromLegSegmentsResolver>())
//                    .ForMember(dest => dest.LegSegments, opt => opt.ResolveUsing<LegSegmentsFromFlightReservationResolver>())
//                    .ForMember(dest => dest.InvoiceNumbers, opt => opt.Ignore())
//                    .ForMember(dest => dest.CreditNoteNumbers, opt => opt.Ignore())
//                    .ForMember(dest => dest.ClassDropIsAllowed, opt => opt.UseValue(true))
//                    .ForMember(dest => dest.CancelledAt, opt => opt.Ignore())
//                    .ForMember(dest => dest.CancellationTimeline, opt => opt.Ignore())
//                    .ForMember(dest => dest.IsVirtual, opt => opt.Ignore())
//                    .ForMember(dest => dest.OriginalReservationId, opt => opt.Ignore())
//                    .ForMember(dest => dest.SearchJobMetadata, opt => opt.Ignore())
//                    .ForMember(dest => dest.FareRuleCat16Ids, opt => opt.Ignore())
//                    .ForMember(dest => dest.Changes, opt => opt.Ignore())
//                    .ForMember(dest => dest.FrequentFlyerNumbers, opt => opt.Ignore());

//                CreateMap<CTeleport.Services.Travelport.Models.TaxDetail, Shared.Models.TaxDetail>()
//                    .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount.PriceToDecimal()))
//                    .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Amount.ExtractCurrency()));

//                CreateMap<CTeleport.Services.Travelport.Models.Fare, Shared.Models.Fare>()
//                    .ForMember(dest => dest.Calc, opt => opt.MapFrom(src => src.FareCalc))
//                    .ForMember(dest => dest.Base, opt => opt.MapFrom(src => src.BasePrice.PriceToDecimal()))
//                    .ForMember(dest => dest.ApproximateBase, opt => opt.MapFrom(src => src.ApproximateBasePrice.PriceToDecimal()))
//                    .ForMember(dest => dest.Net, opt => opt.MapFrom(src => src.TotalPrice.PriceToDecimal()))
//                    .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.ApproximateBasePrice.ExtractCurrency()))
//                    .ForMember(dest => dest.BaseCurrency, opt => opt.MapFrom(src => src.BasePrice.ExtractCurrency()))
//                    .ForMember(dest => dest.PaxType, opt => opt.MapFrom(src => src.PassengerType))
//                    .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => src.PassengerType.Equals("SEA") ? FareType.Marine : FareType.Public))
//                    .ForMember(dest => dest.Taxes, opt => opt.MapFrom(src => src.Taxes.ToDictionary(t => t.Key, t => t.Value.PriceToDecimal())))
//                    .ForMember(dest => dest.TaxDetails, opt => opt.MapFrom(src => src.TaxDetails))
//                    .ForMember(dest => dest.Surcharges, opt => opt.ResolveUsing<FareSurchargesResolver>())
//                    .ForMember(dest => dest.IsValid, opt => opt.MapFrom(src => src.IsValid))
//                    .ForMember(dest => dest.Conditions, opt => opt.Ignore())
//                    .ForMember(dest => dest.Cancellations, opt => opt.Ignore())
//                    .ForMember(dest => dest.IsCheapest, opt => opt.Ignore())
//                    .ForMember(dest => dest.NonRefAmounts, opt => opt.Ignore())
//                    .ForMember(dest => dest.NoShowTimestamp, opt => opt.Ignore())
//                    .ForMember(dest => dest.NoShowConditions, opt => opt.Ignore());

            CreateMap<PassengerDetails, PassengerDetailsDto>()
                .ForMember(dest => dest.Gender,
                    opt => opt.MapFrom(src => src.Gender.Value == Search.Shared.Enums.Gender.Male ? "M" : "F"))
                .ForMember(dest => dest.DocType, opt => opt.MapFrom(src => "P")) // TODO: support other doc types
                .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => SanitizeName(src.FirstName)))
                .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => SanitizeName(src.LastName)))
                .ForMember(dest => dest.Dob, opt => opt.MapFrom(src => src.DateOfBirth));

            CreateMap<Messages.Commands.Models.Passenger, PassengerDetails>()
                .ForMember(dest => dest.DocType,
                    opt => opt.MapFrom(src => CTeleport.Services.Booking.Shared.Enums.DocumentType.Passport))
                .ForMember(dest => dest.Gender,
                    opt => opt.MapFrom(src =>
                        (src.Gender == "M" || src.Gender == "Male")
                            ? Search.Shared.Enums.Gender.Male
                            : Search.Shared.Enums.Gender.Female));

            CreateMap<Messages.Commands.Models.PassengerUpdate, MutablePassengerDetails>()
                .ForMember(dest => dest.DocType,
                    opt => opt.MapFrom(src => CTeleport.Services.Booking.Shared.Enums.DocumentType.Passport));

            CreateMap<PassengerDetails, Shared.Models.ReservationPassenger>();

            CreateMap<Messages.Commands.Models.BookingMetadata, Metadata>()
                .ForMember(dest => dest.CustomFields, opt => opt.MapFrom(x => x.CustomFields));

            CreateMap<CTeleport.Services.Booking.Models.InvoiceRefund, CTeleport.Messages.Commands.Models.InvoiceRefund>();
            CreateMap<Messages.Commands.Models.InvoiceRefund, InvoiceRefund>();
            CreateMap<Messages.Commands.Models.User, CTeleport.Services.Search.Shared.Models.User>()
                .ForMember(dest => dest.Roles, opt => opt.Ignore());

            CreateMap<Common.Authorization.Models.User, CTeleport.Services.Search.Shared.Models.User>();

            CreateMap<RequestInfo, RequestInfoEntity>()
                .ForMember(dest => dest.IpAddress, opt => opt.MapFrom(x => x.IPAddress));

            //CreateMap<BookedSegment, Segment>()
            //    .ForMember(dest => dest.DepartureDate, opt => opt.MapFrom(src => src.DepartureTime.ExtractDate()))
            //    .ForMember(dest => dest.DepartureTime, opt => opt.MapFrom(src => src.DepartureTime.ExtractTime()))
            //    .ForMember(dest => dest.DepartureTimestamp, opt => opt.MapFrom(src => src.DepartureTime.DateToTimestamp()))
            //    .ForMember(dest => dest.DepartureTimestampUtc, opt => opt.MapFrom(src => src.DepartureTime.DateToUtcTimestamp()))
            //    .ForMember(dest => dest.ArrivalDate, opt => opt.MapFrom(src => src.ArrivalTime.ExtractDate()))
            //    .ForMember(dest => dest.ArrivalTime, opt => opt.MapFrom(src => src.ArrivalTime.ExtractTime()))
            //    .ForMember(dest => dest.ArrivalTimestamp, opt => opt.MapFrom(src => src.ArrivalTime.DateToTimestamp()))
            //    .ForMember(dest => dest.ArrivalTimestampUtc, opt => opt.MapFrom(src => src.ArrivalTime.DateToUtcTimestamp()))
            //    .ForMember(dest => dest.OperatorName, opt => opt.MapFrom(src => src.Carrier != src.OperatingCarrier && src.OperatingCarrier == null ? src.CodeshareInfo : null))
            //    .ForMember(dest => dest.Operator, opt => opt.MapFrom(src => src.OperatingCarrier != null && src.Carrier != src.OperatingCarrier ? src.OperatingCarrier : null))
            //    .ForMember(dest => dest.ConnectionDuration, opt => opt.Ignore())
            //    .ForMember(dest => dest.FareComponent, opt => opt.Ignore())
            //    //TODO why it fails?
            //    .ForMember(dest => dest.FareTier, opt => opt.Ignore());

            CreateMap<TravelPolicies.Models.ApprovalQueueItem, Models.ApprovalQueueItem>()
                .ForMember(x => x.RepricingRequired, opts => opts.Ignore());

            CreateMap<BaseBookingDto, ApprovalItem>().ForMember(x => x.ViolatedRuleIds, opts => opts.Ignore())
                .ForMember(x => x.ExpiresAt, opts => opts.Ignore())
                .ForMember(x => x.OutOfPolicyReasons, opts => opts.Ignore());

            CreateMap<TravelPolicies.Models.ApprovalQueueItem, ApprovalItem>()
                .ForMember(x => x.ViolatedRuleIds, opts => opts.Ignore())
                .ForMember(x => x.OutOfPolicyReasons, opts => opts.Ignore())
                .ForMember(x => x.ExpiresAt, opts => opts.MapFrom(i => i.ExpireAt))
                .AfterMap((src, dst) =>
                {
                    dst.ViolatedRuleIds = src.ViolatedRules?.Select(rule => rule.Id).ToArray();
                    dst.OutOfPolicyReasons = src.ViolatedRules?.Select(rule => rule.Name).ToArray();
                })
                .ForAllOtherMembers(x => x.Ignore());

            CreateMap<PendingApprovalBundle, ApprovalItem>()
                .ForMember(x => x.ViolatedRuleIds, opts => opts.Ignore())
                .ForMember(x => x.OutOfPolicyReasons, opts => opts.Ignore())
                .ForMember(x => x.ExpiresAt, opts => opts.MapFrom(i => i.ExpiresAt))
                .AfterMap((src, dst) =>
                {
                    dst.ViolatedRuleIds = src.ViolatedRulesIds.ToArray();
                    dst.OutOfPolicyReasons = src.OutOfPolicyReasons.ToArray();
                })
                .ForAllOtherMembers(x => x.Ignore());

            CreateMap<Reservation, AlternativeFlightSolutionsRequest>()
                .ForMember(dest => dest.DepartureDate, opt => opt.Ignore())
                .ForMember(dest => dest.CorrelationId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.SearchJobId, opt => opt.MapFrom(src => src.SearchJobMetadata.SearchJobId))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Price.OriginalCurrency))
                .ForMember(dest => dest.FareType, opt => opt.MapFrom(src => src.Fare.FareType))
                .ForMember(dest => dest.PlatingCarrier, opt => opt.MapFrom(src => src.Fare.PlatingCarrier))
                .ForMember(dest => dest.ProhibitPenaltyFares,
                    opt => opt.MapFrom(src => src.SearchJobMetadata.ProhibitedPenaltyFares))
                .ForMember(dest => dest.DisableAlternativeFlow, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.PrivateFareCode, opt => opt.MapFrom(src => src.Fare.PrivateFareCode))
                .ForMember(dest => dest.LegSegments, opt => opt.MapFrom<AlternativeSegmentsResolver>());

            CreateMap<Shared.Models.TaxDetail, CTeleport.Messages.Commands.Models.TaxDetails>();

            CreateMap<Messages.Commands.Bookings.UpdateVessel, VesselDetails>();

            CreateMap<Shared.Models.FrequentFlyerNumber, Messages.Commands.Models.FrequentFlyerNumber>()
                .ReverseMap();

            CreateMap<Shared.Models.FrequentFlyerNumber, FrequentFlyerNumber>()
                .ForMember(dest => dest.ProgramName, opt => opt.Ignore())
                .ReverseMap();

            CreateMap<Messages.Commands.Models.FrequentFlyerNumber, FrequentFlyerNumber>()
                .ForMember(dest => dest.ProgramName, opt => opt.Ignore())
                .ReverseMap();

            CreateMap<ExtraServiceModel, ExtraServiceDto>();

            CreateMap<ExtraServiceMetadataModel, ExtraServiceMetadataDto>();

            CreateMap<ExtraServicePriceModel, ExtraServicePriceDto>()
                .ForMember(dest => dest.Total, opt => opt.MapFrom(src => src.Sale.TotalPrice))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Sale.Currency));

            CreateMap<Refund, ExtraServiceRefundDto>()
                .ForMember(dest => dest.Total, opt => opt.MapFrom(src => src.SaleTotal))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.SaleCurrency));
        }

        private static string SanitizeName(string name)
        {
            return name?.Replace("\u00A0", " ").Replace("\u00a0", " ");
        }

        private static void SetupReservationsForVirtualBooking(List<Reservation> reservations)
        {
            if (reservations.Any(r => !r.IsVirtual))
            {
                var originalIds = reservations.Where(r => !r.IsVirtual).Select(r => r.OriginalReservationId);
                reservations.RemoveAll(r => r.IsVirtual && originalIds.Contains(r.Id));
            }

            if (reservations.Any(r => !r.ApprovalRequired))
            {
                reservations.RemoveAll(r => r.ApprovalRequired);
            }
        }

        private static void SetupSupplierLocators(IEnumerable<Reservation> reservations, CompleteBookingDto dto)
        {
            foreach (var reservation in reservations)
            {
                SetupSupplierLocators(dto.LegSegments, reservation);
            }
        }

        private static void SetupSupplierLocators(Reservation reservation, BaseReservationDto dto)
        {
            SetupSupplierLocators(dto.LegSegments, reservation.SupplierLocators);
        }

        private static void SetupSupplierLocators(IEnumerable<Reservation> reservations, PublicBookingDto dto)
        {
            foreach (var reservation in reservations)
            {
                SetupSupplierLocators(dto.LegSegments, reservation);
            }
        }

        private static void SetupSupplierLocators(IEnumerable<Reservation> reservations, ItineraryReceiptBookingDto dto)
        {
            foreach (var reservation in reservations)
            {
                SetupSupplierLocators(dto.LegSegments, reservation);
            }
        }
        
        private static void SetupSupplierLocators(IEnumerable<Reservation> reservations, OfflineBookingDto dto)
        {
            foreach (var reservation in reservations)
            {
                SetupSupplierLocators(dto.LegSegments, reservation);
            }
        }

        public static void SetupCat16(List<List<SegmentDto>> legSegments,
            Dictionary<string, List<Search.Shared.Models.FareRuleSection>> fareRules, List<string> fareRuleCat16Ids)
        {
            if (legSegments is null || fareRules is null || fareRuleCat16Ids is null)
            {
                return;
            }

            try
            {
                var link = fareRules.Select((item, index) => new
                {
                    OD = item.Key,
                    Cat16 = index >= fareRuleCat16Ids.Count ? null : fareRuleCat16Ids[index]
                }).ToDictionary(x => x.OD, y => y.Cat16);

                string start = null;
                var segmentGroup = new List<SegmentDto>();

                foreach (var segments in legSegments)
                {
                    start = null;
                    segmentGroup = new List<SegmentDto>();

                    foreach (var segment in segments)
                    {
                        if (start == null)
                        {
                            start = segment.Origin;
                            segmentGroup = new List<SegmentDto>();
                        }

                        var key = $"{start}-{segment.Destination}";

                        if (link.ContainsKey(key))
                        {
                            var cat16Id = link[key];
                            segmentGroup.Add(segment);

                            foreach (var item in segmentGroup)
                            {
                                item.Cat16Id = cat16Id;
                                item.FareComponent = key;
                            }

                            start = null;
                            segmentGroup = new List<SegmentDto>();
                        }
                        else
                        {
                            segmentGroup.Add(segment);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                //throw;
            }
        }

        private static void SetupSupplierLocators(List<List<SegmentDto>> legSegments, Reservation reservation)
        {
            if (legSegments == null || reservation?.SupplierLocators == null)
            {
                return;
            }

            var splitOds = reservation.LegSegments
                .SelectMany(ls => ls)
                .Where(s => !string.IsNullOrEmpty(s.SplitOD))
                .Select(s => s.SplitOD)
                .Distinct()
                .ToList();

            foreach (var leg in legSegments)
            {
                foreach (var segment in leg)
                {
                    if (reservation.SupplierLocators.ContainsKey(segment.Carrier) &&
                        (splitOds.Contains(segment.SplitOd) || segment.SplitOd.IsNullOrEmpty()))
                    {
                        segment.Locator = reservation.SupplierLocators[segment.Carrier];
                    }
                }
            }
        }

        private static void SetupSupplierLocators(List<List<SegmentDto>> legSegments,
            IDictionary<string, string> locators)
        {
            if (legSegments == null || locators == null || !locators.Any())
            {
                return;
            }

            foreach (var leg in legSegments)
            {
                foreach (var segment in leg)
                {
                    if (locators.ContainsKey(segment.Carrier))
                    {
                        segment.Locator = locators[segment.Carrier];
                    }
                }
            }
        }
    }

    public class AlternativeSegmentsResolver : IValueResolver<Reservation, AlternativeFlightSolutionsRequest,
        IList<IList<Search.Core.Dto.ProviderFlightSegment>>>
    {
        public IList<IList<Search.Core.Dto.ProviderFlightSegment>> Resolve(Reservation source,
            AlternativeFlightSolutionsRequest destination,
            IList<IList<Search.Core.Dto.ProviderFlightSegment>> destMember, ResolutionContext context)
        {
            return source.LegSegments.Select(s =>
                s.Select(x => new Search.Core.Dto.ProviderFlightSegment()
                {
                    ArrivalDate = x.ArrivalDate,
                    ArrivalTime = x.ArrivalTime,
                    DepartureDate = x.DepartureDate,
                    DepartureTime = x.DepartureTime,
                    Carrier = x.Carrier,
                    Connecting = x.Connecting,
                    Destination = x.Destination,
                    FlightNumber = x.FlightNumber,
                    Origin = x.Origin,
                    CabinClass = x.CabinClass,
                    BookingCode = x.BookingCode,
                    FareBasis = x.FareBasis,
                    FareTier = x.FareTier
                }).ToArray()
            ).ToArray();
        }
    }

    public class
        CompleteReservationLocatorsResolver : IValueResolver<Reservation, CompleteReservationDto, Models.Locators>
    {
        public Models.Locators Resolve(Reservation source, CompleteReservationDto destination,
            Models.Locators destMember, ResolutionContext context)
        {
            return new Models.Locators
            {
                AirReservation = source.Locators?.ContainsKey(LocatorNames.AIR_RESERVATION) == true
                    ? source.Locators[LocatorNames.AIR_RESERVATION]
                    : null,
                Provider = source.Locators?.ContainsKey(LocatorNames.PROVIDER) == true
                    ? source.Locators[LocatorNames.PROVIDER]
                    : null,
                UniversalRecord = source.Locators?.ContainsKey(LocatorNames.UNIVERSAL_RECORD) == true
                    ? source.Locators[LocatorNames.UNIVERSAL_RECORD]
                    : null,
                SupplierLocators = source.SupplierLocators
            };
        }
    }

    //public class ReservationLocatorsResolver : IValueResolver<FlightReservation, Reservation, Dictionary<string, string>>
    //{
    //    public Dictionary<string, string> Resolve(FlightReservation source, Reservation destination, Dictionary<string, string> destMember, ResolutionContext context)
    //    {
    //        var locators = new Dictionary<string, string>();

    //        locators.Add(Common.Constants.LocatorNames.PROVIDER, source.Locators.Provider);
    //        locators.Add(Common.Constants.LocatorNames.AIR_RESERVATION, source.Locators.AirReservation);
    //        locators.Add(Common.Constants.LocatorNames.UNIVERSAL_RECORD, source.Locators.UniversalRecord);

    //        return locators;
    //    }
    //}

    public class BookingRefundResolver : IValueResolver<IEnumerable<Reservation>, object, BookingRefundDto>
    {
        public BookingRefundDto Resolve(IEnumerable<Reservation> source, object destination,
            BookingRefundDto destMember, ResolutionContext context)
        {
            var total = 0m;

            foreach (var reservation in source)
            {
                if (reservation.Refund != null)
                {
                    total += reservation.Refund.Total;
                    continue;
                }

                if (reservation.State == ReservationState.Cancelled)
                {
                    // Cancelled reservation without ticket is fully refunded
                    if (!reservation.Tickets.Any() && reservation.Ticketless == false)
                    {
                        total += reservation.Price.Total;
                    }
                }
            }

            return new BookingRefundDto
            {
                Total = total
            };
        }
    }

    public class CustomFieldsResolver : IValueResolver<Metadata, object, Dictionary<string, CustomFieldValue>>
    {
        public Dictionary<string, CustomFieldValue> Resolve(Metadata source, object destination,
            Dictionary<string, CustomFieldValue> destMember, ResolutionContext context)
        {
            if (source.CustomFields != null)
            {
                return source.CustomFields.ToDictionary(x => x.Key, y => new CustomFieldValue()
                {
                    Title = y.Key.Replace('_', ' '),
                    Value = y.Value
                });
            }

            return new Dictionary<string, CustomFieldValue>();
        }
    }

    public class LegDurationsResolver : IValueResolver<IEnumerable<Reservation>, object, IList<int>>
    {
        public IList<int> Resolve(IEnumerable<Reservation> source, object destination, IList<int> member,
            ResolutionContext context)
        {
            if (source.Count() == 1)
            {
                // NOTE: single ticket booking
                return source.First().LegDurations.ToList();
            }

            var legsTotal = (int)context.Options.Items["LegsTotal"];
            if (legsTotal == 1)
            {
                // NOTE: split ticket booking
                return new[]
                {
                    (source.Last().LegSegments.First().Last().ArrivalTimestampUtc -
                     source.First().LegSegments.First().First().DepartureTimestampUtc) / 60
                };
            }

            return source
                .Select(r =>
                    (r.LegSegments.Last().Last().ArrivalTimestampUtc -
                     r.LegSegments.First().First().DepartureTimestampUtc) / 60)
                .ToList();
        }
    }

    public class ArrivalUtcResolver : IValueResolver<Models.Booking, BaseBookingDto, int>
    {
        public int Resolve(Models.Booking source, BaseBookingDto destination, int destMember, ResolutionContext context)
        {
            if (source.Legs != null && source.Legs.Any())
            {
                return source.Legs.Max(l => l.ArrivalUtc);
            }

            return destMember;
        }
    }

    public class SegmentsResolver : IValueResolver<IEnumerable<Reservation>, object, List<List<SegmentDto>>>,
        IValueResolver<Reservation, object, List<List<SegmentDto>>>
    {
        public List<List<SegmentDto>> Resolve(IEnumerable<Reservation> source, object destination,
            List<List<SegmentDto>> member, ResolutionContext context)
        {
            if (source.Count() == 1)
            {
                // NOTE: single ticket booking
                var legSegments = context.Mapper.Map<List<List<SegmentDto>>>(source.First().LegSegments);

                return legSegments;
            }

            var legsTotal = (int)context.Options.Items["LegsTotal"];
            if (legsTotal == 1)
            {
                // NOTE: split ticket booking
                var firstPart = context.Mapper.Map<List<List<SegmentDto>>>(source.First().LegSegments).ToList();
                var secondPart = context.Mapper.Map<List<List<SegmentDto>>>(source.Last().LegSegments).ToList();

                var lastLegFromFirstPart = firstPart.First().Last();
                var firstLegFromSecondPart = secondPart.First().First();
                var departure = firstLegFromSecondPart.DepartureTimestampUtc.FromUnixTime();
                var arrival = lastLegFromFirstPart.ArrivalTimestampUtc.FromUnixTime();

                lastLegFromFirstPart.Splitting = true;
                lastLegFromFirstPart.Connecting = true;
                lastLegFromFirstPart.ConnectionDuration = (int)departure.Subtract(arrival).TotalMinutes;

                var result = firstPart.Select(l => l.Concat(secondPart.First()).ToList()).ToList();
                return result;
            }

            return source
                .Select(r => context.Mapper.Map<List<List<SegmentDto>>>(r.LegSegments).ToList())
                .SelectMany(s => s)
                .ToList();
        }

        public List<List<SegmentDto>> Resolve(Reservation source, object destination, List<List<SegmentDto>> destMember,
            ResolutionContext context)
        {
            return context.Mapper.Map<List<List<SegmentDto>>>(source.LegSegments);
        }
    }

    public class FareRulesResolver : IValueResolver<IEnumerable<Reservation>, object,
        Dictionary<string, List<Search.Shared.Models.FareRuleSection>>>
    {
        public Dictionary<string, List<Search.Shared.Models.FareRuleSection>> Resolve(IEnumerable<Reservation> source,
            object destination, Dictionary<string, List<Search.Shared.Models.FareRuleSection>> member,
            ResolutionContext context)
            => source.Select(r => r.FareRules).Aggregate(DictionaryHelper.UnionWithOverride).NewIfNull();
    }

    public class PriceComponentsResolver : IValueResolver<IEnumerable<Reservation>, CompleteBookingDto, BookingPriceDto>
    {
        public BookingPriceDto Resolve(IEnumerable<Reservation> source, CompleteBookingDto destination,
            BookingPriceDto member, ResolutionContext context)
        {
            if (destination.Terms.Splitting)
            {
                member.Components = source.Select(r => new
                    {
                        OD = $"{r.LegSegments.First().First().Origin}-{r.LegSegments.First().Last().Destination}",
                        Price = r.Price.Total
                    })
                    .ToDictionary(r => r.OD, r => r.Price);
            }

            return member;
        }
    }

    public class PriceArchiveComponentsResolver : IValueResolver<IEnumerable<Reservation>, OfflineBookingDto, BookingPriceDto>
    {
        public BookingPriceDto Resolve(IEnumerable<Reservation> source, OfflineBookingDto destination,
            BookingPriceDto member, ResolutionContext context)
        {
            if (destination.Terms != null && destination.Terms.Splitting)
            {
                member.Components = source.Select(r => new
                    {
                        OD = $"{r.LegSegments.First().First().Origin}-{r.LegSegments.First().Last().Destination}",
                        Price = r.Price.Total
                    })
                    .ToDictionary(r => r.OD, r => r.Price);
            }

            return member;
        }
    }
    
    public class BookingTermComponentsResolver : IValueResolver<IEnumerable<Reservation>, CompleteBookingDto, BookingTermsDto>
    {
        public BookingTermsDto Resolve(IEnumerable<Reservation> source, CompleteBookingDto destination,
            BookingTermsDto member, ResolutionContext context)
        {
            if (destination.Terms.Splitting)
            {
                member.Components = source.Select(r => new
                    {
                        OD = $"{r.LegSegments.First().First().Origin}-{r.LegSegments.First().Last().Destination}",
                        Terms = new BookingComponentTermsDto
                        {
                            FareType = EnumHelper.ToString(r.Fare.FareType),
                            Cancellations = EnumHelper.ToString(r.Fare.Cancellations),
                            Changes = EnumHelper.ToString(r.Fare.Changes)
                            // TODO: (CT-2384) in future, apply CanCancel property while splitting tickets
                            //CanCancel = r.CanCancel
                        }
                    })
                    .ToDictionary(r => r.OD, r => r.Terms);
            }

            return member;
        }
    }

    public class
        BookingTermArchiveComponentsResolver : IValueResolver<IEnumerable<Reservation>, OfflineBookingDto,
        BookingTermsDto>
    {
        public BookingTermsDto Resolve(IEnumerable<Reservation> source, OfflineBookingDto destination,
            BookingTermsDto member, ResolutionContext context)
        {
            if (destination.Terms != null && destination.Terms.Splitting)
            {
                member.Components = source.Select(r => new
                    {
                        OD = $"{r.LegSegments.First().First().Origin}-{r.LegSegments.First().Last().Destination}",
                        Terms = new BookingComponentTermsDto
                        {
                            FareType = EnumHelper.ToString(r.Fare.FareType),
                            Cancellations = EnumHelper.ToString(r.Fare.Cancellations),
                            Changes = EnumHelper.ToString(r.Fare.Changes)
                            // TODO: (CT-2384) in future, apply CanCancel property while splitting tickets
                            //CanCancel = r.CanCancel
                        }
                    })
                    .ToDictionary(r => r.OD, r => r.Terms);
            }

            return member;
        }
    }

    /// <summary>
    /// Remove then admin UI will be updated
    /// </summary>
    [Obsolete]
    public class LegacyBaggageResolver : IValueResolver<Reservation, object, BaggageAllowance>
    {
        public BaggageAllowance Resolve(Reservation source,
            object destination, BaggageAllowance member,
            ResolutionContext context)
        {
            return source.BaggageAllowances?.Values?.FirstOrDefault() ?? new BaggageAllowance()
            {
                Carryon = new Search.Shared.Models.BaggageDetails(),
                Checked = new Search.Shared.Models.BaggageDetails()
            };
        }
    }

    public class
        BaggageResolver : IValueResolver<IEnumerable<Reservation>, object, IDictionary<string, BaggageAllowance>>
    {
        public IDictionary<string, BaggageAllowance> Resolve(IEnumerable<Reservation> source,
            object destination, IDictionary<string, BaggageAllowance> member,
            ResolutionContext context)
        {
            var baggage = new Dictionary<string, BaggageAllowance>();

            foreach (var reservation in source)
            {
                if (reservation.BaggageAllowances != null)
                {
                    foreach (var item in reservation.BaggageAllowances)
                    {
                        baggage[item.Key] = item.Value;
                    }
                }
            }

            return baggage;
        }
    }

    public class CustomFieldsesolver : IValueResolver<IEnumerable<LegacyModels.CustomOption>, BaseBookingExportDto,
        Dictionary<string, string>>
    {
        public Dictionary<string, string> Resolve(IEnumerable<LegacyModels.CustomOption> source,
            BaseBookingExportDto destination, Dictionary<string, string> member, ResolutionContext context)
        {
            var result = new Dictionary<string, string>();
            var duplicateColumnIndex = new Dictionary<string, int>();

            foreach (var column in source)
            {
                var columnName = duplicateColumnIndex.TryGetValue(column.Key, out var index)
                    ? column.Key + $" ({index + 1})"
                    : column.Key;

                duplicateColumnIndex[column.Key] = ++index;

                result.Add(columnName, column.Value);
            }

            return result;
        }
    }

    public class CabinClassResolver : IValueResolver<IEnumerable<Reservation>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<Reservation> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
        {
            var classes = source.SelectMany(r => r.LegSegments.SelectMany(l => l.Select(s => s.CabinClass))).Distinct();
            return classes.Count() == 1 ? classes.First() : "Various classes";
        }
    }

    public class ItineraryStringResolver : IValueResolver<IEnumerable<Reservation>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<Reservation> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
        {
            var legs = new List<string> { };

            foreach (var reservation in source)
            {
                var origin = reservation.LegSegments.First().First().Origin;
                var destinations = reservation.LegSegments.SelectMany(l => l.Select(s => s.Destination));
                legs.Add(string.Join(" - ", destinations.Prepend(origin)));
            }

            return string.Join(", ", legs);
        }
    }

    public class DepartureDateResolver : IValueResolver<IEnumerable<Reservation>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<Reservation> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
            => source.OrderBy(r => r.DepartureAt).First().LegSegments.First().First().DepartureDate;
    }

    public class TicketNumbersResolver : IValueResolver<IEnumerable<Reservation>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<Reservation> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
        {
            var tickets = source.SelectMany(r => r.Tickets?.Select(t => t.Number) ?? new string[0]).ToList();
            return tickets.Count > 0 ? string.Join(", ", tickets) : string.Empty;
        }
    }

    public class InvoiceNumbersResolver : IValueResolver<IEnumerable<Reservation>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<Reservation> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
        {
            var invoices = source.SelectMany(r => r.InvoiceNumbers ?? new string[0]).Distinct().ToList();
            return invoices.Count > 0 ? string.Join(", ", invoices) : string.Empty;
        }
    }

    public class CreditNoteNumbersResolver : IValueResolver<IEnumerable<Reservation>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<Reservation> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
        {
            var invoices = source.SelectMany(r => r.CreditNoteNumbers ?? new string[0]).Distinct().ToList();
            return invoices.Count > 0 ? string.Join(", ", invoices) : string.Empty;
        }
    }

    public class InvoiceeResolver : IValueResolver<IEnumerable<InvoiceDto>, BaseBookingExportDto, string>
    {
        public string Resolve(IEnumerable<InvoiceDto> source, BaseBookingExportDto destination, string member,
            ResolutionContext context)
        {
            var invoicee = source.Select(r => r.Invoicee).Distinct().ToList();
            return invoicee.Count > 0 ? string.Join(", ", invoicee) : string.Empty;
        }
    }

    //public class BaggageAllowancesFromFlightReservationResolver : IValueResolver<FlightReservation, Reservation, IDictionary<string, BaggageAllowance>>
    //{
    //    public IDictionary<string, BaggageAllowance> Resolve(FlightReservation source, Reservation destination, IDictionary<string, BaggageAllowance> member, ResolutionContext context)
    //    {
    //        var dic = new Dictionary<string, BaggageAllowance>();

    //        if (source.BaggageAllowances != null)
    //        {
    //            foreach (var baggageAllowance in source.BaggageAllowances)
    //            {
    //                var item = new BaggageAllowance
    //                {
    //                    Checked = new Search.Shared.Models.BaggageDetails
    //                    {
    //                        Pcs = baggageAllowance.Value.NumberOfPieces,
    //                        Weight = (int?)baggageAllowance.Value.MaxWeightKg,
    //                        Url = baggageAllowance.Value.Url
    //                    }
    //                };

    //                if (source.CarryOnAllowances?.ContainsKey(baggageAllowance.Key) == true)
    //                {
    //                    var carryOn = source.CarryOnAllowances[baggageAllowance.Key];
    //                    if (source.CarryOnAllowances != null)
    //                    {
    //                        item.Carryon = new Search.Shared.Models.BaggageDetails
    //                        {
    //                            Pcs = carryOn.NumberOfPieces,
    //                            Weight = (int?)carryOn.MaxWeightKg,
    //                            Url = carryOn.Url
    //                        };
    //                    }
    //                }

    //                dic[baggageAllowance.Key] = item;
    //            }
    //        }

    //        return dic;
    //    }
    //}

    //public class DepartureFromFlightReservationAtResolver : IValueResolver<FlightReservation, Reservation, DateTime>
    //{
    //    public DateTime Resolve(FlightReservation source, Reservation destination, DateTime member, ResolutionContext context)
    //        => source.Segments.First().DepartureTime.ToDateTime();
    //}

    public class
        LegsFromCreateReservationParamsResolver : IValueResolver<CreateReservationParams, Reservation, ICollection<Leg>>
    {
        public ICollection<Leg> Resolve(CreateReservationParams source, Reservation destination,
            ICollection<Leg> destMember, ResolutionContext context)
        {
            if (source.LegSegments == null)
            {
                throw new ArgumentNullException("LegSegments");
            }

            return source.LegSegments.Select(l => new Leg
            {
                Origin = l.First().Origin,
                Destination = l.Last().Destination,
                Departure = l.First().DepartureTimestamp,
                DepartureUtc = l.First().DepartureTimestampUtc,
                Arrival = l.Last().ArrivalTimestamp,
                ArrivalUtc = l.Last().ArrivalTimestampUtc,
                IsDirect = l.Count == 1
            }).ToList();
        }
    }

    //public class LegsFromLegSegmentsResolver : IValueResolver<FlightReservation, Reservation, ICollection<Leg>>
    //{
    //    public ICollection<Leg> Resolve(FlightReservation source, Reservation destination, ICollection<Leg> destMember, ResolutionContext context)
    //    {
    //        if (!context.Items.ContainsKey("LegSegments") || !(context.Items["LegSegments"] is IList<IList<FlightSegment>> legSegments))
    //        {
    //            throw new ArgumentNullException("LegSegments");
    //        }

    //        return legSegments.Select(l => new Leg
    //        {
    //            Origin = l.First().Origin,
    //            Destination = l.Last().Destination,
    //            Departure = l.First().DepartureTimestamp,
    //            DepartureUtc = l.First().DepartureTimestampUtc,
    //            Arrival = l.Last().ArrivalTimestamp,
    //            ArrivalUtc = l.Last().ArrivalTimestampUtc,
    //            IsDirect = l.Count == 1
    //        }).ToList();
    //    }
    //}

    //public class LegDurationsFromLegSegmentsResolver : IValueResolver<FlightReservation, Reservation, ICollection<int>>
    //{
    //    public ICollection<int> Resolve(FlightReservation source, Reservation destination, ICollection<int> member, ResolutionContext context)
    //    {
    //        if (!context.Items.ContainsKey("LegSegments") || !(context.Items["LegSegments"] is IList<IList<FlightSegment>> legSegments))
    //        {
    //            throw new ArgumentNullException("LegSegments");
    //        }

    //        var durations = new List<int>();
    //        foreach (var leg in legSegments)
    //        {
    //            var firstSegmentDeparture = leg.First().DepartureTimestampUtc.FromUnixTime();
    //            var secondLegArrival = leg.Last().ArrivalTimestampUtc.FromUnixTime();

    //            var travelduration = (int)(secondLegArrival - firstSegmentDeparture).TotalMinutes;
    //            durations.Add(travelduration);
    //        }

    //        return durations;
    //    }
    //}

    public class LegSegmentsFromCreateReservationParamsResolver : IValueResolver<CreateReservationParams, Reservation,
        ICollection<ICollection<Segment>>>
    {
        public ICollection<ICollection<Segment>> Resolve(CreateReservationParams source, Reservation destination,
            ICollection<ICollection<Segment>> destMember, ResolutionContext context)
        {
            var legSegments = new List<ICollection<Segment>>();

            foreach (var leg in source.LegSegments)
            {
                var segments = new List<Segment>();

                foreach (var sourceSegment in leg)
                {
                    var technicalStops = sourceSegment.TechnicalStops is null
                        ? Array.Empty<TechnicalStop>()
                        : context.Mapper.Map<TechnicalStop[]>(sourceSegment.TechnicalStops);

                    var segment = new Segment
                    {
                        Carrier = sourceSegment.Carrier,
                        FlightNumber = sourceSegment.FlightNumber,
                        Origin = sourceSegment.Origin,
                        Destination = sourceSegment.Destination,
                        DepartureDate = sourceSegment.DepartureDate,
                        DepartureTime = sourceSegment.DepartureTime,
                        DepartureTimestamp = sourceSegment.DepartureTimestamp,
                        DepartureTimestampUtc = sourceSegment.DepartureTimestampUtc,
                        ArrivalDate = sourceSegment.ArrivalDate,
                        ArrivalTime = sourceSegment.ArrivalTime,
                        ArrivalTimestamp = sourceSegment.ArrivalTimestamp,
                        ArrivalTimestampUtc = sourceSegment.ArrivalTimestampUtc,
                        OriginTerminal = sourceSegment.OriginTerminal,
                        DestinationTerminal = sourceSegment.DestinationTerminal,
                        Connecting = sourceSegment.Connecting,
                        BookingCode = sourceSegment.BookingCode,
                        FareBasis = sourceSegment.FareBasis,
                        CabinClass = sourceSegment.CabinClass,
                        Operator = sourceSegment.Operator,
                        OperatorName = sourceSegment.OperatorName,
                        FareTier = sourceSegment.FareTier,
                        FareComponent = sourceSegment.FareComponent,
                        SplitOD = sourceSegment.SplitOD,
                        EquipmentCode = sourceSegment.EquipmentCode,
                        FareFamilyName = sourceSegment.FareFamilyName,
                        TechnicalStops = technicalStops
                    };

                    segments.Add(segment);
                }

                for (var i = 0; i < segments.Count - 1; i++)
                {
                    var departure = segments[i + 1].DepartureTimestampUtc.FromUnixTime();
                    var arrival = segments[i].ArrivalTimestampUtc.FromUnixTime();
                    segments[i].ConnectionDuration = (int)departure.Subtract(arrival).TotalMinutes;
                }

                legSegments.Add(segments);
            }

            return legSegments;
        }
    }

    //public class LegSegmentsFromFlightReservationResolver : IValueResolver<FlightReservation, Reservation, ICollection<ICollection<Segment>>>
    //{
    //    public ICollection<ICollection<Segment>> Resolve(FlightReservation source,
    //        Reservation destination, ICollection<ICollection<Segment>> member, ResolutionContext context)
    //    {
    //        if (!context.Items.ContainsKey("LegSegments") || !(context.Items["LegSegments"] is IList<IList<FlightSegment>> legs))
    //        {
    //            throw new ArgumentNullException("LegSegments");
    //        }

    //        var legSegments = new List<ICollection<Segment>>();

    //        foreach (var leg in legs)
    //        {
    //            var segments = new List<Segment>();

    //            foreach (var expectedSegment in leg)
    //            {
    //                var sourceSegment = source.Segments.FirstOrDefault(s =>
    //                    s.Carrier == expectedSegment.Carrier && s.FlightNumber == expectedSegment.FlightNumber);

    //                // NOTE: It is possible for split flight, because it splits into multiple reservations
    //                if (sourceSegment == null)
    //                {
    //                    continue;
    //                }

    //                //TODO Find a way to reuse BookedSegment to Segment mapping
    //                var segment = new Segment
    //                {
    //                    Carrier = sourceSegment.Carrier,
    //                    FlightNumber = sourceSegment.FlightNumber,
    //                    Origin = sourceSegment.Origin,
    //                    Destination = sourceSegment.Destination,
    //                    DepartureDate = sourceSegment.DepartureTime.ExtractDate(),
    //                    DepartureTime = sourceSegment.DepartureTime.ExtractTime(),
    //                    DepartureTimestamp = sourceSegment.DepartureTime.DateToTimestamp(),
    //                    DepartureTimestampUtc = sourceSegment.DepartureTime.DateToUtcTimestamp(),
    //                    ArrivalDate = sourceSegment.ArrivalTime.ExtractDate(),
    //                    ArrivalTime = sourceSegment.ArrivalTime.ExtractTime(),
    //                    ArrivalTimestamp = sourceSegment.ArrivalTime.DateToTimestamp(),
    //                    ArrivalTimestampUtc = sourceSegment.ArrivalTime.DateToUtcTimestamp(),
    //                    OriginTerminal = sourceSegment.OriginTerminal,
    //                    DestinationTerminal = sourceSegment.DestinationTerminal,
    //                    Connecting = sourceSegment.Connecting,
    //                    BookingCode = sourceSegment.BookingCode,
    //                    FareBasis = sourceSegment.FareBasis,
    //                    CabinClass = sourceSegment.CabinClass,
    //                    FareComponent = sourceSegment.FareComponent
    //                };

    //                if (segment.Carrier != sourceSegment.OperatingCarrier)
    //                {
    //                    if (sourceSegment.OperatingCarrier != null)
    //                    {
    //                        segment.Operator = sourceSegment.OperatingCarrier;
    //                    }
    //                    else
    //                    {
    //                        segment.OperatorName = sourceSegment.CodeshareInfo;
    //                    }
    //                }

    //                segments.Add(segment);
    //            }

    //            for (var i = 0; i < segments.Count - 1; i++)
    //            {
    //                var departure = segments[i + 1].DepartureTimestampUtc.FromUnixTime();
    //                var arrival = segments[i].ArrivalTimestampUtc.FromUnixTime();
    //                segments[i].ConnectionDuration = (int)departure.Subtract(arrival).TotalMinutes;
    //            }

    //            legSegments.Add(segments);
    //        }

    //        return legSegments;
    //    }
    //}

    //public class FareSurchargesResolver : IValueResolver<CTeleport.Services.Travelport.Models.Fare, Shared.Models.Fare, ICollection<Shared.Models.FareSurcharge>>
    //{
    //    public ICollection<Shared.Models.FareSurcharge> Resolve(CTeleport.Services.Travelport.Models.Fare source, Shared.Models.Fare destination, ICollection<Shared.Models.FareSurcharge> member, ResolutionContext context)
    //    {
    //        if (source.Fares == null)
    //        {
    //            return null;
    //        }

    //        var surcharges = new List<Shared.Models.FareSurcharge>();

    //        foreach (var fareInfoPair in source.Fares)
    //        {
    //            if (fareInfoPair.Value.Surcharges == null || !fareInfoPair.Value.Surcharges.Any())
    //            {
    //                continue;
    //            }

    //            surcharges.AddRange(fareInfoPair.Value.Surcharges.Select(s => new Shared.Models.FareSurcharge
    //            {
    //                Segment = $"{fareInfoPair.Value.Origin}-{fareInfoPair.Value.Destination}",
    //                Amount = s.Amount.PriceToDecimal(),
    //                Currency = s.Amount.ExtractCurrency(),
    //                Type = s.Type
    //            }));
    //        }

    //        return surcharges.Any() ? surcharges : null;
    //    }
    //}
    //public class TravelerPrefixResolver : IValueResolver<PassengerDetails, Traveler, string>
    //{
    //    public string Resolve(PassengerDetails source, Traveler destination, string destMember, ResolutionContext context)
    //    {
    //        // in case of single name we form correct lastname+firstname pare
    //        // so we don't want to use prefix. it might be added before.
    //        // see SingleNameService for more details
    //        if (source.SingleNameOnly
    //            && source.FirstName.Equals(NamePrefixes.MALE, StringComparison.InvariantCultureIgnoreCase)
    //            || source.FirstName.Equals(NamePrefixes.FEMALE, StringComparison.InvariantCultureIgnoreCase))
    //        {
    //            return null;
    //        }

    //        switch (source.Gender)
    //        {
    //            case Gender.Male:
    //                return NamePrefixes.MALE;
    //            case Gender.Female:
    //                return NamePrefixes.FEMALE;
    //            default:
    //                return null;
    //        }
    //    }
    //}
}