using System.Collections.Generic;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Clients
{
    public interface IAuthzClient
    {
        /// <summary>
        /// Run the query against authorization policies
        /// </summary>
        /// <param name="resourceClass">Class of the resource, e.g. "bookings"</param>
        /// <param name="operation">Operation to authorize, e.g. "create"</param>
        /// <param name="sub">Subject (user) to authorize</param>
        /// <param name="resource">Resource to authorize access to, such as a booking instance</param>
        Task<bool> IsOperationAllowedAsync(string resourceClass, string operation, AuthzSubject sub, object resource = null);

        /// <summary>
        /// Compiles filter for partial evaluation
        /// </summary>
        /// <param name="query">Query to compile against, e.g. "data.authz.bookings.allow == true"</param>
        /// <param name="operation">Operation to authorize, e.g. "create"</param>
        /// <param name="unknowns">Array of unknown portions of data to compile against, e.g. ["input.resource"]</param>
        /// <param name="sub">Subject (user) to authorize</param>
        /// <returns></returns>
        Task<Authz.PartialResult> CompileAsync(string query, string operation, IEnumerable<string> unknowns, AuthzSubject sub);

        /// <summary>
        /// Gets list of subject (user) scopes
        /// </summary>
        /// <param name="sub">Subject (user) to get scopes for</param>
        /// <returns>List of scopes</returns>
        Task<string[]> GetSubjectScopesAsync(AuthzSubject sub);
    }

    /// <summary>
    /// Authorization subject, i.e. user
    /// </summary>
    public class AuthzSubject
    {
        /// <summary>
        /// User id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// User roles
        /// </summary>
        public string[] Roles { get; set; }

        /// <summary>
        /// Tenant user belongs to
        /// </summary>
        public string TenantId { get; set; }
    }
}