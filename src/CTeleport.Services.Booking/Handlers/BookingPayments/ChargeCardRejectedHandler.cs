using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.CreditCardPayments;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Services;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Handlers
{
    /// <summary>
    /// Dispatches RejectBookingSaga command if saga is still in InPayment state
    /// </summary>
    public class ChargeCardRejectedHandler : IEventHandler<ChargeCardRejected>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly ILogger _logger;

        public ChargeCardRejectedHandler(
            IHandlerFactory handlerFactory,
            IMessageDispatcher dispatcher,
            IBookingSagaService bookingSagaService,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _dispatcher = dispatcher;
            _bookingSagaService = bookingSagaService;
            _logger = logger;
        }

        public async Task HandleAsync(ChargeCardRejected @event)
        {
            _logger.Information("Handling {@Event}", @event);
            BookingSaga saga = null;

            await _handlerFactory
                .Create(@event)
                .Run(async () =>
                {
                    saga = await _bookingSagaService.GetByPaymentIdAsync(@event.PaymentId);

                    if (saga == null)
                    {
                        throw new NotFoundException("Booking saga not found");
                    }
                })
                .OnSuccess(async () =>
                {
                    if (saga.SagaPaymentState is BookingSagaPaymentState.InPayment)
                    {
                       await _dispatcher.DispatchAsync(new BookingPaymentRejected(
                            requestId: @event.PaymentId,
                            bookingId: saga.Id,
                            code: @event.Code,
                            reason: @event .Reason));
                    }
                })
                .OnError(async (ex) =>
                {
                    if (!(ex is CTeleportException)) throw ex;
                })
                .ExecuteAsync();
        }
    }
}
