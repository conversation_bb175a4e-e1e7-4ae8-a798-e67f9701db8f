using System;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Booking.Commands;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Services;
using Serilog;

namespace CTeleport.Services.Booking.Handlers
{
    /// <summary>
    /// Cancels the credit card payment for rejected saga
    /// </summary>
    public class CancelPaymentForBookingHandler : ICommandHandler<CancelPaymentForBooking>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IBookingPaymentService _bookingPaymentService;
        private readonly ILogger _logger;

        public CancelPaymentForBookingHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IBookingPaymentService bookingPaymentService,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _bookingPaymentService = bookingPaymentService;
            _logger = logger;
        }

        public async Task HandleAsync(CancelPaymentForBooking command)
        {
            _logger.Information("Handling {@Command}", command);

            Models.Booking booking = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    booking = await _bookingService.GetBookingAsync(command.BookingId);

                    if (booking == null)
                    {
                        throw new NotFoundException("Booking not found");
                    }

                    if (!(booking.State == BookingState.Cancelled || booking.State == BookingState.Declined))
                        throw new ValidationException($"Cannot cancel payment for booking in {booking.State} state");

                    if (booking.PaymentMethod != PaymentMethod.CreditCard)
                        throw new ValidationException($"Cannot cancel payment for method {booking.PaymentMethod}");
                    
                    if (String.IsNullOrEmpty(booking.PaymentId))
                        throw new ValidationException("Booking does not have payment id associated");
                })
                .Run(async () =>
                {
                    var reservations = (await _bookingService.GetBookingReservationsAsync(booking.Id)).ToArray();
                    var tickets = reservations.SelectMany(r => r.Tickets).Count() + reservations.Where(r => r.Ticketless && !r.IsVirtual).Count();

                    if (tickets > 0)
                    {
                        // NOTE: when booking has ticket(s), payment could have been captured already.
                        // Billing service shall handle payment cancellation or refund.
                        _logger.Information("Skipping payment cancellation for booking {BookingId}. It has tickets.", command.BookingId);
                        return;
                    }

                    await _bookingPaymentService.CancelPaymentAsync(booking.Id);
                })
                .OnError(async (ex) =>
                {
                    _logger.Error(ex, $"Could not cancel payment for booking");
                })
                .Lock("Bookings/" + command.BookingId)
                .ExecuteAsync();
        }
    }
}