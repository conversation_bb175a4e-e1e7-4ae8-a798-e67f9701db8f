using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Services;
using Serilog;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class SetBookingCommentHandler : ICommandHandler<SetBookingComment>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IAuthService _authService;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;

        public SetBookingCommentHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            IAuthService authService,
            IMapper mapper,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _authService = authService;
            _dispatcher = dispatcher;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task HandleAsync(SetBookingComment command)
        {
            Models.Booking booking = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    booking = await _bookingService.GetBookingAsync(command.BookingId);
                    if (booking == null)
                    {
                        throw new ValidationException("Booking not found");
                    }

                    await _authService.ValidateIfBookingCanBeManagedOrFailAsync(booking);
                })
                .Run(async () =>
                {
                    var user = _mapper.Map<Search.Shared.Models.User>(command.User);
                    await _bookingService.UpdateBookingComment(command.BookingId, command.Comment, user);
                })
                .OnSuccess(async () =>
                {
                    await _dispatcher.DispatchAsync(new BookingCommentSet(command.Request.Id, booking.Id));
                })
                .OnError(async (ex) =>
                {
                    _logger.Error(ex, "Error occured while setting comment to a booking.");
                    await _dispatcher.DispatchAsync(new SetBookingCommentRejected(command.Request.Id, booking.Id, OperationCodes.Error, ex.Message));
                })
                .ExecuteAsync();
        }
    }
}