using CTeleport.Common.Exceptions;
using CTeleport.Common.Extensions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.CheckFare;
using CTeleport.Services.CheckFare.Dto;
using CTeleport.Services.Helpers;
using Serilog;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Helpers.Extensions;

namespace CTeleport.Services.Booking.Handlers
{
    public class CheckReservationIfCheapestFareSetHandler : ICommandHandler<CheckReservationIfCheapestFareSet>
    {
        private readonly IProviderCheckFareService _checkFareService;
        private readonly IBookingService _bookingService;
        private readonly IHandlerFactory _handlerFactory;
        private readonly ILogger _logger;
        
        public CheckReservationIfCheapestFareSetHandler(
            IProviderCheckFareService checkFareService,
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            ILogger logger)
        {
            _checkFareService = checkFareService;
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _logger = logger;
        }

        public async Task HandleAsync(CheckReservationIfCheapestFareSet command)
        {
            Models.Reservation reservation = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Couldn't find reservation");
                    }
                })
                .Run(async () =>
                {
                    if (reservation.Fare.IsCheapest.HasValue && reservation.Fare.IsCheapest.Value)
                    {
                        return;
                    }

                    if (!reservation.Source.CanBeRepriced())
                    {
                        return; //TODO
                    }

                    var farebasises = reservation.LegSegments.SelectMany(l => l.Select(s => s.FareBasis)).Distinct().ToList();
                    if (farebasises.Count > 1)
                    {
                        // NOTE: we couldn't check combined fares
                        return;
                    }

                    TripType tripType;
                    switch (reservation.Legs.Count)
                    {
                        case 1:
                            tripType = TripType.OneWay;
                            break;
                        case 2:
                            if (reservation.Legs.First().Origin == reservation.Legs.Last().Destination 
                                && reservation.Legs.First().Destination == reservation.Legs.Last().Origin)
                            {
                                tripType = TripType.Return;
                            }
                            else
                            {
                                // NOTE: we couldn't check Open-jaw ticket
                                return;
                            }
                            break;
                        default:
                            // NOTE: we couldn't check multi-city flights
                            return;
                    }

                    var segmentCarriers = reservation.LegSegments.SelectMany(l => l.Select(s => s.Carrier)).Distinct()
                        .ToList();
                    if (segmentCarriers.Count > 1)
                    {
                        // NOTE: We can't determine which carrier published the fare
                        return;
                    }

                    var request = new CheckFareRequest
                    {
                        Carrier = segmentCarriers.First(),
                        Currency = reservation.Fare.BaseCurrency,
                        Provider = reservation.Source.GetProvider(),
                        Pcc = reservation.Source.GetPcc(),
                        Origin = reservation.Legs.First().Origin,
                        Destination = reservation.Legs.First().Destination,
                        TripType = tripType,
                        Ptc = reservation.Fare.PaxType
                    };

                    var response = await _checkFareService.CheckFareAsync(request);
                    var fares = response.Fares.OrderBy(f => f.Amount).ToList();
                    var basis = farebasises.First();
                    var currentFare = fares.FirstOrDefault(f => f.Farebasis == basis);
                    if (currentFare == null)
                    {
                        _logger.Information("Couldn't find fare {Basis} in fares of {Airline}", basis, reservation.Fare.PlatingCarrier);
                        return;
                    }

                    var isCheapest = fares.IndexOf(currentFare) == 0;
                    if (isCheapest)
                    {
                        await _bookingService.SetReservationFareIsCheapestAsync(reservation, true);
                    }
                    else
                    {
                        await _bookingService.SetReservationFareIsCheapestAsync(reservation, false);
                    }
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Command} command", nameof(CheckReservationIfCheapestFareSet));
                })
                .ExecuteAsync();
        }
    }
}
