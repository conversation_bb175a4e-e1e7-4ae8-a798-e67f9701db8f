using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Search.Shared.Models;
using System;
using System.Threading.Tasks;
using Serilog;
using Serilog.Context;
using CTeleport.Common.Exceptions;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Services;
using System.Linq;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Commands;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Helpers;
using CTeleport.Services.SearchProxy.Services;
using Logging = CTeleport.Services.Helpers.Logging;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Handlers
{
    public class CreateReservationHandler(
        IHandlerFactory handlerFactory,
        ILogger logger,
        IMessageDispatcher dispatcher,
        IBookingService bookingService,
        IBookingSagaService bookingSagaService,
        IFlightSolutionService searchService,
        IReservationBuilder reservationBuilder,
        IFareRulesProcessor fareRulesProcessor)
        :
            ICommandHandler<CreateReservation>,
            IEventHandler<CreateReservationRejected>,
            IEventHandler<ReservationCreated>
    {
        public async Task HandleAsync(CreateReservation command)
        {
            BookingSaga saga = null;
            FlightSolution flightSolution = null;
            bool exemptLiTax = false;
            Reservation reservation = null;

            await handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    saga = await bookingSagaService.GetAsync(command.BookingId);

                    if (saga == null)
                    {
                        throw new Exception("Saga was not found.");
                    }

                    var canCreateReservation = saga.SagaState == BookingSagaState.Created
                                               || saga.SagaState == BookingSagaState.InApproval
                                               || saga.SagaState == BookingSagaState.Approved
                                               || saga.SagaPaymentState == BookingSagaPaymentState.PaymentConfirmed;
                    if (!canCreateReservation)
                    {
                        throw new Exception($"Cannot create reservation. Saga state:{saga.SagaState}, paymentState:{saga.SagaPaymentState}");
                    }

                    flightSolution = await searchService.GetFlightSolutionAsync(saga.FlightSolutionEntity.FlightSolutionId);

                    if (flightSolution == null)
                    {
                        throw new Exception("Flight solution was not found.");
                    }

                    exemptLiTax = saga.BookingEntity.Price.IsLiTaxApplied;
                    var canExemptLiTax = flightSolution.CanExemptLiTax.HasValue && flightSolution.CanExemptLiTax.Value;
                    if (exemptLiTax && !canExemptLiTax)
                    {
                        throw new ValidationException(OperationCodes.Error, "Saga requires exempt LI tax, but flight solution doesn't support it.");
                    }
                    
                    if (exemptLiTax && !saga.FlightSolutionEntity.Passenger.Nationality.Equals("PH"))
                    {
                        throw new ValidationException(OperationCodes.Error, "Saga requires exempt LI tax, but pax nationality isn't PH.");
                    }

                    // LI tax exemption is allowed only for seamen
                    if (exemptLiTax && string.IsNullOrWhiteSpace(saga.FlightSolutionEntity.VesselName))
                    {
                        throw new ValidationException(OperationCodes.Error, "Saga requires exempt LI tax, but vessel name is not set.");
                    }
                })
                .Run(async () =>
                {
                    using (LogContext.PushProperty(Logging.SEARCH_JOB_ID_PROPERTY_NAME, flightSolution?.SearchJobId ?? "n/a"))
                    {
                        // TODO: note that reservation's identity is not based on provider key, it needs to be refactored
                        var sagaReservation = saga.Reservations.Single(r => string.Equals(r.ProviderKey, command.ProviderKey));

                        var reservationParams = await reservationBuilder.BuildReservationParams(command.RequestId, command.ProviderKey, flightSolution, saga, sagaReservation, exemptLiTax);
                        var draftReservation = await bookingService.BuildDraftReservation(reservationParams);

                        if (!sagaReservation.IsVirtualized)
                        {
                            saga = await bookingSagaService.InitReservationAsync(new InitSagaReservationRequest
                            {
                                BookingSagaId = saga.Id,
                                ReservationId = sagaReservation.Id,
                                CreateReservationParams = reservationParams,
                                DraftReservation = draftReservation,
                                VirtualReservationId = reservationParams.IsVirtual
                                    ? reservationParams.ReservationId
                                    : default,
                                VirtualReservationLocator = reservationParams.IsVirtual
                                    ? $"C-{RandomUtils.GetRandomString(7)}"
                                    : default
                            });
                        }

                        reservation = await CreateReservation(command, draftReservation, flightSolution, saga, reservationParams);
                    }
                })
                .OnCustomError(async ex =>
                {
                    var errorCode = ex.Code ?? OperationCodes.Error;
                    using (LogContext.PushProperty(Logging.SEARCH_JOB_ID_PROPERTY_NAME, flightSolution?.SearchJobId ?? "n/a"))
                    {
                        logger.Information("Booking failure - {ErrorCode}", errorCode);
                    }

                    await dispatcher.DispatchAsync(new CreateReservationRejected(command.RequestId,
                        command.BookingId, command.ProviderKey, errorCode, ex.Message, reservation?.Id));
                })
                .OnError(async ex =>
                {
                    using (LogContext.PushProperty(Logging.SEARCH_JOB_ID_PROPERTY_NAME, flightSolution?.SearchJobId ?? "n/a"))
                    {
                        logger.Error(ex, "Booking failure - {ErrorCode}", OperationCodes.Error);
                    }
                    await dispatcher.DispatchAsync(new CreateReservationRejected(command.RequestId,
                        command.BookingId, command.ProviderKey, OperationCodes.Error, ex.Message, reservation?.Id));
                })
                .ExecuteAsync();
        }

        public async Task<Reservation> CreateReservation(
            CreateReservation command,
            Reservation draftReservation,
            FlightSolution flightSolution,
            BookingSaga saga,
            CreateReservationParams reservationParams)
        {
            Reservation createdReservation;
            if (reservationParams.IsVirtual)
            {
                createdReservation = await bookingService.CreateVirtualReservation(command.ProviderKey, draftReservation, flightSolution, reservationParams);

                await dispatcher.DispatchAsync(new ReservationCreated
                {
                    BookingId = command.BookingId,
                    RequestId = command.RequestId,
                    ProviderKey = command.ProviderKey,
                    IsVirtual = true,
                    Source = createdReservation.Source,
                    TenantId = createdReservation.TenantId,
                    CreatedAt = createdReservation.CreatedAt,
                    ReservationId = createdReservation.Id,
                    CreatedBy = saga.BookingEntity.CreatedBy.Name ?? saga.BookingEntity.CreatedBy.Email,
                    IsApprovalRequired = draftReservation.ApprovalRequired,
                });
            }
            else
            {
                createdReservation = await bookingService.CreateReservation(draftReservation);
                await fareRulesProcessor.DispatchFareRuleSectionsAsync(draftReservation.FareRules);
                
                // Note: (CT-2384) The booking service will infer whether this reservation can be cancelled
                var providerCommand = await bookingService.CreateReservationCommand(reservationParams);

                await dispatcher.DispatchAsync(providerCommand,
                    suffix: providerCommand.ProviderKey.GetProvider());
            }

            return createdReservation;
        }

        public async Task HandleAsync(CreateReservationRejected rejectedEvent)
        {
            logger.Information("Handling {@CreateReservationRejected}", rejectedEvent);

            await handlerFactory
                .Create(rejectedEvent)
                .Run(async () =>
                {
                    string tenantId;
                    string source;

                    try
                    {
                        var reservation = await bookingService.GetReservationAsync(rejectedEvent.ReservationId);

                        if (reservation is null)
                        {
                            logger.Information("Can't retrieve reservation {ReservationId}", rejectedEvent.ReservationId);
                        }
                        
                        tenantId = reservation?.TenantId;
                        source = reservation?.Source;
                    }
                    catch (Exception ex)
                    {
                        logger.Information(ex, $"Failed to retrieve reservation {{ReservationId}}, {nameof(CreateBookingRejected)} will be dispatched without {nameof(CreateBookingRejected.TenantId)} and {nameof(CreateBookingRejected.Source)}", rejectedEvent.ReservationId);

                        tenantId = default;
                        source = default;
                    }

                    await dispatcher.DispatchAsync(new CreateBookingRejected(rejectedEvent.RequestId,
                        rejectedEvent.BookingId, rejectedEvent.Code, rejectedEvent.Reason,
                        tenantId: tenantId,
                        sources: new[] { source }));
                })
                .ExecuteAsync();
        }

        public async Task HandleAsync(ReservationCreated @event)
        {
            Reservation reservation = null;
            BookingSaga saga = null;

            var logger1 = logger
                .ForContext(nameof(@event.BookingId), @event.BookingId)
                .ForContext(nameof(@event.ReservationId), @event.ReservationId)
                .ForContext(nameof(@event.Locator), @event.Locator);

            logger1.Information("Handling {@event}", @event);

            await handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservation = await bookingService.GetReservationAsync(@event.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Reservation was not found.");
                    }

                    saga = await bookingSagaService.GetAsync(reservation.BookingId);

                    if (saga == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Saga was not found.");
                    }
                })
                .Run(async () =>
                {
                    if (@event.OriginalReservationId != null)
                    {
                        await bookingService.ChangeReservationStateAsync(reservation.OriginalReservationId,
                             Enums.ReservationState.Cancelled);
                    }

                    /*
                     * TODO: note that reservation's identity is not based on provider key, it needs to be refactored
                     * We cannot use event.ReservationId here since the reservation may be virtual, which means having its own id in the db
                     */
                    var sagaReservation = saga.Reservations.Single(r => string.Equals(r.ProviderKey, @event.ProviderKey));

                    if (sagaReservation.State is ReservationState.New)
                    {
                        saga = await bookingSagaService.CreateReservationAsync(new CreateSagaReservationRequest
                        {
                            BookingSagaId = saga.Id,
                            ReservationId = reservation.Id,
                            Locator = $"C-{LocatorsHelper.GetProviderCode(reservation.Locators)}"
                        });
                    }
                })
                .OnSuccess(async () =>
                {
                    var reservationToCreate = saga.ReservationsToCreate.FirstOrDefault(BookingSaga.IsCreationAllowed);
                    if (reservationToCreate is not null)
                    {
                        // Create next reservation
                        await dispatcher.DispatchAsync(new CreateReservation
                        {
                            RequestId = @event.RequestId, BookingId = @event.BookingId,
                            ProviderKey = reservationToCreate.ProviderKey
                        });
                    }
                    else
                    {
                        // All reservations were created, send command to complete booking saga
                        await dispatcher.DispatchAsync(new CompleteBookingSaga { RequestId = @event.RequestId, BookingId = @event.BookingId });
                    }

                    await dispatcher.DispatchAsync(new AddRetentionLine { ReservationId = reservation.Id });

                    logger1.Information("ReservationCreated - Successfully processed");
                })
                .OnError(async (ex) =>
                {
                    logger1.Information("ReservationCreated - Critical - OnError");

                    // NOTE: only ValidationException and InvalidOperationException can interrupt the booking flow. 
                    if (ex is ValidationException || ex is InvalidOperationException)
                    {
                        logger1.Error(ex, "Error occured while processing created reservation.");
                        await dispatcher.DispatchAsync(new CreateBookingRejected(@event.RequestId, @event.BookingId, (ex as ValidationException)?.Code ?? OperationCodes.Error, ex.Message,
                            tenantId: saga.BookingEntity.TenantId,
                            sources: saga.ReservationsToCreate.Select(r => r.ProviderKey.GetSourceFromProviderKey()).ToArray()));
                    }
                    else
                    {
                        throw ex;
                    }
                })
                .ExecuteAsync();
        }
    }
}