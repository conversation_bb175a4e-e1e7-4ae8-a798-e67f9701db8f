using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class UpdateVesselHandler : ICommandHandler<UpdateVessel>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IAuthService _authService;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;

        public UpdateVesselHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            IAuthService authService,
            <PERSON><PERSON><PERSON><PERSON> mapper,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _authService = authService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task HandleAsync(UpdateVessel command)
        {
            Models.Booking booking = null;
            IEnumerable<Reservation> reservations = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    booking = await _bookingService.GetBookingAsync(command.BookingId);

                    if (booking == null)
                    {
                        throw new ValidationException("Booking not found.");
                    }

                    await _authService.ValidateIfBookingCanBeManagedOrFailAsync(booking);

                    if (booking.State == BookingState.Cancelled || booking.State == BookingState.Declined)
                    {
                        throw new ValidationException("Booking is already cancelled.");
                    }

                    if (booking.DepartureAt - DateTime.UtcNow < TimeSpan.FromHours(6))
                    {
                        throw new ValidationException(OperationCodes.DepartureTooCloseToAllowModifications, "Departure time is less than 6 hours away.");
                    }

                    reservations = await _bookingService.GetBookingReservationsAsync(command.BookingId);

                    if (reservations.Count(r => r.State == ReservationState.Active) == 0)
                    {
                        throw new ValidationException("Booking has no active reservations");
                    }
                })
                .Run(async () =>
                {
                    var newVessel = _mapper.Map<VesselDetails>(command);

                    // Update Booking and Reservations' Vessel
                    var tasks = reservations
                        .Select(r => _bookingService.UpdateReservationVesselAsync(r.Id, newVessel))
                        .Concat(new[] { _bookingService.UpdateBookingVesselAsync(command.BookingId, newVessel) });

                    await Task.WhenAll(tasks);
                })
                .OnSuccess(async () =>
                {
                    await _dispatcher.DispatchAsync(new VesselUpdated(
                        command.Request?.Id, 
                        command.BookingId, 
                        command.Name, 
                        command.Flag));
                })
                .OnCustomError(async (ex) =>
                {
                    await _dispatcher.DispatchAsync(new UpdateVesselRejected(
                        command.Request?.Id,
                        command.BookingId, 
                        ex.Code ?? OperationCodes.Error, 
                        ex.Message));
                })
                .OnError(async (ex) =>
                {
                    _logger.Error(ex, "Error occured while updating passenger for booking {Id}", command.BookingId);

                    await _dispatcher.DispatchAsync(new UpdateVesselRejected(
                        command.Request?.Id, 
                        command.BookingId, 
                        OperationCodes.Error, 
                        ex.Message));
                })
                .ExecuteAsync();
        }
    }
}