using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.ApprovalFlow;
using CTeleport.Messages.Models.ApprovalFlow.Enums;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Handlers.ApprovalFlow;

public sealed class ApprovalRejectedHandler : IEventHandler<ApprovalRejected>
{
    private readonly IBookingVariantService _bookingVariantService;
    private readonly IBookingSagaService _bookingSagaService;
    private readonly IBookingMetricsService _metricsService;
    private readonly IHandlerFactory _handlerFactory;
    private readonly IBookingService _bookingService;
    private readonly IMessageDispatcher _dispatcher;
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    

    public ApprovalRejectedHandler(
        IBookingVariantService bookingVariantService,
        IBookingSagaService bookingSagaService,
        IBookingMetricsService metricsService,
        IHandlerFactory handlerFactory,
        IBookingService bookingService,
        IMessageDispatcher dispatcher,
        ILogger logger,
        IMapper mapper)
    {
        _bookingVariantService = bookingVariantService;
        _bookingSagaService = bookingSagaService;
        _metricsService = metricsService;
        _handlerFactory = handlerFactory;
        _bookingService = bookingService;
        _dispatcher = dispatcher;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task HandleAsync(ApprovalRejected @event)
    {
        var logger = _logger
            .ForContext("RequestId", @event.RequestId)
            .ForContext("ReferenceId", @event.ReferenceId)
            .ForContext("ApplicableFor", @event.ApplicableFor.ToString());
        
        logger.Information("{EventName} Start processing", nameof(ApprovalRejected));
        
        if (@event.ApplicableFor != ApprovalApplicableFor.Flight)
        {
            logger.Information("{EventName} Skipping because approval is not applicable for Flight", 
                nameof(ApprovalRejected));
                    
            // Skip because booking service will only handle flight booking approvals
            return;
        }
        
        Models.Booking booking = null;
        BookingSaga saga = null;

        await _handlerFactory
            .Create(@event)
            .Validate(async () =>
            {
                booking = await _bookingService.GetBookingAsync(@event.ReferenceId);
                if (booking == null)
                {
                    throw new NotFoundException("Booking not found");
                }

                if (booking.State != BookingState.ApprovalRequired)
                {
                    throw new ValidationException($"Invalid booking state {booking.State}. Must be {BookingState.ApprovalRequired}");
                }

                saga = await _bookingSagaService.GetAsync(@event.ReferenceId);

                if (saga == null)
                {
                    throw new Exception("Saga was not found.");
                }

                if (saga.SagaState is not Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.InApproval)
                {
                    throw new ValidationException($"Invalid booking saga state {saga.SagaState}. Must be {CreateBookingSagaState.InApproval}");
                }
            })
            .Run(async () =>
            {
                if (@event.ApplicableFor != ApprovalApplicableFor.Flight)
                {
                    // Skip because booking service will only handle flight booking approvals
                    return;
                }
                
                var reservations = await _bookingService.GetBookingReservationsAsync(@event.ReferenceId);

                await Task.WhenAll(reservations
                    .Where(r => r.State == ReservationState.Active)
                    .Select(reservation =>
                        _dispatcher.DispatchAsync(new CancelReservation
                        {
                            Request = Request.New<CancelReservation>(@event.RequestId),
                            ReservationId = reservation.Id,
                            User = @event.RejectedBy
                        })
                    ));

                logger.Information("{EventName} CancelReservation sent", nameof(ApprovalRejected));

                saga = await _bookingSagaService.ChangeStateAsync(saga.Id, Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.Rejected);

                logger.Information("{EventName} Saga updated", nameof(ApprovalRejected));

                await _bookingService.SetAsDeclinedByAsync(@event.ReferenceId,
                        _mapper.Map<Search.Shared.Models.User>(@event.RejectedBy), @event.ReasonText);
                    
                logger.Information("{EventName} Finish with Declined", nameof(ApprovalRejected));

                _metricsService.IncrementBookingSagaRejectedCounter(@event.TenantId, @event.Reason.ToString());
                _metricsService.DecrementActiveBookingSagaCounter(@event.TenantId);
            })
            .OnSuccess(async () =>
            {
                if (Environment.GetEnvironmentVariable("BOOKING_VARIANT_DISABLED") is null)
                    await _bookingVariantService.RejectBooking(@event.ReferenceId, @event.ReasonText);
            })
            .OnError(e =>
            {
                logger.Error(e, "Could not handle ApprovalRejected event {@Event}", @event);
                return Task.CompletedTask;
            })
            .Lock("Bookings/" + @event.ReferenceId)
            .ExecuteAsync();
    }
}