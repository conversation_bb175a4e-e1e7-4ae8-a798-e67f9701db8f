using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages;
using CTeleport.Messages.Events.ApprovalFlow;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Models.ApprovalFlow.Enums;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.SearchProxy.Services;
using Serilog;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Handlers.ApprovalFlow;

public class BookingApprovalGrantedHandler : IEventHandler<BookingApprovalGranted>
{
    private readonly IFlightSolutionService _flightSolutionReadService;
    private readonly IApprovalFlowService _approvalFlowService;
    private readonly IBookingSagaService _bookingSagaService;
    private readonly IHandlerFactory _handlerFactory;
    private readonly IBookingService _bookingService;
    private readonly IMessageDispatcher _dispatcher;
    private readonly ILogger _logger;

    public BookingApprovalGrantedHandler(IFlightSolutionService flightSolutionReadService, IApprovalFlowService approvalFlowService, 
        IBookingSagaService bookingSagaService, IHandlerFactory handlerFactory, IBookingService bookingService, 
        IMessageDispatcher dispatcher, ILogger logger)
    {
        _flightSolutionReadService = flightSolutionReadService;
        _approvalFlowService = approvalFlowService;
        _bookingSagaService = bookingSagaService;
        _bookingService = bookingService;
        _dispatcher = dispatcher;
        _handlerFactory = handlerFactory;
        _logger = logger;
    }
    
    public async Task HandleAsync(BookingApprovalGranted @event)
    {
        var logger = _logger
            .ForContext("RequestId", @event.RequestId)
            .ForContext("ReferenceId", @event.ReferenceId)
            .ForContext("ApplicableFor", @event.ApplicableFor.ToString());
        
        logger.Information("{EventName} Start processing", nameof(BookingApprovalGranted));
        
        if (@event.ApplicableFor != ApprovalApplicableFor.Flight)
        {
            logger.Information("{EventName} Skipping because approval is not applicable for Flight", 
                nameof(BookingApprovalGranted));
                    
            // Skip because booking service will only handle flight booking approvals
            return;
        }
        
        var messagesToDispatch = new List<IMessage>();
        Reservation[] reservations = null;
        Models.Booking booking = null;
        BookingSaga saga = null;
        FlightSolution approvedSolution = null;
        FlightSolution originalSolution = null;
        
        await _handlerFactory.Create(@event)
            .Validate(async () =>
            {
                booking = await _bookingService.GetBookingAsync(@event.ReferenceId);

                if (booking == null)
                {
                    throw new NotFoundException("Booking not found");
                }

                if (booking.State != BookingState.ApprovalRequired)
                {
                    throw new ValidationException($"Invalid booking state {booking.State}. Must be {BookingState.ApprovalRequired}");
                }

                saga = await _bookingSagaService.GetAsync(@event.ReferenceId);

                if (saga == null)
                {
                    throw new NotFoundException("Booking saga not found");
                }

                reservations = (await _bookingService.GetBookingReservationsAsync(@event.ReferenceId)).ToArray();

                var hasNonVirtualReservationsToApprove = _approvalFlowService.HasNonVirtualReservationsToApprove(reservations);

                originalSolution = await _flightSolutionReadService.GetFlightSolutionAsync(booking.FlightSolutionId);
                
                if (originalSolution == null)
                {
                    throw new NotFoundException("Original flight solution was not found.");
                }

                // Front End sends FlightSolutionId only in cases where we have an alternative flight
                var approvedFlightSolutionId = hasNonVirtualReservationsToApprove
                    ? @event.AlternativeSolutionId
                    : @event.AlternativeSolutionId ?? booking.FlightSolutionId; 

                // and try to get the flightSolution (alternative if front end sent the Id)
                approvedSolution = string.IsNullOrEmpty(approvedFlightSolutionId)
                    ? null
                    : await _flightSolutionReadService.GetFlightSolutionAsync(approvedFlightSolutionId); 

                // if @event.FlightSolutionId (repriced) is set, then flightSolution can be null
                // if this is booking flightSolutionId and has reservations to approve, then fail
                if (approvedSolution == null && !hasNonVirtualReservationsToApprove)
                {
                    throw new NotFoundException("Flight solution was not found.");
                }
            })
            .Run(async () =>
            {
                if (@event.ApplicableFor != ApprovalApplicableFor.Flight)
                {
                    // Skip because booking service will only handle flight booking approvals
                    return;
                }
                
                messagesToDispatch = await _approvalFlowService.ApproveBookingAsync(new BookingApprovedRequest
                {
                    RequestId = @event.RequestId,
                    TenantId = @event.TenantId,
                    ApprovedBy = @event.ApprovedBy,
                    Booking = booking,
                    BookingSaga = saga,
                    ApprovedSolution = approvedSolution,
                    OriginalSolution = originalSolution,
                    Reservations = reservations
                });
                
            })
            .OnCustomError(async ex =>
            {
                logger.Error(ex, "Error occured while handling BookingApprovalGranted event");
                await _dispatcher.DispatchAsync(ApproveBookingFailed.InBookingService(@event.ReferenceId));
            })
            .OnError(async ex =>
            {
                logger.Error(ex, "Error occured while handling BookingApprovalGranted event");
                await _dispatcher.DispatchAsync(ApproveBookingFailed.InBookingService(@event.ReferenceId));
            })
            .OnSuccess(async () =>
            {
                foreach (var message in messagesToDispatch)
                    await _dispatcher.DispatchAsync(message);

                logger.Information("{EventName} Finish processing", nameof(BookingApprovalGranted));
            })
            .Lock("Bookings/" + @event.ReferenceId)
            .ExecuteAsync();
    }
}