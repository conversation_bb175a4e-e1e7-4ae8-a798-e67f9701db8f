using System;
using System.Collections.Frozen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Authorization.Services;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Messages.Events.Tickets;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Helpers;
using Serilog;
using Serilog.Context;
using IAuthService = CTeleport.Services.Booking.Services.IAuthService;

namespace CTeleport.Services.Booking.Handlers
{
    /// <summary>
    /// Checks authorization and validates request to set ticket as refunded.
    /// If authorized and validation rules met, marks ticket as refunded and Dispatches TicketRefunded event
    /// </summary>
    public class SetTicketAsRefundedHandler : ICommandHandler<SetTicketAsRefunded>
    {
        private static readonly TicketState[] ExpectedTicketStatesForRefund =
        {
            TicketState.Open, TicketState.Used, TicketState.Refunded
        };

        private readonly IHandlerFactory _handlerFactory;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IBookingService _bookingService;
        private readonly IAuthService _authService;
        private readonly IMapper _mapper;
        private readonly IServiceContext _context;
        private readonly ILogger _logger;
        private readonly IReservationRefundService _reservationRefundService;

        public SetTicketAsRefundedHandler(IHandlerFactory handlerFactory, IMessageDispatcher dispatcher,
            IBookingService bookingService, IAuthService authService, IMapper mapper, IServiceContext context,
            ILogger logger, IReservationRefundService reservationRefundService)
        {
            _handlerFactory = handlerFactory;
            _dispatcher = dispatcher;
            _bookingService = bookingService;
            _authService = authService;
            _mapper = mapper;
            _context = context;
            _logger = logger;
            _reservationRefundService = reservationRefundService;
        }

        public async Task HandleAsync(SetTicketAsRefunded command)
        {
            using (LogContext.PushProperty("ReservationId", command.ReservationId))
            using (LogContext.PushProperty("TicketNumber", command.Number))
            {
                await Process(command);
            }
        }

        // Here is about some money
        // TODO: Refactor; unit-tests coverage
        private async Task Process(SetTicketAsRefunded command)
        {
            Reservation reservation = null;
            var refundedAt = DateTime.UtcNow;
            string ticketNumber = null;
            ReservationPrice userPaidTicketPrice = null;
            decimal? airlinePaidNetPrice = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    _logger.Information("Validating SetTicketAsRefunded command, requested {RefundFee} as refund fee, {RefundAmount} as refund amount, used amount is {UsedAmount}", 
                        command.RefundFee,
                        command.RefundAmount, 
                        command.UsedAmount);
                    
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);

                    if (reservation == null)
                    {
                        throw new ServiceException("Reservation is not found");
                    }

                    var booking = await _bookingService.GetBookingAsync(reservation.BookingId);
                    await _authService.ValidateIfBookingCanBeManagedOrFailAsync(booking);
                    
                    if (command.InvoiceRefund is not null)
                    {
                        _logger
                            .ForContext("InvoiceRefundAmount", command.InvoiceRefund.Amount)
                            .ForContext("InvoiceRefundUsedAmount", command.InvoiceRefund.UsedAmount)
                            .ForContext("InvoiceRefundFee", command.InvoiceRefund.Fee)
                            .Information("InvoiceRefund data exists");
                    }

                    var ticketTaxes = new List<KeyValuePair<string, decimal>>(0);
                    userPaidTicketPrice = reservation.GetFullTicketsPrice();

                    _logger.Information("Got full ticket price paid by user (net): {FullTicketPriceNet} {FullTicketPriceCurrency}; Markup = {FullTicketPriceMarkup}; Total = {FullTicketPriceTotal}",
                        userPaidTicketPrice.Net,
                        userPaidTicketPrice.Currency,
                        userPaidTicketPrice.Markup,
                        userPaidTicketPrice.Total);

                    if (!reservation.Ticketless)
                    {
                        _logger.Information("Reservation has ticket (not ticketless)");

                        var ticket =
                            reservation.Tickets.FirstOrDefault(t => TicketExtensions.NumbersAreEqual(t.Number, command.Number));
                        var tickets = reservation.GetReissuedTicketsChain(command.Number).ToList();

                        if (ticket == null)
                        {
                            throw new ServiceException("Ticket is not found");
                        }

                        if (!ExpectedTicketStatesForRefund.Contains(ticket.State))
                        {
                            throw new ValidationException($"Ticket in state {ticket.State} cannot be refunded");
                        }

                        ticketNumber = ticket.Number.Trim();
                        ticketTaxes = tickets.SelectMany(t => t.Price.Taxes ?? FrozenDictionary<string, decimal>.Empty)
                            .WhereNotNull()
                            .ToList();

                        // ticket can be issued with different price because of class drops and fare updates
                        airlinePaidNetPrice = tickets.Sum(x => x.Price.Net);
                        
                        _logger.Information("Using '{SanitizedTicketNumber}'", ticketNumber);
                    }
                    else
                    {
                        ticketNumber = reservation.GetProviderCode();
                        _logger.Information("Reservation is ticketless, using {ProviderCode} as ticket number", ticketNumber);

                        // for ticketless reservations using user paid price as airline paid price
                        airlinePaidNetPrice = userPaidTicketPrice.Net;
                    }

                    _logger.Information("Using {NetTicketPrice} as net ticket price paid to the airline", airlinePaidNetPrice);

                    // amounts from command are for money flow between us and the airline
                    //  AND ONLY IF these amounts are equal then for money flow between us and the user
                    //  otherwise we need to use "different amount in the invoice" to deal with the user
                    if (command.RefundAmount > airlinePaidNetPrice)
                    {
                        throw new ValidationException("Refund amount cannot exceed net ticket price paid to the airline");
                    }

                    if (command.UsedAmount > airlinePaidNetPrice)
                    {
                        throw new ValidationException("Used amount cannot exceed net ticket price paid to the airline");
                    }

                    if (command.RefundFee > airlinePaidNetPrice)
                    {
                        throw new ValidationException("Refund fee cannot exceed net ticket price paid to the airline");
                    }

                    if (command.RefundAmount + command.RefundFee + command.UsedAmount > airlinePaidNetPrice)
                    {
                        throw new ValidationException("The sum of refund amount, fee and used amount cannot exceed net ticket price paid to the airline");
                    }

                    // IF refund by airline != refund to the user
                    //  then invoice refund is used to deal with the user
                    // otherwise we can refund to the user higher amounts in case of fare updates that made ticket more expensive
                    //  or we can refund to the user lower amounts in case of class drops that made ticket cheaper
                    if (command.InvoiceRefund != null && command.InvoiceRefund.Amount > userPaidTicketPrice.Net)
                    {
                        throw new ValidationException("Refund amount in the invoice cannot exceed net ticket price paid by the user");
                    }

                    if (command.DeductedTaxes != null && command.DeductedTaxes.Count > 0)
                    {
                        _logger.Information("Contains DeductedTaxes in request");

                        if (!ticketTaxes.Any())
                        {
                            throw new ValidationException("Original ticket price have no taxes specified");
                        }

                        var invalidTaxes = command.DeductedTaxes.Where(tax => !ticketTaxes.Any(tt => tax.Equals(tt.Key))).ToList();

                        if (invalidTaxes.Count > 0)
                        {
                            throw new ValidationException($"The following taxes were not in ticket price: {String.Join(", ", invalidTaxes)}");
                        }
                    }

                    if (command.NotRefundedTaxes != null && command.NotRefundedTaxes.Keys.Count > 0)
                    {
                        _logger.Information("Contains NotRefundedTaxes in request");
                        
                        if (!ticketTaxes.Any())
                        {
                            throw new ValidationException("Original ticket price have no taxes specified");
                        }

                        var invalidTaxes = command.NotRefundedTaxes.Keys.Where(tax => !ticketTaxes.Any(tt => tax.Equals(tt.Key))).ToList();

                        if (invalidTaxes.Count > 0)
                        {
                            throw new ValidationException($"The following taxes were not in ticket price: {String.Join(", ", invalidTaxes)}");
                        }
                    }
                })
                .Run(async () =>
                {
                    var refund = new TicketRefundDto
                    {
                        Amount = command.RefundAmount,
                        UsedAmount = command.UsedAmount,
                        Fee = command.RefundFee,
                        DeductedTaxes = command.DeductedTaxes,
                        NotRefundedTaxes = command.NotRefundedTaxes,
                        InvoiceRefund = command.InvoiceRefund != null ? _mapper.Map<InvoiceRefund>(command.InvoiceRefund) : null,
                        Remarks = command.Remarks,
                        BspRefundDate = command.BspRefundDate
                    };

                    await _reservationRefundService.SetTicketAsRefundedAsync(reservation, ticketNumber, refund, refundedAt);
                })
                // TODO: handle custom error
                .OnSuccess(() =>
                {
                    var refundNotificationContext = new RefundNotificationContext
                    {
                        Command = command,
                        Reservation = reservation,
                        TicketNumber = ticketNumber,
                        AirlinePaidNetPrice = airlinePaidNetPrice,
                        RefundedAt = refundedAt,
                        RefundedBy = _context?.User?.Name ?? command.User.Name
                    };
                    
                    return command.NoShowRefund
                        ? ProcessNoShowRefund(refundNotificationContext) 
                        : ProcessTicketRefunded(refundNotificationContext);
                })
                .OnError(e =>
                {
                    _logger
                        .ForContext("RequestId", command.Request?.Id)
                        .ForContext("UserName", _context?.User?.Name)
                        .ForContext("BookingId", reservation.BookingId)
                        .Error(e, "Error occured while SetTicketAsRefunded");
                })
                .Lock("Reservation/" + command.ReservationId)
                .ExecuteAsync();
        }

        private async Task ProcessNoShowRefund(RefundNotificationContext refundNotificationContext)
        {
            var command = refundNotificationContext.Command;
            var reservation = refundNotificationContext.Reservation;
            var ticketNumber = refundNotificationContext.TicketNumber;
            var refundedAt = refundNotificationContext.RefundedAt;

            const NoShowRefundType refundType = NoShowRefundType.Ticket;
            
            var noShowTicketRefunded = new NoShowTicketRefunded
            {
                RequestId = command.Request?.Id,
                FundingSource = reservation.GetFundingSource(ticketNumber),
                Source = reservation.Source,
                ReferenceNumber = ticketNumber,
                RefundAmount = command.RefundAmount,
                RefundedAt = refundedAt,
                Type = refundType,
                ReservationId = reservation.Id,
                BookingId = reservation.BookingId,
                TenantId = reservation.TenantId
            };

            await _dispatcher.DispatchAsync(noShowTicketRefunded);

            var reservationCancelled = new ReservationCancelled
            {
                BookingId = reservation.BookingId,
                ReservationId = reservation.Id,
                Source = reservation.Source,
                TenantId = reservation.TenantId,
                CancelledAt = refundedAt,
                CancelledBy = refundNotificationContext.RefundedBy,
                IsVirtual = reservation.IsVirtual,
                Locator = reservation.GetProviderCode()
            };
            
            await _dispatcher.DispatchAsync(reservationCancelled);
        }

        private async Task ProcessTicketRefunded(RefundNotificationContext refundNotificationContext)
        {
            var command = refundNotificationContext.Command;
            var reservation = refundNotificationContext.Reservation;
            var ticketNumber = refundNotificationContext.TicketNumber;
            var airlinePaidNetPrice = refundNotificationContext.AirlinePaidNetPrice;
            var refundedAt = refundNotificationContext.RefundedAt;
            var refundedBy = refundNotificationContext.RefundedBy;
            
            _logger.Information("Success. Dispatching TicketRefunded");
            
            await _dispatcher.DispatchAsync(new TicketRefunded(requestId: command.Request.Id,
                bookingId: reservation.BookingId,
                reservationId: reservation.Id,
                locator: reservation.GetProviderCode(),
                number: reservation.Ticketless ? reservation.GetProviderCode() : ticketNumber,
                refundAmount: command.RefundAmount,
                totalFee: airlinePaidNetPrice!.Value - command.RefundAmount,
                autoRefund: false,
                refundedBy: refundedBy,
                source: reservation.Source,
                tenantId: reservation.TenantId,
                refundedAt: refundedAt,
                isPredicted: false,
                platingCarrier: reservation.Fare?.PlatingCarrier,
                fundingSource: reservation.GetFundingSource(ticketNumber)
            )
            {
                AcmNumber = command.AcmNumber
            });
            
        }
    }
}