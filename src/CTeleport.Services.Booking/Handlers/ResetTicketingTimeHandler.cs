using System;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Helpers;
using Serilog;
using System.Threading.Tasks;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Helpers.Extensions;

namespace CTeleport.Services.Booking.Handlers
{
    public class ResetTicketingTimeHandler : ICommandHandler<ResetTicketingTime>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ITicketService _ticketService;
        private readonly ILogger _logger;

        public ResetTicketingTimeHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            ITicketService ticketService,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _ticketService = ticketService;
            _logger = logger;
        }

        public async Task HandleAsync(ResetTicketingTime @event)
        {
            Models.Reservation reservation = null;
            Models.Booking booking = null;

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(@event.ReservationId);

                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation is not found");
                    }

                    booking = await _bookingService.GetBookingAsync(reservation.BookingId);

                    if (booking == null)
                    {
                        throw new ValidationException("Booking is not found");
                    }
                })
                .Run(async () =>
                {
                    // TODO: This is a hack for Airgateway. We should remove this when Airgateway is fixed
                    var isVirtualAirgateway = reservation.IsVirtual && reservation.Source.IsAirgateway();
                    
                    var ticketingTime = _ticketService.CalculateTicketingTime(reservation, booking.PaymentExpiresAt);
                    var locator = LocatorsHelper.GetProviderCode(reservation.Locators);

                    if (!isVirtualAirgateway && reservation.TicketingAt.HasValue && reservation.TicketingAt.Value > ticketingTime)
                    {
                        _logger.Information("Updating ticketing time for {Locator} from {OriginalTicketingTime} to {NewTicketingTime}",
                            locator, reservation.TicketingAt.Value, ticketingTime);

                        await _bookingService.SetTicketingTimeAsync(reservation.Id, ticketingTime);
                        await _dispatcher.DispatchAsync(new TicketingAtUpdated
                        {
                            BookingId = reservation.BookingId,
                            ReservationId = reservation.Id,
                            TenantId = reservation.TenantId,
                            TicketingAt = ticketingTime
                        });
                    }
                    else
                    {
                        _logger.Information("Skipping ticketing time update for {Locator}. Ticketing scheduled at {CurrentTicketingTime}",
                            locator, reservation.TicketingAt);
                    }
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "Could not reset ticketing time for reservation");
                })
                .ExecuteAsync();
        }
    }
}