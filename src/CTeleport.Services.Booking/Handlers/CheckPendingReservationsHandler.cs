using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Shared.Enums;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class CheckPendingReservationsHandler : ICommandHandler<CheckPendingReservations>
    {
        private readonly IProviderReservationInfoService _providerReservationService;
        private readonly IPendingReservationService _pendingReservationService;
        private readonly IBookingMetricsService _metricsService;
        private readonly IHandlerFactory _handlerFactory;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;

        public CheckPendingReservationsHandler(
            IProviderReservationInfoService providerReservationService,
            IPendingReservationService pendingReservationService,
            IBookingMetricsService metricsService,
            IHandlerFactory handlerFactory,
            IMessageDispatcher dispatcher,
            ILogger logger)
        {
            _providerReservationService = providerReservationService;
            _pendingReservationService = pendingReservationService;
            _metricsService = metricsService;
            _handlerFactory = handlerFactory;
            _dispatcher = dispatcher;
            _logger = logger;
        }

        public async Task HandleAsync(CheckPendingReservations command)
        {
            await _handlerFactory
                .Create(command)
                .Run(async () =>
                {
                    var pending = await _pendingReservationService.GetActivePendingReservationsAsync();

                    foreach (var pendReservation in pending)
                    {
                        var providerReservation = await _providerReservationService.GetReservationAsync(new ProviderRetrieveReservationRequest
                        {
                            Source = pendReservation.Source,
                            Locators = pendReservation.Locators
                        });

                        _metricsService.IncrementProviderValidationRequestCounter(
                            nameof(CheckPendingReservations), pendReservation?.Source ?? string.Empty, providerReservation?.HasError ?? false);

                        await HandleProviderReservationState(pendReservation, providerReservation);
                    }
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "Error on checking pending reservations' state.");
                })
                .Lock("CheckPendingReservations")
                .ExecuteAsync();
        }

        private async Task HandleProviderReservationState(PendingReservation pendingReservationRecord, ProviderRetrieveReservationResponse providerReservation)
        {
            if (providerReservation.State == ProviderReservationState.Failed)
            {
                pendingReservationRecord.State = ProviderReservationState.Failed;
                await _pendingReservationService.UpdatePendingReservationAsync(pendingReservationRecord);

                await DispatchRejectedNotification(
                    pendingReservationRecord.ReservationId,
                    pendingReservationRecord.Locators,
                    providerReservation.Error?.ErrorMessage ?? "Pending reservation was not confirmed."
                );
            }
            else if (providerReservation.State == ProviderReservationState.Confirmed)
            {
                pendingReservationRecord.State = ProviderReservationState.Confirmed;
                await _pendingReservationService.UpdatePendingReservationAsync(pendingReservationRecord);

                await _dispatcher.DispatchAsync(new ReservationPendingCompleted
                {
                    ReservationId = pendingReservationRecord.ReservationId,
                    Locators = pendingReservationRecord.Locators
                });
            }
            else if (pendingReservationRecord.IsExpired)
            {
                pendingReservationRecord.State = ProviderReservationState.Failed;
                await _pendingReservationService.UpdatePendingReservationAsync(pendingReservationRecord);

                await DispatchRejectedNotification(
                    pendingReservationRecord.ReservationId,
                    pendingReservationRecord.Locators,
                    "Pending Reservation has expired (Seat was not confirmed by airline). Booking will be cancelled.");
            }
            else
            {
                // Still pending -> Just update CheckedAt value
                await _pendingReservationService.UpdatePendingReservationAsync(pendingReservationRecord);
            }
        }

        private Task DispatchRejectedNotification(string reservationId, Dictionary<string, string> locators, string reason) =>
             _dispatcher.DispatchAsync(new ReservationPendingRejected
             {
                 ReservationId = reservationId,
                 Locators = locators,
                 Reason = reason
             });
    }
}
