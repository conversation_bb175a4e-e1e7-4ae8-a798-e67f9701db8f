using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Services;
using Serilog;
using System.Linq;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class ResetFareRefreshQueueHandler : ICommandHandler<ResetFareRefreshQueue>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;

        public ResetFareRefreshQueueHandler(
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IMessageDispatcher dispatcher,
            ILogger logger)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _dispatcher = dispatcher;
            _logger = logger;
        }

        public async Task HandleAsync(ResetFareRefreshQueue command)
        {
            await _handlerFactory
                .Create(command)
                .Run(async () =>
                {
                    var reservations = await _bookingService.GetReservationsToRefreshFareAsync();

                    var tasks = reservations
                        .Select(reservation =>
                            _dispatcher.DispatchAsync(new RefreshReservationFare
                            {
                                Request = Request.New<RefreshReservationFare>(),
                                ReservationId = reservation.Id
                            }));

                    await Task.WhenAll(tasks);
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An error occur when processing {Command} command", nameof(ResetFareRefreshQueue));
                })
                .Lock("Semaphore/ResetFareRefreshQueue")
                .ExecuteAsync();
        }
    }
}