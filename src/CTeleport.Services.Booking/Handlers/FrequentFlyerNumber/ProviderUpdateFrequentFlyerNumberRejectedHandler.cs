using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Helpers;
using Serilog;

namespace CTeleport.Services.Booking.Handlers
{
    public class ProviderUpdateFrequentFlyerNumberRejectedHandler : IEventHandler<ProviderUpdateFrequentFlyerNumberRejected>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly ILogger _logger;
        private readonly IMessageDispatcher _dispatcher;

        public ProviderUpdateFrequentFlyerNumberRejectedHandler(IHandlerFactory handlerFactory,
            IBookingService bookingService, ILogger logger, IMessageDispatcher dispatcher)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _logger = logger;
            _dispatcher = dispatcher;
        }

        public Task HandleAsync(ProviderUpdateFrequentFlyerNumberRejected @event)
        {
            Reservation reservation = null;
            return _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(@event.ReservationId);
                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation not found");
                    }
                })
                .Run(() =>
                {
                    var providerLocator = LocatorsHelper.GetProviderCode(reservation.Locators);
                    _logger.ForContext("ReservationId", reservation.Id)
                        .ForContext("BookingId", reservation.BookingId)
                        .ForContext("Reason", @event.Reason)
                        .ForContext("ProviderLocator", providerLocator)
                        .Information("Update frequent flyer number rejected");

                    return _dispatcher.DispatchAsync(new UpdateFrequentFlyerNumberRejected(
                        @event.RequestId,
                        reservation.BookingId,
                        reservation.Id,
                        providerLocator,
                        @event.Code,
                        @event.Reason));
                })
                .ExecuteAsync();
        }
    }
}