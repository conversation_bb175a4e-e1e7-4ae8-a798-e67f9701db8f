using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.Helpers;
using CTeleport.Services.Price.Services;
using Serilog;

namespace CTeleport.Services.Booking.Handlers
{
    public class UpdateFrequentFlyerNumberHandler : ICommandHandler<UpdateFrequentFlyerNumber>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IPriceService _priceService;
        private readonly ILogger _logger;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ICustomFieldsService _customFieldsService;

        public UpdateFrequentFlyerNumberHandler(IHandlerFactory handlerFactory, IBookingService bookingService, ILogger logger, IMessageDispatcher dispatcher)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _logger = logger;
            _dispatcher = dispatcher;
        }

        public async Task HandleAsync(UpdateFrequentFlyerNumber command)
        {
            Reservation reservation = null;
            Models.Booking booking = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);
                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation not found");
                    }

                    booking = await _bookingService.GetBookingAsync(reservation.BookingId);
                    if (booking == null)
                    {
                        throw new ValidationException("Booking not found");
                    }
                })
                .Run(async () =>
                {
                    var frequentFlyerNumbers = reservation.FrequentFlyerNumbers
                        ?.Select(x => new Messages.Commands.Models.FrequentFlyerNumber
                        {
                            Carrier = x.Carrier,
                            Number = x.Number
                        })
                        .ToList() ?? new List<Messages.Commands.Models.FrequentFlyerNumber>();

                    if (frequentFlyerNumbers.Count > 0)
                    {
                        await _dispatcher.DispatchAsync(new ProviderUpdateFrequentFlyerNumber
                        {
                            Request = Request.New<ProviderUpdateFrequentFlyerNumber>(command.ReservationId),
                            Locators = reservation.Locators,
                            ReservationId = command.ReservationId,
                            Source = reservation.Source,
                            FrequentFlyerNumbers = frequentFlyerNumbers
                        }, reservation.Source.GetProvider());
                    }
                })
                .OnError(ex =>
                {
                    _logger.ForContext(nameof(command.ReservationId), command.ReservationId).Error(ex, "Error on updating rebooked reservation ffns");
                })
                .Lock("Reservation/" + command.ReservationId)
                .ExecuteAsync(); ;
        }
    }
}
