using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Services.Booking.Services;
using Serilog;
using System.Threading.Tasks;

namespace CTeleport.Services.Booking.Handlers
{
    public class RetrieveSupplierLocatorsHandler : ICommandHandler<RetrieveSupplierLocators>
    {
        private readonly IRetrieveSupplierLocatorsHandlerService _handlerService;
        private readonly IHandlerFactory _handlerFactory;
        private readonly ILogger _logger;

        public RetrieveSupplierLocatorsHandler(
            IRetrieveSupplierLocatorsHandlerService handlerService,
            IHandlerFactory handlerFactory,
            ILogger logger)
        {
            _handlerService = handlerService;
            _handlerFactory = handlerFactory;
            _logger = logger;
        }

        public async Task HandleAsync(RetrieveSupplierLocators command)
        {
            await _handlerFactory
                .Create(command)
                .Run(async () =>
                {
                    await _handlerService.SetSupplierLocatorsForAllReservationsWithoutItAsync();
                })
                .OnSuccess(() =>
                {
                    _logger.Information("The {Command} command finished processing", nameof(RetrieveSupplierLocators));
                    return Task.CompletedTask;
                })
                .OnError((ex) =>
                {
                    _logger.Error(ex, "An Error occured when running {Command} command", nameof(RetrieveSupplierLocators));
                })
                .Lock("Semaphore/RetrieveSupplierLocators")
                .ExecuteAsync();
        }
    }
}