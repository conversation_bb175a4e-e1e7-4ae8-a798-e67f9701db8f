using AutoMapper;
using CTeleport.Common.Authorization.Services;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Changes;
using CTeleport.Messages.Events.FlightSolutions;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Messages.Events.Tickets;
using CTeleport.Services.Booking.Commands;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Shared.Constants;
using CTeleport.Services.Booking.Validation;
using CTeleport.Services.ExtraServiceManagement.Models;
using CTeleport.Services.ExtraServiceManagement.Services;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.SearchProxy.Services;
using CTeleport.Services.Settings.Services;
using CTeleport.Services.ApprovalQueueClient;
using CTeleport.Services.ApprovalQueueClient.Extensions;
using Serilog;
using Serilog.Context;
using Serilog.Core;
using Serilog.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Common.Helpers;
using CTeleport.Messages.Commands.ApprovalFlow;
using CTeleport.Messages.Commands.Vessels;
using CTeleport.Messages.Models.ApprovalFlow.Enums;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Helpers.Extensions;
using CTeleport.Services.Providers.Client;
using CTeleport.Services.Travelers.Contracts.Events;
using CTeleport.Services.Travelers.Contracts.Models;
using CTeleport.Services.Travelers.Contracts.Models.Enums;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;
using BookingState = CTeleport.Services.Booking.Enums.BookingState;
using IAuthService = CTeleport.Services.Booking.Services.IAuthService;
using ISecretsManagerService = CTeleport.Services.Booking.Services.ISecretsManagerService;
using PaymentMethod = CTeleport.Services.Booking.Enums.PaymentMethod;
using ReservationState = CTeleport.Services.Booking.Enums.ReservationState;

namespace CTeleport.Services.Booking.Handlers
{
    public class CreateBookingHandler :
        ICommandHandler<CreateBooking>,
        ICommandHandler<RejectBookingSaga>,
        ICommandHandler<CompleteBookingSaga>,
        IEventHandler<BookingCreated>,
        IEventHandler<BookingConfirmed>,
        IEventHandler<CreateBookingRejected>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly ILogger _logger;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IAuthService _authService;
        private readonly IBookingService _bookingService;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IFlightSolutionService _searchService;
        private readonly ITicketService _ticketService;
        private readonly IMapper _mapper;
        private readonly ISingleNameService _singleNameService;
        private readonly ISettingsService _settingsService;
        private readonly IFlightSearchReadService _flightSearchReadService;
        private readonly ITravelPoliciesService _travelPoliciesService;
        private readonly IBookingPaymentService _paymentsService;
        private readonly IBillingDetailsValidator _billingDetailsValidator;
        private readonly IAirProvidersService _airProvidersService;
        private readonly ISecretsManagerService _secretsManager;
        private readonly IServiceContext _context;
        private readonly IAncillaryService _ancillaryService;
        private readonly IBookingMetricsService _metricsService;
        private readonly IBookingVariantService _bookingVariantService;
        private readonly ISagaMapper _sagaMapper;
        private readonly IProvidersClient _providersClient;

        public CreateBookingHandler(IHandlerFactory handlerFactory,
            ILogger logger,
            IMessageDispatcher dispatcher,
            IAuthService authService,
            IBookingService bookingService,
            IBookingSagaService bookingSagaService,
            IFlightSolutionService searchService,
            ITicketService ticketService,
            IMapper mapper,
            ISingleNameService singleNameService,
            ISettingsService settingsService,
            IFlightSearchReadService flightSearchReadService,
            ITravelPoliciesService travelPoliciesService,
            IBookingPaymentService paymentsService,
            IBillingDetailsValidator billingDetailsValidator,
            IAirProvidersService airProvidersService,
            ISecretsManagerService secretsManager,
            IServiceContext context,
            IAncillaryService ancillaryService,
            IBookingMetricsService metricsService,
            IBookingVariantService bookingVariantService,
            ISagaMapper sagaMapper, 
            IProvidersClient providersClient)
        {
            _handlerFactory = handlerFactory;
            _logger = logger;
            _dispatcher = dispatcher;
            _bookingService = bookingService;
            _authService = authService;
            _searchService = searchService;
            _bookingSagaService = bookingSagaService;
            _ticketService = ticketService;
            _mapper = mapper;
            _singleNameService = singleNameService;
            _settingsService = settingsService;
            _flightSearchReadService = flightSearchReadService;
            _travelPoliciesService = travelPoliciesService;
            _airProvidersService = airProvidersService;
            _secretsManager = secretsManager;
            _context = context;
            _ancillaryService = ancillaryService;
            _paymentsService = paymentsService;
            _billingDetailsValidator = billingDetailsValidator;
            _metricsService = metricsService;
            _bookingVariantService = bookingVariantService;
            _sagaMapper = sagaMapper;
            _providersClient = providersClient;
        }

        public async Task HandleAsync(CreateBooking command)
        {
            BookingSaga bookingSaga = null;
            FlightSolution flightSolution = null;
            FlightSolutionAncillaries offeredAncillaries = null;
            Models.Booking originalBooking = null;
            Search.Shared.Models.Search search = null;
            SearchRoute searchRoute = null;
            bool bookingIsApprovalRequired = false;
            int invoiceeId = 0;
            PaymentMethod paymentMethod = PaymentMethod.BankTransfer;
            string paymentMethodId = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    await _authService.ValidateIfBookingCanBeCreatedOrFailAsync();

                    flightSolution = await _searchService.GetFlightSolutionAsync(command.FlightSolutionId);

                    if (flightSolution == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Flight solution was not found.");
                    }

                    search = await _flightSearchReadService.GetSearchAsync(flightSolution.SearchId);

                    if (search == null && command.OriginalBookingId == null)
                    {
                        throw new ValidationException(OperationCodes.Error, "Flight search was not found.");
                    }

                    if (!await CheckSourceIsEnabled(flightSolution))
                    {
                        throw new ValidationException(OperationCodes.SourceDisabled, "One or more sources are globally disabled");
                    }

                    if (!await CheckFundingSourceIsEnabled(flightSolution))
                    {
                        throw new ValidationException(OperationCodes.SourceDisabled, "Funding source is globally disabled");
                    }

                    if (command.OriginalBookingId == null)
                    {
                        searchRoute = search!.Request.Routes.Single(r => r.Id.Equals(flightSolution.RouteId));

                        // NOTE: avoid travel policy for rebooking flow
                        // TODO: Sam & Petr: review how we can support this
                        var travelPolicyValidationResult = await _travelPoliciesService.VerifyTravelPolicy(
                            command.GetUserId(),
                            flightSolution,
                            command.GetCustomFields());

                        bookingIsApprovalRequired = travelPolicyValidationResult.isApprovalRequired;

                        if (travelPolicyValidationResult.isInvalid)
                        {
                            throw new ValidationException(OperationCodes.Error, $"Booking cannot be processed with TravelPolicies '{travelPolicyValidationResult.policyInfo?.RuleResult}'");
                        }
                    }
                    else
                    {
                        originalBooking = await _bookingService.GetBookingAsync(command.OriginalBookingId);
                        search ??= await _flightSearchReadService.GetSearchAsync(originalBooking.SearchId);

                        searchRoute = search.Request.Routes.Single(r => r.Id.Equals(originalBooking.RouteId));
                    }

                    (invoiceeId, paymentMethod, paymentMethodId) = await ValidateInvoiceeAndPaymentMethodAsync(command);

                    if (command.Ancillaries != null && command.Ancillaries.Any())
                    {
                        offeredAncillaries = await _ancillaryService.GetOfferedAncillariesAsync(command.FlightSolutionId);

                        _ancillaryService.ValidateSelectedAncillaries(offeredAncillaries, command);
                    }
                })
                .Run(async () =>
                {
                    MergeCustomFieldsFromSearchToBooking(search, command);

                    var originalBookingLocators = originalBooking is not null ? new[] { originalBooking.Locator } : Array.Empty<string>();
                    var initialBookingLocators = originalBooking is not null ? new[] { originalBooking.InitialLocator ?? originalBooking.Locator } : Array.Empty<string>();
                    var initialBookingId = originalBooking?.InitialBookingId ?? originalBooking?.OriginalBookingId ?? originalBooking?.Id ?? command.BookingId;

                    _logger.ForContext(nameof(command.BookingId), command.BookingId).Information("Create booking saga...");

                    bookingSaga = await _bookingSagaService.CreateAsync(new CreateSagaRequest
                    {
                        BookingId = command.BookingId,
                        OriginalBookingId = command.OriginalBookingId,
                        OriginalBookingLocators = originalBookingLocators,
                        InitialBookingId = initialBookingId,
                        InitialBookingLocators = initialBookingLocators,
                        PaymentMethodId = command.PaymentMethodId,
                        PaymentMethod = paymentMethod,
                        ScreenResolution = command.ScreenResolution,
                        Site = command.Site,
                        SearchId = search.Id,
                        SearchRoute = searchRoute,
                        FlightSolution = flightSolution,
                        OfferedAncillaries = offeredAncillaries,
                        InvoiceeId = invoiceeId,
                        Metadata = command.Metadata,
                        Passenger = command.Passenger,
                        User = command.User,
                        FrequentFlyerNumbers = command.FrequentFlyerNumbers,
                        Ancillaries = command.Ancillaries,
                        Comment = command.Comment,
                        IsApprovalRequired = bookingIsApprovalRequired,
                        RequestInfo = _context.RequestOrigin
                    });
                })
                .OnSuccess(async () =>
                {
                    var tenantId = bookingSaga.BookingEntity.TenantId;
                    _metricsService.IncrementActiveBookingSagaCounter(tenantId);
                    var vesselName = command.Metadata?.VesselName;
                    var vesselFlag = command.Metadata?.VesselFlag;
                    if (!string.IsNullOrWhiteSpace(vesselName) && !string.IsNullOrWhiteSpace(vesselFlag))
                    {
                        await _dispatcher.DispatchAsync(new UpsertVesselInfo
                        {
                            Name = vesselName,
                            Flag = vesselFlag,
                            TenantId = tenantId
                        });
                    }

                    //TODO set provider argument
                    await _dispatcher.DispatchAsync(new CreateReservation
                    {
                        RequestId = command.Request.Id,
                        BookingId = command.BookingId,
                        ProviderKey = bookingSaga.ReservationsToCreate.First().ProviderKey
                    });
                })
                .OnCustomError(async (ex) =>
                {
                    _logger.Warning(ex, "Error occured while creating booking.");
                    await _dispatcher.DispatchAsync(new CreateBookingRejected(command.Request.Id,
                        command.BookingId, ex.Code ?? OperationCodes.Error, ex.Message,
                        tenantId: bookingSaga?.BookingEntity.TenantId,
                        sources: bookingSaga?.ReservationsToCreate.Select(r => r.ProviderKey.GetSourceFromProviderKey()).ToArray()));
                })
                .OnError(async (ex) =>
                {
                    _logger.Error(ex, "Error occured while creating booking.");
                    await _dispatcher.DispatchAsync(new CreateBookingRejected(command.Request.Id,
                        command.BookingId, OperationCodes.Error, ex.Message,
                        tenantId: bookingSaga?.BookingEntity.TenantId,
                        sources: bookingSaga?.ReservationsToCreate.Select(r => r.ProviderKey.GetSourceFromProviderKey()).ToArray()));
                })
                .Lock("Booking/" + command.BookingId)
                .ExecuteAsync();
        }

        public async Task HandleAsync(RejectBookingSaga command)
        {
            BookingSaga saga = null;

            _logger.Information("Handling {@RejectBookingSaga}", command);

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    saga = await _bookingSagaService.GetAsync(command.BookingId);

                    if (saga == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Saga was not found.");
                    }
                })
                .Run(async () =>
                {
                    await _dispatcher.DispatchAsync(new CreateBookingRejected(command.RequestId, command.BookingId, command.RejectCode, command.RejectReason,
                        tenantId: saga.BookingEntity.TenantId,
                        sources: saga.ReservationsToCreate.Select(r => r.ProviderKey.GetSourceFromProviderKey()).ToArray()));
                })
                .OnError((ex) =>
                {
                    if (ex is ValidationException)
                    {
                        _logger.Error(ex, "Error occured while handling RejectBookingSaga");
                        return Task.CompletedTask;
                    }
                    
                    throw ex;
                })
                .Lock("Booking/" + command.BookingId)
                .ExecuteAsync();
        }

        public async Task HandleAsync(CompleteBookingSaga command)
        {
            _logger.Information("Handling {@Command}", command);

            BookingSaga saga = null;
            bool isSagaApproved = false;
            bool isApprovalRequired = false;
            bool isPaymentConfirmationRequired = false;
            bool sendCreateReservationCommand = false;

            ILogger logger = _logger;
            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    saga = await _bookingSagaService.GetAsync(command.BookingId);

                    if (saga == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Saga was not found.");
                    }

                    logger = _logger.ForContext(new BookingSagaLogEnricher(saga, command));
                    
                    isApprovalRequired = saga.BookingEntity.State is Domain.Aggregates.BookingAggregate.Enums.BookingState.ApprovalRequired;
                    isSagaApproved = saga.SagaState is Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.Approved;
                })
                .Run(async () =>
                {
                    // Booking with credit card and payment id is not yet set, this is initial booking transaction:
                    // Initiate hold on CC, and store payment reference within the saga (to be synced to booking)
                    if (saga.BillingEntity.PaymentMethod is Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.CreditCard)
                    {
                        if (string.IsNullOrEmpty(saga.BillingEntity.PaymentId))
                        {
                            logger.Information("Handling new CreditCard payment for booking");

                            BookingPaymentDetails paymentDetails;

                            using (LogContext.Push(new BookingSagaLogEnricher(saga, command)))
                            {
                                paymentDetails = await _paymentsService.CreatePaymentAsync(saga);
                            }

                            saga = await _bookingSagaService.CreatePaymentAsync(new CreateSagaPaymentRequest
                            {
                                BookingSagaId = saga.Id,
                                PaymentId = paymentDetails.Id,
                                PaymentExpiry = paymentDetails.ExpiresAt,
                                PaymentConfirmationRequired = paymentDetails.PaymentConfirmationRequired
                            });

                            // NOTE: set payment is required, this will change target booking state
                            isPaymentConfirmationRequired = paymentDetails.PaymentConfirmationRequired;

                            if (!isPaymentConfirmationRequired)
                            {
                                foreach (var reservation in saga.Reservations.Where(r => r.State is not Domain.Aggregates.BookingAggregate.Enums.ReservationState.Virtual))
                                {
                                    await _bookingService.SetPaymentRequiredAsync(reservation.Id, false);
                                }
                            }
                        }
                        else if (saga.SagaPaymentState is not Domain.Aggregates.BookingAggregate.Enums.BookingSagaPaymentState.PaymentConfirmed)
                        {
                            logger.Warning("Handling CompleteBookingSaga with unconfirmed credit card payment for booking");

                            // booking has payment reference, but it was not confirmed yet
                            isPaymentConfirmationRequired = true;
                        }
                    }

                    if (isPaymentConfirmationRequired)
                    {
                        logger.Information("CompleteBookingSaga - SetStateInPayment");
                        await SaveBookingAsync(saga, BookingState.PaymentRequired);
                        saga = await _bookingSagaService.ChangePaymentStateAsync(saga.Id, Domain.Aggregates.BookingAggregate.Enums.BookingSagaPaymentState.InPayment);
                    }
                    else if (isApprovalRequired)
                    {
                        logger.Information("CompleteBookingSaga - SetStateInApproval");
                        await SaveBookingAsync(saga, BookingState.ApprovalRequired);
                        saga = await _bookingSagaService.ChangeStateAsync(saga.Id, Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.InApproval);
                    }
                    else
                    {
                        if (saga.Reservations.Any(r => r.State is Domain.Aggregates.BookingAggregate.Enums.ReservationState.New))
                        {
                            logger.Information("CompleteBookingSaga - SetVirtualBookingPaymentConfirmed");
                            saga = await _bookingSagaService.ChangePaymentStateAsync(saga.Id, Domain.Aggregates.BookingAggregate.Enums.BookingSagaPaymentState.PaymentConfirmed);
                            sendCreateReservationCommand = true;
                        }
                        else
                        {
                            logger.Information("CompleteBookingSaga - SetStateCompleted");
                            await SaveBookingAsync(saga);
                            if (isSagaApproved) // TODO: check if we need this branch here, this might be already handled by BookingApprovedHandler
                            {
                                await _bookingService.SetAsApprovedByAsync(saga.Id, _mapper.Map<User[]>(saga.BookingEntity.ApprovedBy));
                            }

                            saga = await _bookingSagaService.ChangeStateAsync(saga.Id, Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.Completed);
                            _metricsService.DecrementActiveBookingSagaCounter(saga.BookingEntity.TenantId);
                        }
                    }
                })
                .OnSuccess(async () =>
                {
                    logger.Information("CompleteBookingSaga - Success");
                    
                    if (isPaymentConfirmationRequired)
                    {
                        // Booking saga completes with booking in state PaymentRequired, virtual PNRs will be preserved
                        await DispatchAsync(new BookingCreatedWithPaymentRequired(saga.BookingEntity.CreatedBy.UserIdentity, command.RequestId, saga.Id, saga.BookingEntity.TenantId));
                    }
                    else if (sendCreateReservationCommand && saga.ReservationsToCreate.Any() && BookingSaga.IsCreationAllowed(saga.ReservationsToCreate.First()))
                    {
                        // Payment was confirmed, approval is not required, but we need to convert some virtual PNRs
                        await DispatchAsync(new CreateReservation { RequestId = command.RequestId, BookingId = saga.Id, ProviderKey = saga.ReservationsToCreate.First().ProviderKey });
                    }
                    else if (isSagaApproved)
                    {
                        // Booking was approved. Saga completes in Confirmed state
                        // We need to dispatch BookingConfirmed event here, ApprovalFlowService will not dispatch this event
                        // if booking have virtual reservations or reservations to create
                        await DispatchBookingConfirmed(saga, command, logger);
                        
                        await DispatchBookingCreatedNotification(command.RequestId, saga, isApprovalRequired, logger);
                    }
                    else
                    {
                        var travelerDetailsUpdate = CreateTravelerDetailsUpdateEvent(saga.BookingEntity, saga.FlightSolutionEntity.Passenger);
                        await DispatchAsync(travelerDetailsUpdate);
                        // Booking saga completes either in Confirmed of Approval required state
                        await DispatchAsync(new BookingCreated(command.RequestId, saga.Id, saga.BookingEntity.TenantId, saga.BookingEntity.CreatedBy.UserIdentity, isApprovalRequired));

                        if (!isApprovalRequired)
                        {
                            await DispatchBookingCreatedNotification(command.RequestId, saga, isApprovalRequired, logger);
                        }
                        else
                        {
                            // Approval Flow request if required
                            _logger.Information("Sending RequestApproval command for Booking {BookingId}", saga.Id);
                            await DispatchAsync(new RequestApproval
                            {
                                Id = Id.New(),
                                ReferenceId = saga.Id,
                                CreatedBy = new Messages.Commands.Models.User
                                {
                                    Id = saga.BookingEntity.CreatedBy.UserIdentity,
                                    Name = saga.BookingEntity.CreatedBy.Name,
                                    Email = saga.BookingEntity.CreatedBy.Email,
                                    TenantId = saga.BookingEntity.TenantId
                                },
                                ApplicableFor = ApprovalApplicableFor.Flight
                            });
                        }
                    }

                    if (saga.BillingEntity.PaymentMethod is Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.CreditCard)
                    {
                        await DispatchAsync(new BookingPaymentCompleted
                        {
                            RequestId = command.RequestId,
                            BookingId = saga.Id,
                            PaymentId = saga.BillingEntity.PaymentId
                        });
                    }

                    async Task DispatchAsync<T>(T message) where T : IMessage
                    {
                        await _dispatcher.DispatchAsync(message);
                        logger.Information("CompleteBookingSaga - Dispatched {@Message}", message);
                    }
                })
                .OnError(async ex =>
                {
                    // NOTE: only ServiceException and InvalidOperationException can interrupt the booking flow. 
                    if (ex is ServiceException || ex is InvalidOperationException)
                    {
                        logger.Error(ex, "Error occured while trying to complete booking saga");

                        await _dispatcher.DispatchAsync(new CreateBookingRejected(command.RequestId, command.BookingId,
                            (ex as ServiceException)?.Code ?? OperationCodes.Error, ex.Message,
                            tenantId: saga.BookingEntity.TenantId,
                            sources: saga.ReservationsToCreate.Select(r => r.ProviderKey.GetSourceFromProviderKey())
                                .ToArray()));
                    }
                    else
                    {
                        throw ex;
                    }
                })
                .Lock("Booking/" + command.BookingId)
                .ExecuteAsync();
        }

        private async Task DispatchBookingConfirmed(BookingSaga saga, CompleteBookingSaga command, ILogger logger)
        {
            try
            {
                var mappedReservations = _sagaMapper.MapToCommonReservations(saga).ToArray();
                var mappedBooking = _sagaMapper.MapToCommonBooking(saga);

                await DispatchAsync(new BookingConfirmed(
                    saga.BookingEntity.CreatedBy.UserIdentity, 
                    command.RequestId, 
                    bookingId: saga.Id, 
                    saga.BookingEntity.TenantId)
                {
                    Reservations = mappedReservations,
                    Booking = mappedBooking
                });
            }
            catch (Exception e)
            {
                _logger.ForContext("CorrelationId", saga.Id)
                    .Error(e, "{EventName} Error occured while mapping booking data", nameof(BookingConfirmed));
                
                await DispatchAsync(new BookingConfirmed(saga.BookingEntity.CreatedBy.UserIdentity, 
                    command.RequestId, saga.Id, saga.BookingEntity.TenantId));
            }
            
            async Task DispatchAsync<T>(T message) where T : IMessage
            {
                await _dispatcher.DispatchAsync(message);
                logger.Information("CompleteBookingSaga - Dispatched {@Message}", message);
            }
        }
        
        private async Task DispatchBookingCreatedNotification(
            string requestId,
            BookingSaga saga,
            bool isApprovalRequired,
            ILogger logger)
        {
            try
            {
                var mappedReservations = _sagaMapper.MapToCommonReservations(saga).ToArray();
                var mappedBooking = _sagaMapper.MapToCommonBooking(saga);

                await DispatchAsync(new BookingCreatedNotification
                {
                    Reservations = mappedReservations,
                    Booking = mappedBooking,
                    IsApprovalRequired = isApprovalRequired,
                    RequestId = requestId,
                    BookingId = saga.Id,
                    TenantId = saga.BookingEntity.TenantId,
                    UserId = saga.BookingEntity.CreatedBy.UserIdentity
                });
            }
            catch (Exception e)
            {
                _logger.ForContext("CorrelationId", saga.Id)
                    .Error(e, "{EventName} Error occured while mappping booking data", nameof(BookingCreatedNotification));

                await DispatchAsync(new BookingCreatedNotification
                {
                    IsApprovalRequired = isApprovalRequired,
                    RequestId = requestId,
                    BookingId = saga.Id,
                    TenantId = saga.BookingEntity.TenantId,
                    UserId = saga.BookingEntity.CreatedBy.UserIdentity
                });
            }

            async Task DispatchAsync<T>(T message) where T : IMessage
            {
                await _dispatcher.DispatchAsync(message);
                logger.Information("CompleteBookingSaga - Dispatched {@Message}", message);
            }
        }

        private async Task SaveBookingAsync(BookingSaga saga, BookingState? bookingState = null)
        {
            var booking = _mapper.Map<Models.Booking>(saga);
            if (bookingState.HasValue)
            {
                booking.State = bookingState.Value;
            }
            await _bookingService.AddBookingAsync(booking);
        }

        public async Task HandleAsync(BookingCreated @event)
        {
            _logger.Information("Handling {EventName}, BookingId {BookingId}", nameof(BookingCreated), @event.BookingId);

            if (@event.ApprovalRequired)
            {
                _logger.ForContext("CorrelationId", @event.BookingId).Information("BookingCreated with state ApprovalRequired");
            }

            ICollection<Reservation> reservations = null;
            Models.Booking booking = null;

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservations = (await _bookingService.GetBookingReservationsAsync(@event.BookingId))
                        ?.Where(r => !r.IsVirtual)
                        .ToList();

                    if (reservations == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Reservations were not found.");
                    }

                    booking = await _bookingService.GetBookingAsync(@event.BookingId);
                    if (booking == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Booking was not found.");
                    }
                })
                .Run((async () =>
                {
                    await ProcessConfirmedBooking(@event.RequestId, reservations, booking);
                    _metricsService.IncrementBookingCreatedCounter(booking.TenantId);
                }))
                .Lock("Booking/" + @event.BookingId)
                .ExecuteAsync();
        }

        public async Task HandleAsync(BookingConfirmed @event)
        {
            _logger.ForContext("CorrelationId", @event.BookingId).Information("BookingConfirmed - Start processing");

            ICollection<Reservation> reservations = null;
            Models.Booking booking = null;

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    reservations = (await _bookingService.GetBookingReservationsAsync(@event.BookingId))
                        ?.Where(r => !r.IsVirtual)
                        .ToList();

                    if (reservations == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Reservations were not found.");
                    }

                    booking = await _bookingService.GetBookingAsync(@event.BookingId);
                    if (booking == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Booking was not found.");
                    }
                })
                .Run((async () =>
                {
                    await ProcessConfirmedBooking(@event.RequestId, reservations, booking);
                }))
                .Lock("Booking/" + @event.BookingId)
                .ExecuteAsync();

            _logger.ForContext("CorrelationId", @event.BookingId).Information("BookingConfirmed - Finish processing");
        }

        /// <summary>
        /// Cleans up reservations that could have already been sent.
        /// Updates saga state.
        /// Dispatches ResetBookingState if booking was already saved in db
        /// </summary>
        public async Task HandleAsync(CreateBookingRejected @event)
        {
            BookingSaga saga = null;

            _logger.Information("Handling {@Event}", @event);

            await _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    saga = await _bookingSagaService.GetAsync(@event.BookingId);

                    if (saga == null)
                    {
                        throw new ValidationException(OperationCodes.NotFound, "Saga was not found.");
                    }
                })
                .Run(async () =>
                {
                    if (saga.SagaState is
                        Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.Rejected or
                        Domain.Aggregates.BookingAggregate.Enums.BookingSagaState.Completed)
                    {
                        return;
                    }

                    var booking = await _bookingService.GetBookingAsync(@event.BookingId);

                    foreach (var reservation in saga.Reservations)
                    {
                        if (reservation.State is not Domain.Aggregates.BookingAggregate.Enums.ReservationState.Unknown)
                            await CancelReservationAsync(reservation, booking);
                    }

                    saga = await _bookingSagaService.CancelAsync(saga.Id, @event.Reason);

                    if (@event.Code == OperationCodes.AvailabilityError)
                    {
                        await _dispatcher.DispatchAsync(new FlightSolutionUnavailable
                        {
                            SearchId = saga.FlightSolutionEntity.SearchId,
                            FlightSolutionId = saga.FlightSolutionEntity.FlightSolutionId,
                            ErrorCode = @event.Code,
                            ErrorMessage = @event.Reason,
                            CreatedAt = DateTime.UtcNow
                        });

                        if (!string.IsNullOrEmpty(saga.BookingEntity.OriginalBookingId))
                        {
                            await _dispatcher.DispatchAsync(new ExcludeFlightSolution
                            {
                                BookingId = @event.BookingId,
                                FlightSolutionId = @event.FlightSolutionId,
                            });
                        }
                    }

                    if (saga.BillingEntity.PaymentMethod is Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.CreditCard && !String.IsNullOrEmpty(saga.BillingEntity.PaymentId))
                    {
                        _logger.Information("Cancelling credit card payment for failed booking {BookingId}", saga.Id);
                        await _dispatcher.DispatchAsync(new CancelPaymentForBookingSaga { BookingId = saga.Id });
                    }

                    _metricsService.IncrementBookingSagaRejectedCounter(@event.TenantId, @event.Reason);
                    _metricsService.DecrementActiveBookingSagaCounter(@event.TenantId);
                })
                .OnError(async (ex) =>
                {
                    if (ex is ValidationException || ex is InvalidOperationException)
                    {
                        _logger.Error(ex, "Error occured while handling CreateBookingRejected");
                        return;
                    }
                    throw ex;
                })
                .Lock("Booking/" + @event.BookingId)
                .ExecuteAsync();
        }

        private async Task CancelReservationAsync(ReservationEntity reservation, Models.Booking booking)
        {
            if (reservation.State is Domain.Aggregates.BookingAggregate.Enums.ReservationState.Cancelled)
                return;

            var reservations = await Task.WhenAll(
                new[] { reservation.Id, reservation.OriginalReservationId }
                    .Select(rid => _bookingService.GetReservationAsync(rid)));

            var fullReservation = reservations[0];
            var originalReservation = reservations[1];

            if (fullReservation is null && originalReservation is null)
                throw new ValidationException($"Reservation {reservation.Id} was not found while handling CreateBookingRejected event");

            if (reservation.State is Domain.Aggregates.BookingAggregate.Enums.ReservationState.Active)
            {
                // NOTE: CancelReservation command was issued here, although due to CT-2375, we skip that step in the pipeline
                // and issue ProviderCancelReservation command directly
                await _dispatcher.DispatchAsync(new ProviderCancelReservation
                {
                    Request = Request.New<ProviderCancelReservation>(),
                    ReservationId = fullReservation!.Id,
                    Source = fullReservation.Source,
                    Locators = fullReservation.Locators,
                    DepartureAt = fullReservation.DepartureAt
                }, fullReservation.Source.GetProvider());
            }
            else
            {
                await _bookingService.ChangeReservationStateAsync(fullReservation?.Id ?? originalReservation.Id, ReservationState.Cancelled);

                if (fullReservation?.OriginalReservationId is not null)
                    await _bookingService.ChangeReservationStateAsync(fullReservation.OriginalReservationId, ReservationState.Cancelled);

                if (booking != null)
                {
                    // NOTE: dispatch ResetBookingState if booking was previously saved to DB somewhere ealier in the flow
                    await _dispatcher.DispatchAsync(new ResetBookingState
                    {
                        Request = Request.New<ResetBookingState>(),
                        BookingId = fullReservation?.BookingId ?? originalReservation.BookingId
                    });
                }
            }
        }

        private async Task ProcessConfirmedBooking(string requestId, ICollection<Reservation> reservations, Models.Booking booking)
        {
            foreach (var reservation in reservations)
            {
                var airGatewayTicketless = reservation.Source.IsAirgateway() && reservation.Tickets.Count == 0;
                if (airGatewayTicketless)
                {
                    _logger.ForContext("BookingId", reservation.BookingId)
                        .Information("Dispatching ResetBookingState since the AGW booking is ticketless");

                    await _dispatcher.DispatchAsync(new ResetBookingState
                    {
                        Request = Request.New<ResetBookingState>(),
                        BookingId = reservation.BookingId
                    });
                }

                // TODO: This is a hack for Airgateway. We should remove this when Airgateway is fixed
                if (reservation.IsVirtual && reservation.Source.IsAirgateway())
                {
                    _logger.ForContext("BookingId", reservation.BookingId)
                        .Information("Set LatestTicketingTime as null for virtual AGW");
                    
                    reservation.Fare.LatestTicketingTime = null;
                    await _bookingService.SetTicketingTimeAsync(reservation.Id, null);
                }

                if (!reservation.ApprovalRequired && reservation.Ticketless)
                {
                    if (reservation.State == ReservationState.Active)
                    {
                        _logger.ForContext("BookingId", reservation.BookingId)
                            .Information("Send TicketIssued for ticketless");
                        
                        await _dispatcher.DispatchAsync(new TicketIssued(requestId,
                            reservation.BookingId,
                            reservation.Id,
                            LocatorsHelper.GetProviderCode(reservation.Locators),
                            LocatorsHelper.GetProviderCode(reservation.Locators),
                            reservation.Fare.Net,
                            reservation.DepartureAt,
                            booking.CreatedBy.Name,
                            reservation.TenantId,
                            booking.CreatedBy.Name,
                            reservation.Source,
                            reservation.CreatedAt,
                            reservation.Fare.PlatingCarrier,
                            reservation.FundingSource));
                    }

                    // NOTE: just continue here to the next reservation.
                    // No need to set ticketing time, no need to set refresh fare time
                    continue;
                }
               
                if (!reservation.IsVirtual)
                {
                    if (reservation.TicketingAt.HasValue)
                    {
                        _logger.Information("Skipping ticketing time setter for {Locator}. Ticketing is set to {TicketingTime} for reservation {ReservationId}",
                            LocatorsHelper.GetProviderCode(reservation.Locators), reservation.TicketingAt.Value, reservation.Id);
                    }
                    else
                    {
                        var provider = await _airProvidersService.GetSourceAsync(reservation.Source);
                        DateTime? easyPayExpirationDate = null;
                        if (provider.DefaultPaymentMethod == PaymentMethods.EasyPay)
                        {
                            var sec = await _secretsManager.GetEasyPayAsync(reservation.Source);
                            if (sec?.Number == null || sec?.ExpDate == null)
                            {
                                _logger.Error("Invalid EasyPay number");
                            }

                            easyPayExpirationDate = DateFormatHelper.ToDateTime(sec.ExpDate, "yyyy-MM");
                        }

                        var ticketingTime = _ticketService.CalculateTicketingTime(reservation, booking.PaymentExpiresAt, easyPayExpirationDate: easyPayExpirationDate);

                        _logger.Information("Settings ticketing time for {Locator} to {TicketingTime}",
                                                    LocatorsHelper.GetProviderCode(reservation.Locators), ticketingTime);

                        await _bookingService.SetTicketingTimeAsync(reservation.Id, ticketingTime);
                        await _dispatcher.DispatchAsync(new TicketingAtUpdated
                        {
                            BookingId = reservation.BookingId,
                            ReservationId = reservation.Id,
                            TenantId = reservation.TenantId,
                            TicketingAt = ticketingTime
                        });
                    }

                    await _bookingService.SetRefreshFareTimeAsync(reservation.Id, System.DateTime.UtcNow.AddHours(24));

                    await _dispatcher.DispatchAsync(new CheckReservationIfCheapestFareSet(reservation.Id));
                }
            }

            if (booking.OriginalBookingId != null)
            {
                _logger.Information("Booking {BookingId} is a rebooking. Cancelling original booking {OriginalBookingId}", booking.Id, booking.OriginalBookingId);
                
                await _dispatcher.DispatchAsync(new CancelBooking
                {
                    BookingId = booking.OriginalBookingId,
                    Request = Request.New<CancelBooking>(requestId),
                    User = new Messages.Commands.Models.User { Email = "system" }
                });

                _logger.Information("Dispatched CancelBooking for original booking {OriginalBookingId}", booking.OriginalBookingId);
                
                await _dispatcher.DispatchAsync(new RebookingCompleted
                {
                    BookingId = booking.OriginalBookingId,
                    NewBookingLocator = booking.Locator
                });
            }

            if (!PassengerHelper.IsSingleNameOnly(booking.Passenger))
            {
                return;
            }

            foreach (var reservation in reservations)
            {
                var exists = await _singleNameService.SettingExistsAsync(reservation.Fare.PlatingCarrier);
                if (exists)
                {
                    continue;
                }

                _logger.Information("Single name setting not found for {PlatingCarrier}. Dispatching SingleNameSettingNotFound event", reservation.Fare.PlatingCarrier);
                
                await _dispatcher.DispatchAsync(new SingleNameSettingNotFound
                {
                    PlatingCarrier = reservation.Fare.PlatingCarrier,
                    BookingId = booking.Id,
                    Locator = LocatorsHelper.GetProviderCode(reservation.Locators)
                });
            }
        }

        private async Task<bool> CheckSourceIsEnabled(FlightSolution flightSolution)
        {
            var sourceEnabled = true;
            foreach (var source in flightSolution.ProviderKeys.Select(StringExtensions.GetSourceFromProviderKey).Distinct())
            {
                if (await _settingsService.CheckSourceGloballyEnabled(source))
                {
                    continue;
                }

                sourceEnabled = false;
                _logger.Warning("Could not create booking for flight solution {FlightSolutionId} because source {Source} is globally disabled",
                    flightSolution.Id, source);
            }

            return sourceEnabled;
        }

        private async Task<bool> CheckFundingSourceIsEnabled(FlightSolution flightSolution)
        {
            var sourceEnabled = true;
            var fundingSources = new List<string> { flightSolution.FundingSource };

            if (flightSolution.SplitProviderODs != null)
                fundingSources.AddRange(flightSolution.SplitProviderODs.Select(od => od.FundingSource));
            
            foreach (var source in fundingSources.WhereNotNull().Distinct())
            {
                if (await _providersClient.GetFundingSourceEnabledAsync(source))
                {
                    continue;
                }

                sourceEnabled = false;
                _logger.Warning(
                    "Could not create booking for flight solution {FlightSolutionId} because Funding source {FundingSource} is disabled",
                    flightSolution.Id, source);
            }

            return sourceEnabled;
        }

        private async Task<(int, PaymentMethod, string)> ValidateInvoiceeAndPaymentMethodAsync(CreateBooking command)
        {
            var result = await _billingDetailsValidator.ValidateCreateBookingAsync(command);
            return (result.InvoiceeId, result.PaymentMethod, result.PaymentMethodId);
        }

        private void MergeCustomFieldsFromSearchToBooking(Search.Shared.Models.Search search, CreateBooking command)
        {

            KeyValuePair<string, string>[] searchCustomFields = Array.Empty<KeyValuePair<string, string>>();
            if (search != null)
            {
                searchCustomFields = search.Request.Routes
                .SelectMany(route => route.Passengers)
                .Where(passenger =>
                    $"{passenger.FirstName}_{passenger.LastName}_{passenger.DateOfBirth}".ToLower() ==
                    $"{command.Passenger.FirstName}_{command.Passenger.LastName}_{passenger.DateOfBirth}".ToLower())
                .Where(passenger => (passenger.CustomFields?.Count > 0))
                .SelectMany(passenger => passenger.CustomFields)
                .ToArray();
            }


            if (command.Metadata.CustomFields == null && searchCustomFields.Any())
            {
                command.Metadata.CustomFields = new Dictionary<string, string>();
            }

            foreach (var customField in searchCustomFields)
            {
                if (!command.Metadata.CustomFields.ContainsKey(customField.Key))
                {
                    command.Metadata.CustomFields.Add(customField.Key, customField.Value);
                }
            }
        }

        private class BookingSagaLogEnricher : ILogEventEnricher
        {
            private readonly BookingSaga _saga;
            private readonly IMessage _message;

            public BookingSagaLogEnricher(BookingSaga saga, IMessage message)
            {
                _saga = saga;
                _message = message;
            }
            
            public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
            {
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("Message", _message, true));
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("BookingId", _saga.Id));
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("SagaState", _saga.SagaState));
                logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("BookingState", _saga.BookingEntity.State));
                if (_saga.BillingEntity.PaymentMethod is Domain.Aggregates.BookingAggregate.Enums.PaymentMethod.CreditCard)
                {
                    logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("PaymentState", _saga.SagaPaymentState));
                }
            }
        }
        
        private static TravelerDetailsUpdate CreateTravelerDetailsUpdateEvent(BookingEntity booking, PassengerEntity passenger)
        {
            var docType = passenger.DocumentType switch
            {
                DocumentType.Passport => DocType.Passport,
                _ => DocType.Passport
            };
            var document = new Document(
                Type: docType,
                Number: passenger.DocumentNumber,
                Country: passenger.DocumentCountry,
                ExpireDate: passenger.DocumentExpire);
            
            var emails = string.IsNullOrWhiteSpace(passenger.Email)
                ? Array.Empty<Email>()
                : new[]
                {
                    new Email(
                        Type: ContactType.Work,
                        Value: passenger.Email)
                };

            var phones = string.IsNullOrWhiteSpace(passenger.Phone)
                ? Array.Empty<Phone>()
                : new[]
                {
                    new Phone(
                        Type: ContactType.Work,
                        Value: passenger.Phone)
                };

            var gender = passenger.Gender switch
            {
                Gender.Female => "F",
                _ => "M"
            };

            var dateOfBirth = DateTime.TryParse(passenger.DateOfBirth, out var dob) ? dob : default;
            
            var loyaltyPrograms = booking.FreqFlyerNums?
                .Select(kv => new LoyaltyProgram(
                    Type: LoyaltyProgramType.Flight,
                    Code: kv.Code,
                    Carrier: kv.Carrier,
                    Group: kv.ProgramName,
                    Number: kv.Number)
                ).ToArray() ?? Array.Empty<LoyaltyProgram>();

            var traveler = new Traveler(
                Id: null,
                FirstName: passenger.FirstName,
                LastName: passenger.LastName,
                DateOfBirth: dateOfBirth,
                Nationality: passenger.Nationality,
                Gender: gender,
                Emails: emails,
                Phones: phones,
                LoyaltyPrograms: loyaltyPrograms,
                Documents: new[] { document }
            );

            var @event = new TravelerDetailsUpdate(
                Traveler: traveler,
                BookingId: booking.OriginalBookingId!,
                TenantId: booking.TenantId);
            return @event;
        }
    }
}
