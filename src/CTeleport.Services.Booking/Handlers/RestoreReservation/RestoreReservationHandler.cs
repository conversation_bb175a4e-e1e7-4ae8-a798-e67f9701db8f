using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using CTeleport.Common.Exceptions;
using CTeleport.Messages.Commands.Models;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.Settings.Services;
using CTeleport.Services.Booking.Services;
using System.Linq;
using Serilog;
using AutoMapper;
using CTeleport.Common.Messaging.Services;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.CustomFields.Dto;
using CTeleport.Services.Helpers;

namespace CTeleport.Services.Booking.Handlers
{
    public class RestoreReservationHandler : ICommandHandler<RestoreReservation>
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly ILogger _logger;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ICustomFieldsService _customFieldsService;
        private readonly ISettingsService _settingsService;
        private readonly IMapper _mapper;

        public RestoreReservationHandler(IHandlerFactory handlerFactory, IBookingService bookingService,
            ILogger logger, IMessageDispatcher dispatcher, ISettingsService settingsService, ICustomFieldsService customFieldsService, IMapper mapper)
        {
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _logger = logger;
            _dispatcher = dispatcher;
            _customFieldsService = customFieldsService;
            _settingsService = settingsService;
            _mapper = mapper;
        }

        public async Task HandleAsync(RestoreReservation command)
        {
            Reservation reservation = null;
            Models.Booking booking = null;

            await _handlerFactory
                .Create(command)
                .Validate(async () =>
                {
                    reservation = await _bookingService.GetReservationAsync(command.ReservationId);
                    if (reservation == null)
                    {
                        throw new ValidationException("Reservation not found");
                    }

                    booking = await _bookingService.GetBookingAsync(reservation.BookingId);
                    if (booking == null)
                    {
                        throw new ValidationException("Booking not found");
                    }

                    if (reservation.State != ReservationState.Active)
                    {
                        throw new ValidationException($"Reservation is {reservation.State.ToString()}");
                    }
                })
                .Run(async () =>
                {
                    _logger.Information("Rebooking reservation {Locator}", LocatorsHelper.GetProviderCode(reservation.Locators));

                    var remarks = new List<Messages.Commands.Models.RemarkMetadata>();
                    try
                    {
                        remarks = await BuildRemarksMetadataAsync(reservation, booking);
                        _logger.ForContext(nameof(command.ReservationId), command.ReservationId).Information("{@remarks}", remarks);
                    }
                    catch (Exception e)
                    {
                        _logger.ForContext(nameof(command.ReservationId), command.ReservationId).Error(e, "Unable to create Remarks metadata in Restore Reservation");
                    }

                    await _dispatcher.DispatchAsync(new ProviderRestoreReservation
                    {
                        ReservationId = reservation.Id,
                        Request = command.Request,
                        Source = reservation.Source,
                        Locators = reservation.Locators,
                        AllowHigherPrice = command.AllowHigherPrice,
                        PriceDiffUpTo = command.PriceDiffUpTo,
                        Passenger = _mapper.Map<Messages.Commands.Models.PassengerDetails>(booking.Passenger),
                        ReservationMetadata = new ReservationMetadata()
                        {
                            RemarksMetadata = remarks
                        },
                        Segments = _mapper.Map<ICollection<ICollection<Messages.Commands.Models.Segment>>>(reservation.LegSegments),
                        Currency = reservation.Price.OriginalCurrency,
                        PlatingCarrier = reservation.Fare.PlatingCarrier,
                        PassengerType = reservation.Fare.PaxType,
                        PrivateFareCode = reservation.Fare.PrivateFareCode
                    }, reservation.Source.GetProvider());
                })
                .OnError(async ex =>
                {
                    _logger.Error(ex, "Error on restore reservation {Locator}", LocatorsHelper.GetProviderCode(reservation.Locators));

                    await _dispatcher.DispatchAsync(new RestoreReservationRejected
                    {
                        ReservationId = reservation.Id,
                        BookingId = reservation.BookingId,
                        Locators = reservation.Locators,
                        PaxLastName = booking.Passenger.LastName,
                        Reason = ex.Message
                    });
                })
                .ExecuteAsync();
        }

        //TODO: review if we can Combine it with ReservationBuilder.CalculateCustomFields
        private async Task<List<Messages.Commands.Models.RemarkMetadata>> BuildRemarksMetadataAsync(Reservation reservation, Models.Booking booking)
        {
            var reservationParams = await BuildRestoreReservationParams(reservation, booking);

            var context = await _customFieldsService.GetContextMetadata(reservationParams.TenantId, reservationParams.Source, reservationParams.Metadata?.VesselName);

            var request = CustomMetadataRequestBuilder.GetMetadataRequest(reservationParams, context);
            request.Type = CustomMetadataRequestType.RestoreReservation;

            var metadata = await _customFieldsService.CreateMetadata(request);

            var remarksMetadata = new List<RemarkMetadata>();

            try
            {
                var remarksRequest = CustomMetadataRequestBuilder.GetRemarksRequest(context, reservationParams, reservationParams.CorrelationId, request, metadata);

                remarksMetadata = await _customFieldsService.GenerateRemarks(remarksRequest);

                _logger.ForContext("BookingId", reservationParams.CorrelationId).Information("{@remarks}", remarksMetadata);

                return remarksMetadata;
            }
            catch (Exception ex)
            {
                _logger.ForContext("BookingId", reservationParams.CorrelationId).Error(ex, "Building remarks threw an exception.");
            }

            return remarksMetadata;
        }

        private async Task<ModifyReservationParams> BuildRestoreReservationParams(Reservation reservation, Models.Booking booking)
        {
            return new ModifyReservationParams
            {
                InitialLocator = booking.InitialLocator,
                ReservationPrice = reservation.Price,
                CorrelationId = booking.Id,
                PlatingCarrier = reservation.Fare.PlatingCarrier,
                CreatedBy = booking.CreatedBy,
                TenantId = reservation.TenantId,
                Source = reservation.Source,
                Metadata = booking.Metadata,
                Passenger = booking.Passenger,
                Price = new CreateReservationPrice
                {
                    QuotedPrice = reservation.Price.Total,
                    Currency = reservation.Price.Currency,
                    OriginalCurrency = reservation.Price.OriginalCurrency,
                    //TotalTaxes = reservation.Price.TotalTaxes, TODO?
                    Markup = reservation.Price.Markup,
                    MarkupCurrency = reservation.Price.MarkupCurrency,
                },
                LegSegments = ReservationExtensions.ConvertSegments(reservation.LegSegments).Select(e => e.ToArray()).ToArray(),
                CorporateCodes = await GetCorporatesCodesAsync(reservation.TenantId),
                FareType = reservation.Fare.FareType,
                Site = booking.Site
            };
        }

        private async Task<Dictionary<string, string>> GetCorporatesCodesAsync(string tenantId)
        {
            try
            {
                return await _settingsService.GetTenantCorporateCodesAsync(tenantId);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Could not get corporates codes for {Tenant}", tenantId);
                return new Dictionary<string, string>();
            }
        }
    }


}