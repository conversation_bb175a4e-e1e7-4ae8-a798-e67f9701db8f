using System.Threading.Tasks;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Helpers;
using Serilog;

namespace CTeleport.Services.Booking.Handlers
{
   public class ProviderRestoreReservationRejectedHandler : IEventHandler<ProviderRestoreReservationRejected>
   {
       private readonly IHandlerFactory _handlerFactory;
       private readonly IBookingService _bookingService;
       private readonly ILogger _logger;
       private readonly IMessageDispatcher _dispatcher;

       public ProviderRestoreReservationRejectedHandler(IHandlerFactory handlerFactory, IBookingService bookingService, ILogger logger, IMessageDispatcher dispatcher)
       {
           _handlerFactory = handlerFactory;
           _bookingService = bookingService;
           _logger = logger;
           _dispatcher = dispatcher;
       }

       public async Task HandleAsync(ProviderRestoreReservationRejected @event)
       {
           Reservation reservation = null;
           Models.Booking booking = null;
           await _handlerFactory
               .Create(@event)
               .Validate(async () =>
               {
                   reservation = await _bookingService.GetReservationAsync(@event.ReservationId);
                   if (reservation == null)
                   {
                       throw new ValidationException("Reservation not found");
                   }

                   booking = await _bookingService.GetBookingAsync(reservation.BookingId);
                   if (booking == null)
                   {
                       throw new ValidationException("Booking not found");
                   }
               })
               .Run(async () =>
               {
                   _logger.Information("Rebooking reservation {Locator}", LocatorsHelper.GetProviderCode(reservation.Locators));

                   await _dispatcher.DispatchAsync(new RestoreReservationRejected
                   {
                       ReservationId = reservation.Id,
                       RequestId = @event.RequestId,
                       Locators = reservation.Locators,
                       Code = @event.Code
                   });
               })
               .OnError(async ex =>
               {
                   _logger.Error(ex, "Error on restore reservation {Locator}", LocatorsHelper.GetProviderCode(reservation.Locators));

                   await _dispatcher.DispatchAsync(new RestoreReservationRejected
                   {
                       ReservationId = reservation.Id,
                       BookingId = reservation.BookingId,
                       Locators = reservation.Locators,
                       PaxLastName = booking.Passenger.LastName,
                       Reason = ex.Message
                   });
               })
               .ExecuteAsync();
       }
   }
}