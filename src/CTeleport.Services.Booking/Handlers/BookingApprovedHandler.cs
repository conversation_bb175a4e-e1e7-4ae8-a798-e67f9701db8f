using System;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.SearchProxy.Services;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Messages;
using BookingSaga = CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Booking;

namespace CTeleport.Services.Booking.Handlers
{
    public class BookingApprovedHandler : IEventHandler<BookingApproved>
    {
        private readonly IFlightSolutionService _flightSolutionReadService;
        private readonly IApprovalFlowService _approvalFlowService;
        private readonly IBookingSagaService _bookingSagaService;
        private readonly IHandlerFactory _handlerFactory;
        private readonly IBookingService _bookingService;
        private readonly IBookingVariantService _bookingVariantService;
        private readonly IMessageDispatcher _dispatcher;
        private readonly ILogger _logger;

        public BookingApprovedHandler(
            IFlightSolutionService flightSolutionReadService,
            IBookingSagaService bookingSagaService,
            IApprovalFlowService approvalFlowService,
            IHandlerFactory handlerFactory,
            IBookingService bookingService,
            IBookingVariantService bookingVariantService,
            IMessageDispatcher dispatcher,
            ILogger logger)
        {
            _flightSolutionReadService = flightSolutionReadService;
            _bookingSagaService = bookingSagaService;
            _approvalFlowService = approvalFlowService;
            _handlerFactory = handlerFactory;
            _bookingService = bookingService;
            _bookingVariantService = bookingVariantService;
            _dispatcher = dispatcher;
            _logger = logger;
        }

        public Task HandleAsync(BookingApproved @event)
        {
            var logger = _logger.ForContext("BookingId", @event.BookingId)
                .ForContext("RequestId", @event.RequestId)
                .ForContext("TenantId", @event.TenantId)
                .ForContext("FlightSolutionId", @event.FlightSolutionId)
                .ForContext("ApprovedBy", @event.ApprovedBy);

            logger.Information("{EventName} Start processing", nameof(BookingApproved));
            
            var messagesToDispatch = new List<IMessage>();
            Reservation[] reservations = null;
            Models.Booking booking = null;
            BookingSaga saga = null;
            FlightSolution approvedSolution = null;
            FlightSolution originalSolution = null;

            return _handlerFactory
                .Create(@event)
                .Validate(async () =>
                {
                    booking = await _bookingService.GetBookingAsync(@event.BookingId);

                    if (booking == null)
                    {
                        throw new NotFoundException("Booking not found");
                    }

                    if (booking.State != BookingState.ApprovalRequired)
                    {
                        throw new ValidationException($"Invalid booking state {booking.State}. Must be {BookingState.ApprovalRequired}");
                    }

                    saga = await _bookingSagaService.GetAsync(@event.BookingId);

                    if (saga == null)
                    {
                        throw new NotFoundException("Booking saga not found");
                    }

                    reservations = (await _bookingService.GetBookingReservationsAsync(@event.BookingId)).ToArray();

                    var hasNonVirtualReservationsToApprove = _approvalFlowService.HasNonVirtualReservationsToApprove(reservations);

                    originalSolution = await _flightSolutionReadService.GetFlightSolutionAsync(booking.FlightSolutionId);
                    
                    if (originalSolution == null)
                    {
                        throw new NotFoundException("Original flight solution was not found.");
                    }

                    // Front End sends FlightSolutionId only in cases where we have an alternative flight
                    var approvedFlightSolutionId = hasNonVirtualReservationsToApprove
                        ? @event.FlightSolutionId
                        : @event.FlightSolutionId ?? booking.FlightSolutionId; 

                    // and try to get the flightSolution (alternative if front end sent the Id)
                    approvedSolution = string.IsNullOrEmpty(approvedFlightSolutionId)
                        ? null
                        : await _flightSolutionReadService.GetFlightSolutionAsync(approvedFlightSolutionId); 

                    // if @event.FlightSolutionId (repriced) is set, then flightSolution can be null
                    // if this is booking flightSolutionId and has reservations to approve, then fail
                    if (approvedSolution == null && !hasNonVirtualReservationsToApprove)
                    {
                        throw new NotFoundException("Flight solution was not found.");
                    }
                })
                .Run(async () =>
                {
                    messagesToDispatch = await _approvalFlowService.ApproveBookingAsync(new BookingApprovedRequest
                    {
                        RequestId = @event.RequestId,
                        TenantId = @event.TenantId,
                        ApprovedBy = @event.ApprovedBy,
                        Booking = booking,
                        BookingSaga = saga,
                        ApprovedSolution = approvedSolution,
                        OriginalSolution = originalSolution,
                        Reservations = reservations
                    });
                })
                .OnSuccess(async () =>
                {
                    foreach (var message in messagesToDispatch)
                        await _dispatcher.DispatchAsync(message);

                    logger.Information("{EventName} Finish processing", nameof(BookingApproved));
                })
                .OnError(async e =>
                {
                    logger.Error(e, "Could not handle {EventName} event", nameof(BookingApproved));
                    
                    await _dispatcher.DispatchAsync(ApproveBookingFailed.InBookingService(@event.BookingId));
                })
                .Lock("Bookings/" + @event.BookingId)
                .ExecuteAsync();
        }
    }
}
