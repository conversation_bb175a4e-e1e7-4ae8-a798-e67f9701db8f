using System.Diagnostics;

namespace CTeleport.Services.Providers.Dto
{
    [DebuggerDisplay("{FullSource}")]
    public class AirProvider
    {
        public string Id { get; set; }
        public string Source { get; set; }
        public string AgencyName { get; set; }
        public string AgencyIata { get; set; }
        public string AgencyPhone { get; set; }
        public string Location { get; set; }
        public string Timezone { get; set; }
        public string Currency { get; set; }
        public int Rating { get; set; }
        public decimal ConsolidatorMarkup { get; set; }
        public decimal CompensationMarkup { get; set; }
        public bool DisableClassdrops { get; set; }
        public bool DisableTicketedClassdrop { get; set; }
        public string Country { get; set; }
        public int GuaranteeDays { get; set; }
        public string DisplayLocation { get; set; }
        public string AgencyGroup { get; set; }
        /// <summary>
        /// Returns combination of source and id, eg. 1G.8WG0
        /// </summary>
        public string FullSource => $"{Source}.{Id}";
        public int? AmountDecimalPlaces { get; set; }
        public string DefaultPaymentMethod { get; set; }
    }
}
