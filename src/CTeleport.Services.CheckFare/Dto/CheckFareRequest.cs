namespace CTeleport.Services.CheckFare.Dto
{
    /// <summary>
    /// Check fare request
    /// </summary>
    public class CheckFareRequest
    {
        /// <summary>
        /// Carrier code, eg. KL
        /// </summary>
        public string Carrier { get; set; }

        /// <summary>
        /// Origin IATA code, eg. RIX
        /// </summary>
        public string Origin { get; set; }

        /// <summary>
        /// Destination IATA code, eg. AMS
        /// </summary>
        public string Destination { get; set; }

        /// <summary>
        /// Passenger type code
        /// </summary>
        public string Ptc { get; set; }

        /// <summary>
        /// Fare trip type
        /// </summary>
        public TripType TripType { get; set; }

        /// <summary>
        /// Currency, eg EUR
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// PCC, eg. 8WG0
        /// </summary>
        public string Pcc { get; set; }

        /// <summary>
        /// 1G or 1A
        /// </summary>
        public string Provider { get; set; }
    }
}