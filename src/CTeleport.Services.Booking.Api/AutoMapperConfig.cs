using AutoMapper;
using CTeleport.Services.CustomFields;

namespace CTeleport.Services.Booking.Api;

public static class AutoMapperConfig
{
    public static IMapper InitializeMapper()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.ShouldMapMethod = _ => false;
            cfg.AddProfile<CommandsMappingProfile>();
            cfg.AddProfile<BookingMapperProfile>();
            cfg.AddProfile<CustomFieldsProfile>();
            cfg.AddProfile<Cancellation.AutoMapperConfig>();
            cfg.AddProfile<ClassDrop.AutoMapperConfig>();
            cfg.AddProfile<BookingSagaMapperProfile>();
        });
            
        return config.CreateMapper();
    }
}