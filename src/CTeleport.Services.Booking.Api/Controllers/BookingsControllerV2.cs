using AutoMapper;
using CTeleport.Api.Framework;
using CTeleport.Api.Models;
using CTeleport.Common.Authorization;
using CTeleport.Common.Authorization.Services;
using CTeleport.Common.Exceptions;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Services.Airlines;
using CTeleport.Services.Booking.Api.Clients;
using CTeleport.Services.Booking.Api.Helpers;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.Helpers;
using CTeleport.Services.PlacesApiClient;
using CTeleport.Services.Search.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using System;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Services.Export;
using Serilog;
using Serilog.Context;

namespace CTeleport.Services.Booking.Api.Controllers
{
    [Route("v2/bookings")]
    public class BookingsControllerV2 : BaseController
    {
        private readonly IBookingService _bookingService;
        private readonly IBookingRepriceService _bookingRepriceService;
        private readonly IBookingPaymentService _bookingPaymentService;
        private readonly IBookingExportService _bookingExportService;
        private readonly ICustomFieldsService _customFieldsService;
        private readonly IPlacesClient _placesClient;
        private readonly IAirlinesClient _airlinesClient;
        private readonly IExcelClient _excelClient;
        private readonly IHttpContextAccessor _contextAccessor;
        private readonly IHidePenaltiesService _hidePenaltiesService;
        private readonly IBookingApprovalItemsService _bookingApprovalItemsService;
        private readonly ILogger _logger;

        public BookingsControllerV2(
            IBookingService bookingService,
            IBookingExportService bookingExportService,
            IBookingRepriceService bookingRepriceService,
            IBookingPaymentService bookingPaymentService,
            ICustomFieldsService customFieldsService,
            IPlacesClient placesClient,
            IAirlinesClient airlinesClient,
            IExcelClient excelClient,
            IMessageDispatcher messageDispatcher,
            IHttpContextAccessor contextAccessor,
            IServiceContext serviceContext,
            IHidePenaltiesService hidePenaltiesService,
            IMapper mapper, ILogger logger, IBookingApprovalItemsService bookingApprovalItemsService)
            : base(messageDispatcher, serviceContext, mapper)
        {
            _bookingService = bookingService;
            _bookingRepriceService = bookingRepriceService;
            _bookingPaymentService = bookingPaymentService;
            _bookingExportService = bookingExportService;
            _customFieldsService = customFieldsService;
            _placesClient = placesClient;
            _airlinesClient = airlinesClient;
            _excelClient = excelClient;
            _contextAccessor = contextAccessor;
            _hidePenaltiesService = hidePenaltiesService;
            _logger = logger;
            _bookingApprovalItemsService = bookingApprovalItemsService;
        }

        // POST /v2/bookings
        [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
        [ApiAuthorize(AuthScopes.BookingsCreate)]
        [HttpPost]
        public async Task<IActionResult> CreateBooking([FromBody] CreateBookingRequestV2 request, CancellationToken cancellationToken)
        {
            using (LogContext.PushProperty("FlightSolutionId", request.FlightSolutionId))
            {
                _logger.Information("Booking creation requested");
                
                var errorMessage = await _customFieldsService.ValidateCustomFieldsInputAsync(ServiceContext.TenantId, request.CustomFields, cancellationToken);
                if (errorMessage.Any())
                {
                    _logger.Information("Some errors on custom fields validation: {CustomFieldsValidationErrors}, request will be processed anyway", errorMessage);
                    // TODO: Return HTTP 400
                }

                return await For(Mapper.Map<CreateBooking>(request))
                    .SetResourceId(x => x.BookingId)
                    .OnSuccessAccepted("v2/bookings/{0}")
                    .DispatchAsync();
            }
        }

        // PUT /v2/bookings/{id}/cancel
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/cancel")]
        public async Task<IActionResult> CancelBooking([FromBody] CancelBookingRequestV2 request)
            => await For<CancelBooking>(Mapper.Map<CancelBooking>(request))
                .WithResourceId(request.BookingId)
                .OnSuccessAccepted($"v2/bookings/{request.BookingId}")
                .DispatchAsync();

        // PUT /v2/bookings/{id}/issue_ticket
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/issue_ticket")]
        public async Task<IActionResult> IssueTicket([FromBody] IssueTicketRequestV2 request)
            => await For<IssueTicket>(Mapper.Map<IssueTicket>(request))
                .WithResourceId(request.BookingId)
                .OnSuccessAccepted($"v2/bookings/{request.BookingId}")
                .DispatchAsync();

        // PUT /v2/bookings/{id}/reset_refund_pending
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/reset_refund_pending")]
        public async Task<IActionResult> ResetRefundPending([FromBody] ResetRefundPendingRequest request)
            => await For(Mapper.Map<ResetRefundPending>(request))
                .WithResourceId(request.BookingId)
                .OnSuccessAccepted($"v2/bookings/{request.BookingId}")
                .DispatchAsync();

        // PUT /v2/bookings/{id}/passenger
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/passenger")]
        public async Task<IActionResult> UpdatePassengerDetails([FromBody] UpdatePassengerDetailsRequest updatePassengerRequest)
            => await For(Mapper.Map<UpdatePassengerDetails>(updatePassengerRequest))
                .WithResourceId(updatePassengerRequest.BookingId)
                .OnSuccessAccepted($"v2/bookings/{updatePassengerRequest.BookingId}")
                .DispatchAsync();

        // PUT /v2/bookings/{id}/vessel
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/vessel")]
        public async Task<IActionResult> UpdateVessel([FromBody] UpdateVesselRequest updateVesselRequest)
            => await For(Mapper.Map<UpdateVessel>(updateVesselRequest))
                .WithResourceId(updateVesselRequest.BookingId)
                .OnSuccessAccepted($"v2/bookings/{updateVesselRequest.BookingId}")
                .DispatchAsync();

        // GET /v2/bookings/<id>
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet("{id}")]
        public async Task<ActionResult<BaseBookingDto>> Get(string id)
        {
            var result = await _bookingService.GetBaseBookingAsync(id);

            if (result == null)
                return NotFound();
            else
                return Ok(result);
        }

        // GET /v2/bookings/<id>/details
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet("{id}/details")]
        public async Task<ActionResult<CompleteBookingDto>> GetDetails(string id, bool requestValidation = false)
        {
            var result = await _bookingService.GetCompleteBookingAsync(id);

            if (result == null)
                return NotFound();

            if (await _hidePenaltiesService.ShouldHidePenaltiesAsync())
            {
                _hidePenaltiesService.RemovePenalties(result);
            }
            
            if (requestValidation)
            {
                result.RepricingRequired = 
                    await _bookingApprovalItemsService.GetApprovalQueueItemRepricingRequiredAsync(result.Id);    
            }

            return Ok(result);
        }

        // GET /v2/bookings/<id>/payment/details
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet("{id}/payment/details")]
        public async Task<ActionResult<BookingPaymentDetailsDto>> GetPaymentDetails(string id)
        {
            try
            {
                return Ok(await _bookingPaymentService.GetPaymentDetailsAsync(id));
            }
            catch (NotFoundException)
            {
                return NotFound();
            }
        }
        // PUT /v2/bookings/{id}/payment/confirm
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/payment/confirm")]
        [Obsolete("We don't need this anymore. All data are set by events. " +
                  "It is kept for backward compatibility with frontend. " +
                  "Consider changes on frontend to remove the endpoint too")]
        public async Task<IActionResult> ConfirmPayment([FromBody] ConfirmBookingPaymentRequest request)
        {
            var payment = await _bookingPaymentService.GetPaymentDetailsAsync(request.BookingId);
            return new OperationAcceptedResult(
                operationId: payment.PaymentId, 
                resourceId: request.BookingId);
        }

        // GET /v2/bookings/<id>/public
        [AllowAnonymous]
        [HttpGet("{id}/public")]
        public async Task<IActionResult> GetPublic(string id)
        {
            var booking = await _bookingService.GetPublicBookingAsync(id);
            if (booking == null)
                return NotFound();

            var airportCodes = booking.LegSegments.SelectMany(l => l.Select(s => s.Origin).Union(l.Select(s => s.Destination))).Distinct();
            var airportsTask = _placesClient.GetAirportsByCodeAsync(airportCodes.ToArray());

            var airlineCodes = booking.LegSegments
                .SelectMany(l => l.Select(s => s.Carrier).Union(l.Select(s => s.Operator)))
                .Union(booking.Reservations.Select(r => r.PlatingCarrier))
                .Distinct()
                .Where(a => a != null && a.Length == 2);

            var airlinesTask = _airlinesClient.GetAirlinesByCodeAsync(airlineCodes);
            
            return Ok(new PublicBookingResponseV2(booking, await airportsTask, await airlinesTask));
        }

        // GET /v2/bookings/passenger
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet("passenger")]
        public async Task<IActionResult> Get(string first_name, string last_name, string departure, int range = 2)
        {
            if (string.IsNullOrWhiteSpace(first_name) && string.IsNullOrWhiteSpace(last_name))
            {
                return BadRequest($"{nameof(first_name)} or {nameof(last_name)} have to be specified");
            }

            if (string.IsNullOrWhiteSpace(departure))
            {
                return BadRequest($"{nameof(departure)} has to be specified");
            }

            return Ok(await _bookingService.GetBookingsForPassengerAsync(first_name, last_name, departure, range));
        }

        // GET /v2/bookings
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet]
        public async Task<IActionResult> GetBookings(string search_id, int? months = 6)
        {
            if (!String.IsNullOrEmpty(search_id))
            {
                return Ok(await _bookingService.GetBookingsForSearchIdAsync(search_id));
            }

            var startFrom = DateTimeExtensions.GetStartFromDate(months);
            return Ok(await _bookingService.GetBookingsScopedAsync(startFrom));
        }

        // GET /v2/bookings/export
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet("export")]
        public async Task<IActionResult> ExportBookings(int? months)
        {
            var startFrom = DateTimeExtensions.GetStartFromDate(months);
            
            return await ExportInternal(startFrom, null);
        }

        // GET /v2/bookings/export
        [ApiAuthorize(AuthScopes.BookingsRead)]
        [HttpGet("export-by-daterange")]
        public async Task<IActionResult> ExportBookings([FromQuery] int from, [FromQuery] int? to)
        {
            var fromDate = from.ToDateTimeUtc();
            var toDate = to?.ToDateTimeUtc();
            
            return await ExportInternal(fromDate, toDate);
        }

        private async Task<IActionResult> ExportInternal(DateTime? fromDate, DateTime? toDate)
        {
            var (bookings, skipColumns) = await _bookingExportService.GetBookingsForExportAsync(fromDate, toDate);
            var data = ExportHelper.BuildExcelWithCustomFieldsRequest("bookings.xlsx", "Bookings", bookings, skipColumns);

            var stream = await _excelClient.GenerateExcelAsync(data);

            Response.Headers.Add("Content-Disposition", "attachment; filename=bookings.xlsx");
            return new FileStreamResult(stream,
                new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        }

        // PUT /v2/bookings/{id}/comment
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpPut("{id}/comment")]
        public async Task<IActionResult> CommentBooking([FromBody] SetBookingCommentRequest request)
            => await For(Mapper.Map<SetBookingComment>(request))
                .WithResourceId(request.BookingId)
                .OnSuccessAccepted($"v2/bookings/{request.BookingId}")
                .DispatchAsync();

        // GET /v2/bookings/<id>/cancel_quote
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpGet("{id}/cancel_quote")]
        public async Task<IActionResult> GetCancelQuote(string id)
        {
            try
            {
                return Ok(await _bookingService.GetRefundQuoteAsync(id));
            }
            catch (ValidationException ex)
            {
                return Problem(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
        }

        // GET /v2/bookings/{id}/reprice
        [ApiAuthorize(AuthScopes.BookingsManage)]
        [HttpGet("{id}/reprice")]
        public async Task<ActionResult<FlightSolutionDetails>> GetAlternativeFlightSolution(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return BadRequest();
            }

            try
            {
                var result = await _bookingRepriceService.RepriceBooking(_contextAccessor.GetAuthToken(), id);
                
                return Ok(result);
            }
            catch (NotFoundException)
            {
                return NotFound();
            }
        }
    }
}
