using CTeleport.Common.Authorization;
using CTeleport.Common.Authorization.Extensions;
using CTeleport.Common.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CTeleport.Services.Booking.Api.Extensions
{
    public static class AuthorizationExtensions
    {
        public static IApplicationBuilder UseAuths(this IApplicationBuilder appBuilder)
        {
            appBuilder
                .UseAuthentication()
                .UseAuthorization();

            return appBuilder;
        }

        public static IServiceCollection AddAuths(this IServiceCollection services, IConfiguration config)
        {
            services
                .AddJwtAuthentication(config.GetSettings<AuthConfig>("Security:Auth0"))
                .AddApiAuthorization();

            return services;
        }
    }
}