using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using System.Net.Http.Headers;

namespace CTeleport.Services.Booking.Api.Extensions
{
    public static class HttpContextExtensions
    {
        public static string GetAuthToken(this HttpContext context, string authScheme = JwtBearerDefaults.AuthenticationScheme)
        {
            var authorization = context?.Request.Headers[HeaderNames.Authorization];
            if (authorization.HasValue && AuthenticationHeaderValue.TryParse(authorization, out var headerValue))
            {
                return headerValue.Scheme.Equals(authScheme) ? headerValue.Parameter : null;
            }
            return null;
        }
    }
}