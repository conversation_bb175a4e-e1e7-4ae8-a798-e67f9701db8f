using CTeleport.Services.Booking.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CTeleport.Services.Booking.Api.Extensions
{
    public static class MongoExtensions
    {
        public static IServiceCollection AddMongo(this IServiceCollection services, IConfiguration config)
        {
            Common.Mongo.MongoExtensions.AddMongo(services, config);
            DatabaseInitializer.RegisterClassMaps();

            return services;
        }
    }
}