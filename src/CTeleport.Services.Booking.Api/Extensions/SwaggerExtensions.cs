using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using CTeleport.Common.Authorization;
using CTeleport.Services.Booking.Api.Helpers;
using CTeleport.Services.Booking.Api.Infrastructure;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Interfaces;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerUI;

namespace CTeleport.Services.Booking.Api.Extensions
{
    public static class SwaggerExtensions
    {
        public static WebApplication UseSwagger(this WebApplication app)
        {
            SwaggerBuilderExtensions.UseSwagger(app);
            app.UseSwaggerUI(c =>
            {
                c.RoutePrefix = "";
                c.SwaggerEndpoint("/swagger/v1/swagger.json", $"{AssemblyHelper.GetSolutionName()} API - v1");

                string clientId = app.Configuration["Security:Auth0:ClientId"]?.Split(',').FirstOrDefault();
                if (clientId != null)
                {
                    c.OAuthClientId(clientId);
                }
                c.DisplayRequestDuration();
                c.DocExpansion(DocExpansion.None);
            });

            return app;
        }

        public static IServiceCollection AddSwagger(this IServiceCollection services, IConfiguration config)
        {
            services.AddSwaggerGen(c =>
            {
                c.DescribeAllParametersInCamelCase();
                c.CustomSchemaIds(x => x.FullName);
                c.SwaggerDoc("v1", new OpenApiInfo { Title = $"{AssemblyHelper.GetSolutionName()} API - v1", Version = "v1" });
                c.EnableAnnotations();

                var dir = new DirectoryInfo(AppDomain.CurrentDomain.BaseDirectory);
                foreach (var fileInfo in dir.EnumerateFiles("*.xml"))
                {
                    c.IncludeXmlComments(fileInfo.FullName);
                }

                c.OperationFilter<CustomEndpointsFilter>();

            // TODO [sg]: this is not really robust solution, come up with a better solution
            var isAuthRegistered = services.FirstOrDefault(x => x.ImplementationType == typeof(ScopedAuthorizationPolicyProvider));
                if (isAuthRegistered != null)
                {
                    c.AddSecurityDefinition("oauth2",
                        new OpenApiSecurityScheme
                        {
                            Type = SecuritySchemeType.OAuth2,
                            Extensions = new Dictionary<string, IOpenApiExtension>
                            {
                            { "x-tokenName", new OpenApiString("id_token") }
                            },
                            Flows = new OpenApiOAuthFlows
                            {
                                Implicit = new OpenApiOAuthFlow
                                {
                                    AuthorizationUrl = new Uri($"https://{config["Security:Auth0:Domain"]}/authorize"),
                                    Scopes = new Dictionary<string, string>
                                    {
                                    { "offline_access", "OAuth 2.0 Refresh Token be issued" },
                                    { "openid", "Making an OpenID Connect request" },
                                    { "profile", "Access to the End-User's Default Profile Claims" }
                                    }
                                }
                            }
                        });

                    c.AddSecurityRequirement(
                        new OpenApiSecurityRequirement
                        {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "oauth2" }
                            },
                            Array.Empty<string>()
                        }
                        });
                }
            });

            services.AddSwaggerGenNewtonsoftSupport();

            return services;
        }
    }
}