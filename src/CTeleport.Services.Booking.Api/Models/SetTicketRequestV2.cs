using System;
using System.ComponentModel.DataAnnotations;
using CTeleport.Services.Search.Shared.Attributes;

namespace CTeleport.Api.Models
{
    /// <summary>
    /// Set ticket request model
    /// </summary>
    public class SetTicketRequestV2 : BaseModifyReservationRequestV2
    {
        /// <summary>
        /// Ticket number
        /// </summary>
        [Required]
        public string Number { get; set; }

        /// <summary>
        /// Ticket issue date in yyyy-mm-dd format
        /// </summary>
        [Required]
        [StringDate]
        public string IssueDate { get; set; }

        /// <summary>
        /// Net ticket price as it is charged by airline
        /// NOTE: left boundary for range validation is set to 1.0 to prevent registering
        /// tickets with 0.00 net price, which should never be a case.
        /// </summary>
        [Required]
        [Range(1.0, Double.MaxValue)]
        public decimal? Price { get; set; }
        
        /// <summary>
        /// Funding source
        /// </summary>
        public string FundingSource { get; set; }
    }
}
