using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.Airlines.Models;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Api.Models;

public class ItineraryReceiptResponse
{
    public ItineraryReceiptResponse(ItineraryReceiptBookingDto booking, IEnumerable<Airport> airports, IEnumerable<Airline> airlines)
    {
        Booking = booking;
        Airports = airports.ToDictionary(a => a.IATA);
        Airlines = airlines.ToDictionary(a => a.IATA);
    }

    /// <summary>
    /// Booking details
    /// </summary>
    public ItineraryReceiptBookingDto Booking { get; set; }

    /// <summary>
    /// Airports dictionary with airport IATA code as a key
    /// </summary>
    public IDictionary<string, Airport> Airports { get; set; }
        
    /// <summary>
    /// Airlines dictionary with airport IATA code as a key
    /// </summary>
    public IDictionary<string, Airline> Airlines { get; set; }
}