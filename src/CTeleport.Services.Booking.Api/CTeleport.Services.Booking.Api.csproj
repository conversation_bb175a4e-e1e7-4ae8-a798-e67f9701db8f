<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTeleport.Services.AirGateway\CTeleport.Services.AirGateway.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Airlines\CTeleport.Services.Airlines.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Amadeus\CTeleport.Services.Amadeus.csproj" />
    <ProjectReference Include="..\CTeleport.Services.ApprovalQueueClient\CTeleport.Services.ApprovalQueueClient.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Billing\CTeleport.Services.Billing.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.AirGateway\CTeleport.Services.Booking.AirGateway.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Amadeus\CTeleport.Services.Booking.Amadeus.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Infrastructure\CTeleport.Services.Booking.Infrastructure.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Travelport\CTeleport.Services.Booking.Travelport.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Cancellation\CTeleport.Services.Cancellation.csproj" />
    <ProjectReference Include="..\CTeleport.Services.CheckFare.Amadeus\CTeleport.Services.CheckFare.Amadeus.csproj" />
    <ProjectReference Include="..\CTeleport.Services.CheckFare.Travelport\CTeleport.Services.CheckFare.Travelport.csproj" />
    <ProjectReference Include="..\CTeleport.Services.ClassDrop\CTeleport.Services.ClassDrop.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Co2Emissions\CTeleport.Services.Co2Emissions.csproj" />
    <ProjectReference Include="..\CTeleport.Services.ExtraServiceManagement\CTeleport.Services.ExtraServiceManagement.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FareTerms\CTeleport.Services.FareTerms.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FlightStatus.AirGateway\CTeleport.Services.FlightStatus.AirGateway.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FlightStatus.Travelport\CTeleport.Services.FlightStatus.Travelport.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FlightStatus\CTeleport.Services.FlightStatus.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FlightStatus.Amadeus\CTeleport.Services.FlightStatus.Amadeus.csproj" />
    <ProjectReference Include="..\CTeleport.Services.FlightStatus.Travelfusion\CTeleport.Services.FlightStatus.Travelfusion.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking\CTeleport.Services.Booking.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Excel.Shared\CTeleport.Services.Excel.Shared.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Providers\CTeleport.Services.Providers.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Settings\CTeleport.Services.Settings.csproj" />
    <ProjectReference Include="..\CTeleport.Services.TravelportJson\CTeleport.Services.TravelportJson.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Travelport\CTeleport.Services.Travelport.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Settings\CTeleport.Services.Settings.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Travelfusion\CTeleport.Services.Travelfusion.csproj" />
    <ProjectReference Include="..\CTeleport.Services.Booking.Travelfusion\CTeleport.Services.Booking.Travelfusion.csproj" />
    <ProjectReference Include="..\CTeleport.Services.UsersClient\CTeleport.Services.UsersClient.csproj" />
    <ProjectReference Include="..\CTeleport.Services.VoidCalc\CTeleport.Services.VoidCalc.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CTeleport.Authorization" Version="1.1.200" />
    <PackageReference Include="CTeleport.Common.V2.Redis" Version="1.1.39" Aliases="RedisV2" />
    <PackageReference Include="CTeleport.FareRules.Shared" Version="1.1.183" />
    <PackageReference Include="CTeleport.HealthChecks.Core" Version="2024.11.25.27" />
    <PackageReference Include="CTeleport.Infrastructure.Metrics" Version="2025.3.28.62" />
    <PackageReference Include="CTeleport.Infrastructure.Http" Version="2025.3.28.62" />
    <PackageReference Include="CTeleport.Infrastructure.RawRabbit" Version="2025.3.28.62" />
    <PackageReference Include="CTeleport.Infrastructure.Serilog" Version="2025.3.28.62" />
    <PackageReference Include="CTeleport.Messages.Abstractions" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Services.Cancellation.Communication.Http" Version="2025.3.30.150" />
    <PackageReference Include="CTeleport.Services.Price.Shared" Version="1.1.101" />
    <PackageReference Include="CTeleport.Messages" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Services.Search.Shared" Version="1.1.578" />
    <PackageReference Include="CTeleport.Services.ExtraServiceManagement.Shared" Version="2025.4.29.1173" />
    <PackageReference Include="CTeleport.Common.Logging" Version="1.1.112" />
    <PackageReference Include="CTeleport.Common.Apm" Version="1.1.50" />
    <PackageReference Include="CTeleport.Common.Messaging" Version="1.1.293" />
    <PackageReference Include="CTeleport.Services.TravelPolicies.Shared" Version="1.1.59" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.7.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.11" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.1" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="7.2.0" />
    <PackageReference Include="Microsoft.FeatureManagement" Version="3.1.1" />
    <PackageReference Include="ProxyKit" Version="2.3.4" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.2.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.2.3" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>
</Project>