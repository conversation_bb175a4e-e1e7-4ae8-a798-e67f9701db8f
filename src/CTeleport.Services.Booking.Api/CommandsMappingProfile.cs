using System;
using AutoMapper;
using CTeleport.Api.Models;
using CTeleport.Messages.Commands.Changes;
using CTeleport.Messages.Commands.Models;
using CTeleport.Messages.Commands.Tickets;
using CTeleport.Messages.Models.Changes;
using CTeleport.Money;
using CTeleport.Services.Booking.Api.Helpers;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using Currency = CTeleport.Messages.Models.Currency;
using FrequentFlyerNumber = CTeleport.Api.Models.FrequentFlyerNumber;
using InvoiceRefund = CTeleport.Api.Models.InvoiceRefund;
using Site = CTeleport.Api.Models.Site;
using Ticket = CTeleport.Messages.Commands.Models.Ticket;

namespace CTeleport.Services.Booking.Api
{
    public class CommandsMappingProfile : Profile
    {
        public CommandsMappingProfile()
        {
            CreateMap<CreateBookingRequestV2, Messages.Commands.Bookings.CreateBooking>()
                    .ForMember(dest => dest.BookingId, opt => opt.Ignore())
                    .ForMember(dest => dest.OriginalBookingId, opt => opt.Ignore())
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore())
                    .AfterMap((src, dest) =>
                    {
                        if (dest.Metadata == null)
                        {
                            dest.Metadata = new BookingMetadata();
                        }
                        dest.Metadata.CustomFields = src.CustomFields;
                    });

            CreateMap<Site, CTeleport.Messages.Commands.Models.Site>().ReverseMap();
            
            CreateMap<ScreenResolution, Messages.Commands.Bookings.ScreenResolution>();
            
                CreateMap<MetadataInputModel, BookingMetadata>()
                    .ForMember(dest => dest.CustomFields, opt => opt.Ignore());

                CreateMap<AncillaryModel, CTeleport.Messages.Commands.Models.Ancillary>();

                CreateMap<MetadataInputModel, Metadata>()
                    .ForMember(dest => dest.CustomFields, opt => opt.Ignore());
                
                CreateMap<CancelBookingRequestV2, Messages.Commands.Bookings.CancelBooking>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<IssueTicketRequestV2, IssueTicket>()
                    .ForMember(dest => dest.ReservationId, opt => opt.Ignore())
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<UpdatePassengerDetailsRequest, CTeleport.Messages.Commands.Bookings.UpdatePassengerDetails>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<CancelReservationRequestV2, Messages.Commands.Reservations.CancelReservation>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SetReservationAsCancelledRequest, Messages.Commands.Reservations.SetReservationAsCancelled>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SyncReservationRequest, Messages.Commands.Reservations.SyncReservation>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<IssueTicketForReservationRequestV2, IssueTicketForReservation>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SetTicketRequestV2, SetTicket>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<VoidTicketRequestV2, VoidTicket>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<UpdateVesselRequest, CTeleport.Messages.Commands.Bookings.UpdateVessel>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<RefundTicketRequestV2, RefundTicket>()
                    .ForMember(dest => dest.RefundFee, opt => opt.MapFrom(src => src.Fee.Value))
                    .ForMember(dest => dest.RefundFeeCurrency, opt => opt.MapFrom(src => src.Currency))
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore())
                    .ForMember(dest => dest.AutoRefund, opt => opt.MapFrom(src => false))
                    .ForMember(dest => dest.IsPredicted, opt => opt.MapFrom(src => false));
                
                CreateMap<ResetRefundPendingRequest, Messages.Commands.Bookings.ResetRefundPending>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SetTicketAsRefundedRequestV2, SetTicketAsRefunded>()
                    .ForMember(dest => dest.UsedAmount, opt => opt.MapFrom(src => src.UsedAmount ?? 0M))
                    .ForMember(dest => dest.RefundAmount, opt => opt.MapFrom(src => src.Amount.Value))
                    .ForMember(dest => dest.RefundFee, opt => opt.MapFrom(src => src.Fee.Value))
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SetTicketAsNonRefundableRequest, SetTicketAsNonRefundable>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<RestoreReservationRequest, Messages.Commands.Reservations.RestoreReservation>()
                    .ForMember(dest => dest.ReservationId, opt => opt.MapFrom(src => src.ReservationId))
                    .ForMember(dest => dest.AllowHigherPrice, opt => opt.MapFrom(src => src.AllowHigherPrice))
                    .ForMember(dest => dest.PriceDiffUpTo, opt => opt.MapFrom(src => src.PriceDiffUpTo))
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<AddCommentRequest, Messages.Commands.Bookings.AddComment>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SetBookingCommentRequest, Messages.Commands.Bookings.SetBookingComment>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<ConfirmBookingPaymentRequest, Messages.Commands.Bookings.ConfirmBookingPayment>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<Metadata, BookingMetadata>()
                    .AfterMap((b, bm) => TrimTextData(bm));

                CreateMap<Models.PassengerDetails, Passenger>()
                    .AfterMap((pd, p) => TrimTextData(p));

                CreateMap<MutablePassengerDetails, PassengerUpdate>();

                CreateMap<Messages.Commands.Enums.BookingState, BookingState>().ConvertUsing((value, _) =>
                    value switch
                    {
                        Messages.Commands.Enums.BookingState.Confirmed => BookingState.Confirmed,
                        Messages.Commands.Enums.BookingState.Cancelled => BookingState.Cancelled,
                        Messages.Commands.Enums.BookingState.Issued => BookingState.Issued,
                        Messages.Commands.Enums.BookingState.Used => BookingState.Used,
                        Messages.Commands.Enums.BookingState.ChangePending => BookingState.ChangePending,
                        Messages.Commands.Enums.BookingState.RefundPending => BookingState.RefundPending,
                        Messages.Commands.Enums.BookingState.ApprovalRequired => BookingState.ApprovalRequired,
                        Messages.Commands.Enums.BookingState.Declined => BookingState.Declined,
                        Messages.Commands.Enums.BookingState.PaymentRequired => BookingState.PaymentRequired,
                        _ => throw new ArgumentOutOfRangeException(nameof(value), value, "Mapping is not provided")
                    }
                );
                
                CreateMap<InvoiceRefund, Messages.Commands.Models.InvoiceRefund>();

                CreateMap<ClassDropIsAllowedRequest, Messages.Commands.Reservations.SetClassDropIsAllowed>()
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<SetProhibitAutoRefundRequest, SetProhibitAutoRefund>()
                    .ForMember(dest => dest.Number, opt => opt.MapFrom(src => src.TicketNumber))
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                CreateMap<TaxDetailDto, Shared.Models.TaxDetail>();

                CreateMap<ReissueTicketRequest, Ticket>()
                    .ForMember(x => x.IssueDate, opt => opt.MapFrom(y => y.IssueDate))
                    .ForMember(x => x.Currency, opt => opt.MapFrom(y => y.Currency))
                    .ForMember(x => x.TaxDetails, opt => opt.MapFrom(y => y.TaxDetails))
                    .ForMember(x => x.FareCalc, opt => opt.MapFrom(y => y.FareCalc))
                    .ForMember(x => x.TotalPrice, opt => opt.MapFrom(y => y.Price))
                    .ForMember(x => x.Taxes, opt => opt.MapFrom(y => y.Taxes))
                    .ForMember(x => x.Number, opt => opt.Ignore())
                    .ForMember(x => x.Status, opt => opt.Ignore())
                    .ForMember(x => x.Coupons, opt => opt.Ignore());

                CreateMap<TaxDetailDto, TaxDetails>();

                CreateMap<ReissueTicketRequest, SyncChangedTicket>()
                    .ForMember(x => x.NewTicketNumber, opt => opt.MapFrom(y => y.NewNumber))
                    .ForMember(x => x.OldTicketNumber, opt => opt.MapFrom(y => y.OldNumber))
                    .ForMember(x => x.NewTicket, opt => opt.MapFrom(s => s))
                    .ForMember(x=> x.EmdDetails, opt=>opt.MapFrom<EmdResolver>())
                    .ForMember(dest => dest.BookingId, opt => opt.Ignore())
                    .ForMember(dest => dest.Request, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore())
                    .ForMember(dest => dest.TotalAmountToCollect, opt => opt.Ignore());
                


                CreateMap<FrequentFlyerNumber, Messages.Commands.Models.FrequentFlyerNumber>()
                    .ForMember(x => x.Carrier, opt => opt.Ignore());
        }

        private static void TrimTextData(Passenger passenger)
        {
            passenger.LastName = passenger.LastName.CleanSpaces();
            passenger.FirstName = passenger.FirstName.CleanSpaces();
            passenger.Email = passenger.Email.CleanSpaces();
            passenger.Phone = passenger.Phone.CleanSpaces();
        }

        private static void TrimTextData(BookingMetadata metadata)
        {
            metadata.VesselName = metadata.VesselName.CleanSpaces();
        }

        private class EmdResolver : IValueResolver<ReissueTicketRequest, object, Emd>
        {
            public Emd Resolve(ReissueTicketRequest source, object destination, Emd destMember, ResolutionContext context)
            {
                if (source.EmdDetails != null)
                {
                    return new Emd
                    {
                        Number = source.EmdDetails.Number,
                        Description = source.EmdDetails.Description,
                        Fee = new Messages.Models.Money
                        {
                            Amount = source.EmdDetails.Fee,
                            Currency = new Currency { IsoCode = source.EmdDetails.FeeCurrency }
                        }
                    };
                }

                return null;
            }
        }
    }
}