using AutoMapper;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.ClassDrop.Communication.Contracts.Messages;
using CTeleport.Services.ClassDrop.Communication.Http.Core;
using CTeleport.Services.ClassDrop.Extensions;
using CTeleport.Services.ClassDrop.Models.Dto;
using CTeleport.Services.ClassDrop.Models.Dto.Reservation;
using Serilog;

namespace CTeleport.Services.ClassDrop.Operations.RequestHandlers;

public class GetReservationRequestHandler(
    IBookingService bookingService,
    IMapper mapper,
    ILogger logger) : IClassDropServiceRequestHandler<GetReservation.ById.Request, GetReservation.ById.Response>
{
    public async Task<GetReservation.ById.Response> HandleAsync(GetReservation.ById.Request request)
    {
        var ctx = logger
            .ForContext("ReservationId", request.ReservationId);
        
        try
        {
            var reservation = await bookingService.GetReservationAsync(request.ReservationId);
            if (reservation == null)
            {
                var notFoundResult = Result<ReservationDto>.Fail("Reservation not found");
                return GetReservation.ById.Response.FromResult(notFoundResult);
            }

            var resultReservation = reservation.MapTo<ReservationDto>(mapper);
            if (resultReservation == null)
            {
                var mapFailedResult = Result<ReservationDto>.Fail("Failed to map reservation to DTO");
                return GetReservation.ById.Response.FromResult(mapFailedResult);
            }
            
            var result = Result<ReservationDto>.Ok(resultReservation);
            return GetReservation.ById.Response.FromResult(result);
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(GetReservation.ById)}");
            
            var result = Result<ReservationDto>.Fail(ex.Message);
            return GetReservation.ById.Response.FromResult(result);
        }
    }
}