using AutoMapper;
using CTeleport.Services.ClassDrop.Communication.Contracts.Messages;
using CTeleport.Services.ClassDrop.Communication.Http.Core;
using CTeleport.Services.ClassDrop.Exceptions;
using CTeleport.Services.ClassDrop.Extensions;
using CTeleport.Services.ClassDrop.Models.Dto;
using CTeleport.Services.Providers.Client;
using Serilog;

namespace CTeleport.Services.ClassDrop.Operations.RequestHandlers;

public class GetProvidersRequestHandler(
    IProvidersClient providersClient,
    IMapper mapper,
    ILogger logger) : IClassDropServiceRequestHandler<GetProviders.Request, GetProviders.Response>
{
    public async Task<GetProviders.Response> HandleAsync(GetProviders.Request request)
    {
        logger.Information("Getting providers {Request}", request);
        
        try
        {
            var providers = (await providersClient.GetProvidersAsync())?.ToArray();
            logger.Information("Received providers: {Providers}", providers);
            
            var providersDto = providers?.MapTo<AirProvider[]>(mapper);
            if (providersDto == null)
                throw new MappingException($"Failed to map {nameof(AirProvider)} array to result to DTO");
            
            return GetProviders.Response.FromResult(Result<AirProvider[]>.Ok(providersDto));
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error while getting Air providers");
            
            var result = Result<AirProvider[]>.Fail(ex.Message);
            return GetProviders.Response.FromResult(result);
        }
    }
}