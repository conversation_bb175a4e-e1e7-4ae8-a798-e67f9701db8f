using AutoMapper;
using CTeleport.Services.Booking.Cache;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Extensions;
using CTeleport.Services.Cancellation.Models.Dto;
using Serilog;
using TicketRefundCache = CTeleport.Services.Cancellation.Communication.Contracts.Messages.TicketRefundCache;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class TicketRefundCacheSetRequestHandler(
    ITicketRefundCache cache,
    IMapper mapper,
    ILogger logger) : ICancellationServiceRequestHandler<TicketRefundCache.Set.Request, TicketRefundCache.Set.Response>
{
    public Task<TicketRefundCache.Set.Response> HandleAsync(TicketRefundCache.Set.Request request)
    {
        var ctx = logger
            .ForContext("Request", request, true);
        
        try
        {
            var cacheEntry = request.Entity.MapTo<TicketRefundCacheEntry>(mapper);
            cache.Set(cacheEntry, request.Id);
            
            var response = TicketRefundCache.Set.Response.FromResult(Result.Ok());
            return Task.FromResult(response);
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(TicketRefundCache.Set)}");
            
            var result = Result.Fail(ex.Message);
            var response = TicketRefundCache.Set.Response.FromResult(result);
            return Task.FromResult(response);
        }
    }
}