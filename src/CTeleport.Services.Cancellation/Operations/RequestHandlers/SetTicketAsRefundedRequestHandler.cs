using AutoMapper;
using CTeleport.Services.Booking.Dto;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Extensions;
using CTeleport.Services.Cancellation.Models.Dto;
using CTeleport.Services.Cancellation.Models.Dto.Reservation;
using Serilog;
using PromisedRefund = CTeleport.Services.Booking.Models.PromisedRefund;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class SetTicketAsRefundedRequestHandler(
    IBookingService bookingService,
    IReservationRefundService reservationRefundService, 
    IMapper mapper,
    ILogger logger) : ICancellationServiceRequestHandler<SetTicketAsRefunded.Request, SetTicketAsRefunded.Response>
{
    public async Task<SetTicketAsRefunded.Response> HandleAsync(SetTicketAsRefunded.Request request)
    {
        var ctx = logger
            .ForContext(nameof(request.Command.ReservationId), request.Command.ReservationId)
            .ForContext(nameof(request.Command), request.Command, destructureObjects: true)
            .ForContext(nameof(request.RefundedAt), request.RefundedAt);
        
        try
        {
            var reservation = await bookingService.GetReservationAsync(request.Command.ReservationId);

            var refund = new TicketRefundDto
            {
                Amount = request.Command.RefundAmount,
                Fee = request.Command.RefundFee,
                DeductedTaxes = request.Command.DeductedTaxes,
                InvoiceRefund = request.InvoiceRefund.MapTo<InvoiceRefund>(mapper),
                PromisedRefund = request.PromisedRefund.MapTo<PromisedRefund>(mapper),
                NotRefundedTaxes = request.Command.DeductedTaxes.ToDictionary(x => x, _ => decimal.Zero),
                Remarks = request.Command.Remarks
            };
                
            await reservationRefundService.SetTicketAsRefundedAsync(reservation, request.Command.Number, refund, request.RefundedAt);
            
            return SetTicketAsRefunded.Response.FromResult(Result.Ok());
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(SetTicketAsRefunded)}");
            return SetTicketAsRefunded.Response.FromResult(Result.Fail(ex.Message));
        }
    }
}