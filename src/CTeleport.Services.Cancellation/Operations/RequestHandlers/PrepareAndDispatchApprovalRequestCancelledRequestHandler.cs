using CTeleport.Common.Helpers;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Events.ApprovalFlow;
using CTeleport.Messages.Models.ApprovalFlow.Enums;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Models.Dto;
using Serilog;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class PrepareAndDispatchApprovalRequestCancelledRequestHandler(
    IBookingService bookingService,
    IMessageDispatcher dispatcher,
    ILogger logger) 
    : ICancellationServiceRequestHandler<PrepareAndDispatchApprovalRequestCancelled.Request, PrepareAndDispatchApprovalRequestCancelled.Response>
{
    public async Task<PrepareAndDispatchApprovalRequestCancelled.Response> HandleAsync(PrepareAndDispatchApprovalRequestCancelled.Request request)
    {
        var ctx = logger
            .ForContext(nameof(request.BookingId), request.BookingId);
        
        try
        {
            var booking = await bookingService.GetBookingAsync(request.BookingId);
            if (booking == null)
            {
                return PrepareAndDispatchApprovalRequestCancelled.Response.FromResult(
                    Result.Fail($"Booking {request.BookingId} not found"));
            }

            await dispatcher.DispatchAsync(new ApprovalRequestCancelled
            {
                RequestId = Id.New(),
                ReferenceId = booking.Id,
                TenantId = booking.TenantId,
                ApplicableFor = ApprovalApplicableFor.Flight,
                CreatedBy = new Messages.Models.User
                {
                    Id = booking.CreatedBy.Id,
                    Name = booking.CreatedBy.Name,
                    Email = booking.CreatedBy.Email,
                    TenantId = booking.TenantId
                },
                ReferenceEntity = ApprovalBundleReferenceHelper.BuildApprovalBundleReference(booking)
            });

            return PrepareAndDispatchApprovalRequestCancelled.Response.FromResult(Result.Ok());
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(PrepareAndDispatchApprovalRequestCancelled)}");
            return PrepareAndDispatchApprovalRequestCancelled.Response.FromResult(Result.Fail(ex.Message));
        }
    }
}