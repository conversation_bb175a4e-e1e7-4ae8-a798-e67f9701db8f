using CTeleport.Services.Booking.Services;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Models.Dto;
using Serilog;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class CancelReservationOnSagaRequestHandler(
    IBookingSagaService bookingSagaService,
    ILogger logger) : ICancellationServiceRequestHandler<CancelReservationOnSaga.Request, CancelReservationOnSaga.Response>
{
    public async Task<CancelReservationOnSaga.Response> HandleAsync(CancelReservationOnSaga.Request request)
    {
        var ctx = logger
            .ForContext("ReservationId", request.ReservationId)
            .ForContext("Request", request, true);
        
        try
        {
            await bookingSagaService.CancelReservationAsync(request.SagaId, request.ReservationId);
            return CancelReservationOnSaga.Response.FromResult(Result.Ok());
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(CancelReservationOnSaga)}");
            return CancelReservationOnSaga.Response.FromResult(Result.Fail(ex.Message));
        }
    }
}