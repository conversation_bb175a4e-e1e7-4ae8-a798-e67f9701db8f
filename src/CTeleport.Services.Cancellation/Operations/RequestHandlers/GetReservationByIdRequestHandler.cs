using AutoMapper;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Cancellation.Communication.Contracts.Messages;
using CTeleport.Services.Cancellation.Communication.Http.Core;
using CTeleport.Services.Cancellation.Extensions;
using CTeleport.Services.Cancellation.Models.Dto;
using CTeleport.Services.Cancellation.Models.Dto.Reservation;
using Serilog;

namespace CTeleport.Services.Cancellation.Operations.RequestHandlers;

public class GetReservationByIdRequestHandler(
    IBookingService bookingService,
    IMapper mapper,
    ILogger logger) : ICancellationServiceRequestHandler<GetReservation.ById.Request, GetReservation.ById.Response>
{
    public async Task<GetReservation.ById.Response> HandleAsync(GetReservation.ById.Request request)
    {
        var ctx = logger
            .ForContext("ReservationId", request.ReservationId);
        
        try
        {
            var reservation = await bookingService.GetReservationAsync(request.ReservationId);
            if (reservation == null)
            {
                var notFoundResult = Result<Reservation>.Fail("Reservation not found");
                return GetReservation.ById.Response.FromResult(notFoundResult);
            }

            var resultReservation = reservation.MapTo<Reservation>(mapper);
            if (resultReservation == null)
            {
                var mapFailedResult = Result<Reservation>.Fail("Failed to map reservation to DTO");
                return GetReservation.ById.Response.FromResult(mapFailedResult);
            }
            
            var result = Result<Reservation>.Ok(resultReservation);
            return GetReservation.ById.Response.FromResult(result);
        }
        catch (Exception ex)
        {
            ctx.Error(ex, $"Error while processing {nameof(GetReservation.ById)}");
            
            var result = Result<Reservation>.Fail(ex.Message);
            return GetReservation.ById.Response.FromResult(result);
        }
    }
}