<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="CTeleport.Common.Messaging" Version="1.1.293" />
      <PackageReference Include="CTeleport.Messages" Version="1.1.3440" />
      <PackageReference Include="CTeleport.Messages.Abstractions" Version="1.1.3440" />
      <PackageReference Include="CTeleport.Services.Booking.Core" Version="1.1.174" />
      <PackageReference Include="CTeleport.Services.Cancellation.Communication.Http" Version="2025.3.30.150" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\CTeleport.Services.Booking\CTeleport.Services.Booking.csproj" />
      <ProjectReference Include="..\CTeleport.Services.Helpers\CTeleport.Services.Helpers.csproj" />
    </ItemGroup>

</Project>
