using System;
using System.Collections.Generic;
using System.Net;
using CTeleport.Services.ApprovalQueueClient.Configuration;
using Polly;
using RestEase;
using Serilog;
using System.Threading.Tasks;
using CTeleport.Services.ApprovalQueueClient.Models;
using CTeleport.Services.Helpers;
using CTeleport.Services.TravelPolicies.Models;
using Polly.Retry;

namespace CTeleport.Services.ApprovalQueueClient
{
    public class ApprovalQueueClient : IApprovalQueueClient
    {
        [Header("User-Agent", "CTeleport.ApprovalQueueClient")]
        [Header("Accept", "application/json")]
        public interface IApprovalQueueApi
        {
            [Get("approval-queue/pending-items/{approverId}")]
            Task<IEnumerable<ApprovalQueueItem>> GetPendingApprovalQueueItemsAsync(
                [Header("Authorization", null)] string authToken, [Path] string approverId);

            [Get("approval-queue/bundles/pending?applicableFor=flight")]
            Task<PendingApprovalBundleResponse> GetPendingFlightApprovalBundlesAsync([Header("Authorization", null)] string authToken);

            [Get("approval-queue/{bookingId}")]
            Task<ApprovalQueueItem> GetApprovalQueueItemAsync(
                [Header("Authorization", null)] string authToken, [Path] string bookingId);

            [Post("travel-policies/flight_solution/single")]
            Task<FlightSolutionResponse> PostSingleFlightSolutionAsync([Body] SingleFlightSolutionRequest request);

            [Post("approval-queue/reference/{referenceId}/approve")]
            Task ApproveBundleAsync(
                [Header("Authorization", null)] string authToken, [Path] string referenceId, [Body] ApproveRejectBundleRequest request);

            [Post("approval-queue/reference/{referenceId}/reject")]
            Task RejectBundleAsync(
                [Header("Authorization", null)] string authToken, [Path] string referenceId, [Body] ApproveRejectBundleRequest request);
        }

        private readonly IApprovalQueueApi _api;
        private readonly AsyncRetryPolicy _policy;

        public ApprovalQueueClient(
            ApprovalQueueOptions options,
            ILogger logger)
        {
            _api = new RestClient(options.Url, HttpRequestHelper.AddApmTracingHeader)
            {
                JsonSerializerSettings = Common.Json.Settings.JsonSerializerSettings
            }.For<IApprovalQueueApi>();

            _policy = Policy
                .Handle<Exception>(e => !(e is ApiException apiException) ||
                                        (apiException.StatusCode != HttpStatusCode.NotFound
                                         && apiException.StatusCode != HttpStatusCode.Forbidden
                                         && apiException.StatusCode != HttpStatusCode.InternalServerError))
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromMilliseconds(300 * retryAttempt),
                    (exception, _, retryCount, _) => logger.Warning(exception,
                        "Exception {ExceptionType} on {RetryCount} retry count",
                        exception.GetType().Name, retryCount));
        }

        public Task<IEnumerable<ApprovalQueueItem>> GetPendingApprovalQueueItemsAsync(string authToken, string approverId)
            => _policy.ExecuteAsync(() => _api.GetPendingApprovalQueueItemsAsync(authToken, approverId));

        public Task<PendingApprovalBundleResponse> GetPendingFlightApprovalBundlesAsync(string authToken)
            => _policy.ExecuteAsync(() => _api.GetPendingFlightApprovalBundlesAsync(authToken));

        public Task<ApprovalQueueItem> GetApprovalQueueItemAsync(string authToken, string bookingId)
            => _policy.ExecuteAsync(() => _api.GetApprovalQueueItemAsync(authToken, bookingId));

        public Task<FlightSolutionResponse> GetSingleFlightSolutionResultAsync(SingleFlightSolutionRequest request)
            => _policy.ExecuteAsync(() => _api.PostSingleFlightSolutionAsync(request));

        public Task ApproveBundleAsync(string authToken, string bookingId, ApproveRejectBundleRequest request)
            => _policy.ExecuteAsync(() => _api.ApproveBundleAsync(authToken, bookingId, request));
        
        public Task RejectBundleAsync(string authToken, string bookingId, ApproveRejectBundleRequest request)
            => _policy.ExecuteAsync(() => _api.RejectBundleAsync(authToken, bookingId, request));
    }
}
