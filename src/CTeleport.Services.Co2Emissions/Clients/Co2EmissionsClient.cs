using System.Net;
using CTeleport.Services.Co2Emissions.Contracts.Requests;
using CTeleport.Services.Co2Emissions.Contracts.Responses;
using Polly;
using RestEase;
using Serilog;

namespace CTeleport.Services.Co2Emissions.Clients
{
    public class Co2EmissionsClient : ICo2EmissionsClient
    {
        private readonly ICo2EmissionsApi _api;
        private readonly ILogger _log;
        private readonly IAsyncPolicy _policy;

        public Co2EmissionsClient(ICo2EmissionsApi api, ILogger log)
        {
            _log = log;
            _api = api;
            _policy = Policy
                .Handle<Exception>(e => !(e is ApiException apiException) ||
                                        (apiException.StatusCode != HttpStatusCode.NotFound
                                         && apiException.StatusCode != HttpStatusCode.Forbidden
                                         && apiException.StatusCode != HttpStatusCode.InternalServerError))
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromMilliseconds(3000 * retryAttempt),
                    (exception, timeSpan, retryCount, context) =>
                    {
                        _log.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count",
                            exception.GetType().Name, retryCount);
                    });
        }

        public async Task<Co2EmissionsCalculationResponse> GetCO2Emissions([Body] Co2EmissionsCalculationRequest request)
        {
            return await _policy.ExecuteAsync(async () => await _api.GetCO2Emissions(request));
        }

        public interface ICo2EmissionsApi
        {
            [Post("co2-emissions")]
            Task<Co2EmissionsCalculationResponse> GetCO2Emissions([Body] Co2EmissionsCalculationRequest request);
        }
    }
}
