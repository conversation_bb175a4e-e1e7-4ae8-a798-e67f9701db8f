using System;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Travelport.Clients;
using Serilog;

namespace CTeleport.Services.Booking.Travelport.Services
{
    public class ClassDropRepriceService : IProviderClassdropRepriceService
    {
        private readonly ITravelportClient _travelportClient;
        private readonly ILogger _logger;

        public ClassDropRepriceService(ITravelportClient travelportClient, ILogger logger)
        {
            _travelportClient = travelportClient;
            _logger = logger;
        }

        public async Task<ProviderRepriceReservationResponse> RepriceReservationAsync(ProviderRepriceReservationRequest request)
        {
            try
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Debug("[TravelportClassDropRepriceService] Requesting Classdrop Reprice details for locator: {@locator}",
                    request.Locators);
                return await _travelportClient.GetRepricingForClassdropAsync(request);
            }
            catch (Exception e)
            {
                _logger.ForContext(nameof(request.CorrelationId), request.CorrelationId).Error(e, "Error getting ClassDrop Reprice for reservation {@locator} in Travelport.", request.Locators);
                return new ProviderRepriceReservationResponse().WithError(ErrorCodes.Error, "error while repricing a reservation in provider (Classdrop)");
            }
        }
    }
}