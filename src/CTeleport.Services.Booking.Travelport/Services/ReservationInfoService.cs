using System;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Travelport.Clients;
using Serilog;

namespace CTeleport.Services.Booking.Travelport.Services
{
    public class ReservationInfoService : IProviderReservationInfoService
    {
        private readonly ITravelportClient _travelportClient;
        private readonly ILogger _logger;

        public ReservationInfoService(ITravelportClient travelportClient, ILogger logger)
        {
            _travelportClient = travelportClient;
            _logger = logger;
        }

        public async Task<ProviderRetrieveReservationResponse> GetReservationAsync(ProviderRetrieveReservationRequest request)
        {
            try
            {
                _logger.Debug("[TravelportReservationService] Requesting Reservation details for locator: {@locator}", 
                    request.Locators);
                var reservation = await _travelportClient.GetReservationAsync(request);

                return reservation;
            }
            catch (Exception e)
            {
                _logger.Error(e, "Error getting reservation from Travelport in GetReservationAsync() {TraceId} {@Locators}", 
                    request.CorrelationId, request.Locators);
                
                return new ProviderRetrieveReservationResponse().WithError(ErrorCodes.Error, "error getting reservation from provider");
            }
        }
    }
}
