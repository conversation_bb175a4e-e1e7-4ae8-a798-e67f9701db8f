using System.Collections.Generic;
using CTeleport.Services.Search.Core.Dto;
using CTeleport.Services.Search.Shared.Enums;

namespace CTeleport.Services.Search.Shared.Models
{
    public class AlternativeFlightSolutionsRequest
    {
        public string SearchJobId { get; set; }
        public string Source { get; set; }
        public string Currency { get; set; }
        public FareType FareType { get; set; }
        public string PlatingCarrier { get; set; }
        public bool ProhibitPenaltyFares { get; set; }
        public bool DisableAlternativeFlow { get; set; }
        public IList<IList<ProviderFlightSegment>> LegSegments { get; set; }
        public string DepartureDate { get; set; }
        public string CorrelationId { get; internal set; }
        public string PrivateFareCode { get; set; }
    }
}