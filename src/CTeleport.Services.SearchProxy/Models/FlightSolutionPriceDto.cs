using System.Collections.Generic;

namespace CTeleport.Services.Search.Shared.Models
{
    public class FlightSolutionPriceDto
    {
        /// <summary>
        /// Target currency
        /// </summary>
        public string Ccy { get; set; }

        /// <summary>
        /// Total price in target currency
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>Net price, in original currency</summary>
        public decimal Net { get; set; }

        /// <summary>
        /// Price components with O-D pair as a key, and price in target currency
        /// </summary>
        public IDictionary<string, decimal> Components { get; set; }

        /// <summary>
        /// Price per mile, can be undefined in target currency
        /// </summary>
        public decimal? PerMile { get; set; }

        /// <summary>
        /// LI tax amount, in target currency
        /// </summary>
        public decimal LiTax { get; set; }

        /// <summary>
        /// LI tax amount, in provider currency
        /// </summary>
        public decimal OriginalLiTax { get; set; }

    }
}