using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.SearchProxy.Client;

namespace CTeleport.Services.SearchProxy.Services
{
    public class FlightSolutionService : IFlightSolutionService
    {
        private readonly ISearchClient _client;

        public FlightSolutionService(ISearchClient client)
        {
            _client = client;
        }

        public Task<FlightSolution> GetFlightSolutionAsync(string id)
        {
            return _client.GetFlightSolutionAsync(id);
        }

        public Task<IEnumerable<FlightSolutionDetails>> GetFlightSolutionAlternativeAsync(string searchAuthToken, FlightSolution sourceSolution, string departDate)
        {
            return _client.GetFlightSolutionAlternativeAsync(searchAuthToken, sourceSolution, departDate);
        }

        public Task<IEnumerable<FlightSolutionDetails>> RunFlightSolutionAlternativeAsync(string searchAuthToken, IList<AlternativeFlightSolutionsRequest> alternativeFlightSolutionsRequests, FlightSolution solution,
            SearchContext searchContext)
        {
            return _client.RunFlightSolutionAlternativeAsync(searchAuthToken, alternativeFlightSolutionsRequests, solution, searchContext);
        }
    }
}
