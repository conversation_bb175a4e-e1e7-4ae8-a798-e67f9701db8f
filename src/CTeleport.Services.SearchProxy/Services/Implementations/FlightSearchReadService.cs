using System.Threading.Tasks;
using CTeleport.Services.SearchProxy.Client;

namespace CTeleport.Services.SearchProxy.Services
{
    public class FlightSearchReadService : IFlightSearchReadService
    {
        private readonly ISearchClient _client;

        public FlightSearchReadService(ISearchClient client)
        {
            _client = client;
        }

        public Task<Search.Shared.Models.Search> GetSearchAsync(string searchId)
        {
            return _client.GetSearchAsync(searchId);
        }
    }
}
