using CTeleport.Services.Search.Shared.Models;
using System.Collections.Generic;

namespace CTeleport.Services.SearchProxy.Client
{
    public class RunFlightSolutionAlternativeRequest
    {
        public IList<AlternativeFlightSolutionsRequest> AlternativeFlightSolutionsRequests { get; set; }
        public FlightSolution FlightSolution { get; set; }
        public SearchContext SearchContext { get; set; }
    }
}