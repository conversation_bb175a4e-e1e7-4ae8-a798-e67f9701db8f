using Autofac;
using CTeleport.Common.Extensions;
using CTeleport.Services.Travelport.Clients;
using CTeleport.Services.Travelport.Configuration;
using Microsoft.Extensions.Configuration;

namespace CTeleport.Services.Travelport
{
    public class TravelportModule : Module
    {
        private readonly IConfiguration _configuration;

        public TravelportModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterInstance(_configuration.GetSettings<TravelportOptions>()).SingleInstance();
            builder.RegisterType<TravelportClient>().Keyed<ITravelportClient>(TravelportVersion.UAPI);
            builder.Register(c => new TravelportClientDecorator(
                c.ResolveKeyed<ITravelportClient>(TravelportVersion.UAPI),
                c.ResolveKeyed<ITravelportClient>(TravelportVersion.JSON))).As<ITravelportClient>();
        }
    }
}