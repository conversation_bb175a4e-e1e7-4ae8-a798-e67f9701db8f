using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Changes.Core.Dto;
using CTeleport.Services.Helpers.Constants;
using CTeleport.Services.Search.Shared.Models;

namespace CTeleport.Services.Travelport.Clients;

public class TravelportClientDecorator : ITravelportClient
{
    private readonly ITravelportClient _uapiClient;
    private readonly ITravelportClient _jsonClient;

    public TravelportClientDecorator(ITravelportClient uapiClient, 
        ITravelportClient jsonClient)
    {
        _uapiClient = uapiClient;
        _jsonClient = jsonClient;
    }

    public Task<ProviderRetrieveReservationResponse> GetReservationAsync(ProviderRetrieveReservationRequest request)
    {
        return _uapiClient.GetReservationAsync(request);
    }

    public Task<ConfirmReservationResponse> ConfirmReservationAsync(ConfirmReservationRequest request)
    {
        return _uapiClient.ConfirmReservationAsync(request);
    }

    public Task<ProviderAvailabilityResponse> GetAvailabilitySolutionsAsync(ProviderAvailabilityRequest request)
    {
        return _uapiClient.GetAvailabilitySolutionsAsync(request);
    }

    public Task<ProviderRetrieveTicketResponse> GetTicketStateAsync(ProviderRetrieveTicketRequest request)
    {
        return GetClient(request).GetTicketStateAsync(request);
    }

    public Task<ProviderRepriceReservationResponse> GetRepricingForFareRefreshAsync(ProviderRepriceReservationRequest request)
    {
        return _uapiClient.GetRepricingForFareRefreshAsync(request);
    }

    public Task<ProviderFareRefreshResponse> RefreshFareMaskAsync(ProviderFareRefreshRequest request)
    {
        return _uapiClient.RefreshFareMaskAsync(request);
    }

    public Task<ProviderRepriceReservationResponse> GetRepricingForClassdropAsync(ProviderRepriceReservationRequest request)
    {
        return _uapiClient.GetRepricingForClassdropAsync(request);
    }

    public Task<ProviderInternalQuotationResponse> GetRebookQuotationsAsync(ProviderChangesQuotationRequest request)
    {
        return _uapiClient.GetRebookQuotationsAsync(request);
    }

    public Task<ProviderFareModifierResponse> ModifyFareAsync(ProviderFareModifierRequest request)
    {
        return _uapiClient.ModifyFareAsync(request);
    }

    public Task<FlightSolution> GetPnrAwareQuotationAsync(ProviderChangesQuotationRequest request)
    {
        return _uapiClient.GetPnrAwareQuotationAsync(request);
    }

    public Task<IList<FlightSolution>> GetFastQuotationAsync(ProviderChangesQuotationRequest request)
    {
        return _uapiClient.GetFastQuotationAsync(request);
    }
    
    private ITravelportClient GetClient(ProviderBaseRequest request)
    {
        return request.SourceDetails?.Version == ProviderKeyIndicators.JsonSource ? _jsonClient : _uapiClient;
    }
}