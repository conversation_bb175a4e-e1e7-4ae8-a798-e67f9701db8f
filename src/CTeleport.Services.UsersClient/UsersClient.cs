using System;
using System.Net.Http;
using System.Threading.Tasks;
using CTeleport.Common.Json;
using CTeleport.Services.UsersClient.Configuration;
using CTeleport.Services.UsersClient.Models;
using Serilog;
using Polly;
using Polly.Retry;
using RestEase;
using static System.Net.HttpStatusCode;
using Policy = Polly.Policy;

namespace CTeleport.Services.UsersClient;

public class UsersClient : IUsersClient
{
    private const int RetryCount = 3;
    private const int Timeout = 3000;

    private readonly IUsersApi _usersApi;
    private readonly AsyncRetryPolicy _policy;

    public UsersClient(UsersApiOptions options, ILogger logger)
    {
        _policy = Policy
            .Handle<HttpRequestException>(e => e.StatusCode != NotFound && e.StatusCode != InternalServerError)
            .WaitAndRetryAsync(RetryCount, retryAttempt => TimeSpan.FromMilliseconds(Timeout * retryAttempt),
                (exception, _, retryCount, _) =>
                {
                    logger.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count",
                        exception.GetType().Name, retryCount);
                });

        _usersApi = new RestClient(options.Url)
        {
            JsonSerializerSettings = Settings.JsonSerializerSettings
        }.For<IUsersApi>();
    }

    public Task<UserDto> GetUserByIdAsync(string userId)
        => _policy.ExecuteAsync(() => _usersApi.GetUserByIdAsync(userId));

    [Header("Accept", "application/json")]
    public interface IUsersApi
    {
        [Get("/internal/users/{id}")]
        Task<UserDto> GetUserByIdAsync([Path] string id);
    }
}

