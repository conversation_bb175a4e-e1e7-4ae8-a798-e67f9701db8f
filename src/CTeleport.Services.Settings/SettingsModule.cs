using Autofac;
using CTeleport.Common.Extensions;
using CTeleport.Common.Json;
using CTeleport.Services.Helpers;
using CTeleport.Services.Settings.Clients;
using CTeleport.Services.Settings.Configuration;
using CTeleport.Services.Settings.Services;
using Microsoft.Extensions.Configuration;
using RestEase;

namespace CTeleport.Services.Settings
{
    public class SettingsModule : Autofac.Module
    {
        private readonly IConfiguration _configuration;

        public SettingsModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterInstance(_configuration.GetSettings<TenantManagementServiceOptions>()).SingleInstance();

            builder.RegisterType<TenantManagementServiceClient>().As<ITenantManagementServiceClient>();
            builder.Register(c =>
                new RestClient(c.Resolve<TenantManagementServiceOptions>().Url, HttpRequestHelper.AddApmTracingHeader)
                {
                    JsonSerializerSettings = Common.Json.Settings.JsonSerializerSettings
                }.For<ITenantManagementServiceApi>()).SingleInstance();

            builder.RegisterType<AirProvidersService>().As<IAirProvidersService>();
            builder.RegisterType<SettingsService>().As<ISettingsService>();
        }
    }
}