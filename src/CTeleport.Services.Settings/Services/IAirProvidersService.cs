using System.Threading.Tasks;
using CTeleport.Services.Providers.Dto;

namespace CTeleport.Services.Settings.Services
{
    public interface IAirProvidersService
    {
        /// <summary>
        /// GetSource Async version 
        /// Returns source by id
        /// </summary>
        /// <param name="sourceId">Source id, eg. 1G.8WG0</param>
        /// <returns></returns>
        Task<AirProvider> GetSourceAsync(string sourceId);

        /// <summary>
        /// Check source if it is enabled globally.
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        Task<bool> CheckSourceGloballyEnabled(string source);
    }
}