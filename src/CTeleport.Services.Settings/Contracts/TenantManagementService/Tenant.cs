using System;
using System.Collections.Generic;
using CTeleport.Services.TenantShared.Models;

namespace CTeleport.Services.Settings.Contracts.TenantManagementService
{
    public class Tenant
    {
        public string Id { get; set; }

        public Markup Markup { get; set; }

        public Kickback Kickback { get; set; }

        public HighFareMarkup HighFareMarkup { get; set; }

        public MarineFareMarkup MarineFareMarkup { get; set; }

        public string Currency { get; set; }

        public string HomeSource { get; set; }

        /// <summary>
        /// Dictionary of tenant corporate codes.
        /// Key - airline code.
        /// Value - corporate code.
        /// </summary>
        public Dictionary<string, string> CorporateCodes { get; set; }

        /// <summary>
        /// Dictionary of tenant search features:
        /// Key - feature name
        /// Value - is this feature enabled
        /// </summary>
        [Obsolete(@"This property will be deleted soon. Use 'TenantFeatures' instead")]
        public Dictionary<string, bool> TenantSearchFeatures { get; set; }

        /// <summary>
        /// Actual tenant features, subject to global and white-label forced settings
        /// </summary>
        public TenantFeatures TenantFeatures { get; set; }

        public bool IsBillingEnabled { get; set; }

        public bool IsNewChangesEnabled { get; set; }

        public string OnlyAvailableSource { get; set; }

        public bool IsExtraServicesEnabled { get; set; }

        public bool IsBlocked { get; set; }

        public string BlockReasonCode { get; set; }
        public string NotificationEmail { get; set; }
    }
}