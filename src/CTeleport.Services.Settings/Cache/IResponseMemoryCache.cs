namespace CTeleport.Services.Settings.Cache
{
    public interface IResponseMemoryCache
    {
        /// <summary>
        /// Returns cached item by key if it's exist there. Otherwise makes api call and stores result in cache.
        /// Should be used for single item. For collection of items see enumerable overload.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="apiCall"></param>
        /// <returns></returns>
        System.Threading.Tasks.Task<T> WithCache<T>(string key, System.Func<System.Threading.Tasks.Task<RestEase.Response<T>>> apiCall) where T : class;
    }
}