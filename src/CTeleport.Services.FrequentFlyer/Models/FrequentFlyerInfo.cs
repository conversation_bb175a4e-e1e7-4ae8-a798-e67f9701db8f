using System.Collections.Generic;

namespace CTeleport.Services.FrequentFlyer.Models
{
    public class FrequentFlyerInfo
    {
        /// <summary>
        /// Components - allow to apply several number to single booking (for split tickets and some other cases)
        /// </summary>
        public IList<FrequentFlyerComponent> Components { get; set; }
    }

    public class FrequentFlyerInfoForReservation
    {
        public bool AllowCorporateCodes { get; set; }
        public IList<FrequentFlyerNumber> FrequentFlyerNumbers { get; set; }

        public static FrequentFlyerInfoForReservation Empty => new FrequentFlyerInfoForReservation
        {
            AllowCorporateCodes = true,
            FrequentFlyerNumbers = new List<FrequentFlyerNumber>(0)
        };
    }

    public class FrequentFlyerNumber
    {
        /// <summary>
        /// Loyalty program code
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Loyalty program carrier
        /// </summary>
        public string Carrier { get; set; }

        /// <summary>
        /// Loyalty program participant's number
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// Loyalty program name
        /// </summary>
        public string ProgramName { get; set; }
    }

    public class FrequentFlyerComponent
    {
        /// <summary>
        /// Key
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// Name for UI in case of multiple components
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// List of supported LoyaltyCards
        /// </summary>
        public IList<FrequentFlyerProgram> SupportedFrequentFlyerPrograms { get; set; }
    }

    public class FrequentFlyerProgram
    {
        /// <summary>
        /// Program Code in our system
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// Carrier
        /// </summary>
        public string Carrier { get; set; }
        /// <summary>
        /// Loyalty Program name
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Rank shows frequent flyer program priority. Program with higher rank is recommended.
        /// </summary>
        public int Rank { get; set; }
    }
}
