using System;
using System.Net;
using System.Threading.Tasks;
using CTeleport.Services.CheckFare.Dto;
using CTeleport.Services.Galileo.Configuration;
using CTeleport.Services.Helpers;
using Polly;
using Polly.Retry;
using RestEase;
using Serilog;

namespace CTeleport.Services.CheckFare.Travelport
{
    public class TravelportCheckFareService : IProviderCheckFareService
    {
        [Header("User-Agent", "CTeleport.Api")]
        [Header("Accept", "application/json")]
        public interface IGalileoApi
        {
            [Get("fares")]
            [Header("Cache-Control", "public")]
            Task<CheckFareResponse> CheckFareAsync([Query]string carrier, [Query]string origin, [Query]string destination, [Query]string ptc, [Query]string currency, [Query]string pcc, [Query]string tripType = null);
        }

        private readonly IGalileoApi _api;
        private readonly AsyncRetryPolicy _policy;

        public TravelportCheckFareService(GalileoOptions options, ILogger logger)
        {
            _api = new RestClient(options.Url, HttpRequestHelper.AddApmTracingHeader)
            {
                JsonSerializerSettings = Common.Json.Settings.JsonSerializerSettings
            }.For<IGalileoApi>();

            _policy = Policy
                .Handle<Exception>(e =>
                {
                    if (!(e is ApiException apiException))
                    {
                        return true;
                    }

                    return apiException.StatusCode != HttpStatusCode.BadRequest
                           && apiException.StatusCode != HttpStatusCode.NotFound;
                })
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromMilliseconds(300 * retryAttempt),
                    (exception, timeSpan, retryCount, context) =>
                    {
                        logger.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount);
                    });
        }

        public async Task<CheckFareResponse> CheckFareAsync(CheckFareRequest request)
         => await _policy.ExecuteAsync(async () => await _api.CheckFareAsync(request.Carrier, request.Origin, request.Destination, request.Ptc, request.Currency, request.Pcc, Enum.GetName(typeof(TripType), request.TripType)));
    }
}