using System.Threading.Tasks;
using CTeleport.Services.FareTerms.Shared.Requests;
using CTeleport.Services.FareTerms.Shared.Responses;

namespace CTeleport.Services.FareTerms
{
    public interface IFareTermsClient
    {
        Task<OnPostBookingResponse> GetFareTermsByTimelineAsync(OnPostBookingRequest request);
        Task<OnAutoRefundResponse> GetRefundFeesAsync(OnAutoRefundRequest request);
    }
}
