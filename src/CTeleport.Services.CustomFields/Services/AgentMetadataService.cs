using CTeleport.Services.CustomFields.Repositories.Interfaces;
using CTeleport.Services.CustomFields.Services.Interfaces;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.CustomFields.Helpers;
using CTeleport.Services.CustomFields.Models;
using CTeleport.Services.Helpers.Extensions;
using CTeleport.Services.Settings.Contracts.TenantManagementService;
using Microsoft.FeatureManagement;

namespace CTeleport.Services.CustomFields.Services
{
    public class AgentMetadataService : IAgentMetadataService
    {
        private readonly IVesselAgentCodesRepository _repository;
        private readonly IVesselsCustomFieldsRepository _vesselsCustomFieldsRepository;
        private readonly IFeatureManager _featureManager;

        public AgentMetadataService(IVesselAgentCodesRepository repository, IVesselsCustomFieldsRepository vesselsCustomFieldsRepository, IFeatureManager featureManager)
        {
            _repository = repository;
            _vesselsCustomFieldsRepository = vesselsCustomFieldsRepository;
            _featureManager = featureManager;
        }

        public async Task<AgentAccountMetadata> Generate(Tenant tenant, string source, string vesselName)
        {
            if (tenant?.Id == null)
            {
                return null;
            }

            var notificationEmail = GetNotificationEmail(tenant, source);
            var isNewSourceEnabled = await _featureManager.IsEnabledAsync(FeaturesHelper.VesselManagementFeature);
            
            if (vesselName != null)
            {
                var codes = isNewSourceEnabled
                    ? await _vesselsCustomFieldsRepository.GetVesselAgentCodesAsync(vesselName, tenant.Id)
                    : await _repository.GetAsync(vesselName);

                if (codes != null)
                {
                    //First check the exact tenant match, then by pattern tenant-*
                    var code = codes.FirstOrDefault(x => x.Tenant == tenant.Id) ?? codes.FirstOrDefault(x => tenant.Id.StartsWith(x.Tenant + "-"));

                    if (code != null && !string.IsNullOrEmpty(code.AccountCode))
                    {
                        return new AgentAccountMetadata()
                        {
                            AccountCode = code.AccountCode,
                            AccountName = code.AccountName,
                            NotificationEmail = notificationEmail
                        };
                    }
                }
            }

            var accountInfo = isNewSourceEnabled
                ? await _vesselsCustomFieldsRepository.GetFleetCodesAsync(tenant.Id)
                : (await _repository.GetByAgentCodeByTenantAsync(tenant.Id)).FirstOrDefault();

            if (accountInfo != null)
            {
                return new AgentAccountMetadata()
                {
                    AccountCode = accountInfo.AccountCode,
                    AccountName = accountInfo.AccountName,
                    NotificationEmail = notificationEmail
                };
            }

            if (!string.IsNullOrEmpty(notificationEmail))
            {
                return new AgentAccountMetadata()
                {
                    NotificationEmail = notificationEmail
                };
            }

            return null;
        }

        private string GetNotificationEmail(Tenant tenant, string source)
        {
            if (tenant.TenantFeatures?.TravelfusionUseTenantNotificationEmail == true
                && source.IsTravelfusion()
                && !string.IsNullOrEmpty(tenant.NotificationEmail))
            {
                return tenant.NotificationEmail;
            }

            return null;
        }
    }
}
