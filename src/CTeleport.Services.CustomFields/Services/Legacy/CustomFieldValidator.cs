using CTeleport.Services.CustomFields.Services.Interfaces.Legacy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using LegacyPublicContract = CTeleport.Services.CustomFields.Dto.LegacyPublicContract;

namespace CTeleport.Services.CustomFields.Services.Legacy;

public class CustomFieldValidator : IValidator<LegacyPublicContract.TenantCustomFieldDto>
{
    private const string OnlyASCIIPattern = @"^[\x00-\x7F]+$";
    private const string OnlyAlphanumericSnakeCasePattern = "^[a-z0-9_]*$";
    private const int MaxLength = 255;

    public (bool IsValid, string[] Errors) Validate(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        var errors = new List<string>();

        var (isNameValid, nameErrors) = ValidateName(customField);
        if (!isNameValid)
        {
            errors.AddRange(nameErrors);
        }

        var (isFieldNameValid, fieldNameErrors) = ValidateFieldName(customField);
        if (!isFieldNameValid)
        {
            errors.AddRange(fieldNameErrors);
        }

        var (isValidByType, validByTypeErrors) = customField.FieldType switch
        {
            LegacyPublicContract.TenantCustomFieldTypeDto.Text => (IsValid: true, Errors: null),
            LegacyPublicContract.TenantCustomFieldTypeDto.Dropdown => ValidateCustomFieldTypeDropdown(customField),
            LegacyPublicContract.TenantCustomFieldTypeDto.Hidden => ValidateCustomFieldTypeHidden(customField),
            _ => throw new ArgumentOutOfRangeException($"{customField.FieldType} is not supported")
        };
        if (!isValidByType)
        {
            errors.AddRange(validByTypeErrors);
        }

        return CalculateValidationResult(errors);
    }

    private static (bool IsValid, string[] Errors) ValidateCustomFieldTypeDropdown(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        var (isOptionsValid, optionsErrors) = ValidateOptions(customField);
        if (!isOptionsValid)
        {
            return CalculateValidationResult(optionsErrors);
        }

        return (IsValid: true, Errors: null);
    }

    private static (bool IsValid, string[] Errors) ValidateCustomFieldTypeHidden(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        var errors = new List<string>();

        var (isTooltipValid, tooltipErrors) = ValidateTooltip(customField);
        if (!isTooltipValid)
        {
            errors.AddRange(tooltipErrors);
        }

        var (isPlaceholderValid, placeholderErrors) = ValidatePlaceholder(customField);
        if (!isPlaceholderValid)
        {
            errors.AddRange(placeholderErrors);
        }

        var (isCustomValidationValid, customValidationErrors) = ValidateCustomFieldValidation(customField);
        if (!isCustomValidationValid)
        {
            errors.AddRange(customValidationErrors);
        }

        return CalculateValidationResult(errors);
    }

    private static (bool IsValid, string[] Errors) ValidateName(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        var value = customField.Name;
        var field = nameof(LegacyPublicContract.TenantCustomFieldDto.Name);

        var errors = new List<string>();

        if (!IsValidByLength(value))
        {
            errors.Add($"The custom field '{field}' should contain from 1 to {MaxLength} symbols");
            return CalculateValidationResult(errors);
        }

        if (!IsValidByPattern(value, OnlyASCIIPattern))
        {
            errors.Add($"The custom field '{field}' with Value:'{value}' should contain only ASCII symbols");
        }

        return CalculateValidationResult(errors);
    }

    private static (bool IsValid, string[] Errors) ValidateFieldName(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        var value = customField.FieldName;
        var field = nameof(LegacyPublicContract.TenantCustomFieldDto.FieldName);

        var errors = new List<string>();

        if (!IsValidByLength(value))
        {
            errors.Add($"The custom field '{field}' should contain from 1 to {MaxLength} symbols");
            return CalculateValidationResult(errors);
        }
        if (!IsValidByPattern(value, OnlyAlphanumericSnakeCasePattern))
        {
            errors.Add($"The custom field '{field}' with Value:'{value}' should contain only numbers, lower case letters, and underscore");
        }

        return CalculateValidationResult(errors);
    }

    private static (bool IsValid, string[] Errors) ValidateOptions(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        var errors = new List<string>();

        if (customField.Options is null || !customField.Options.Any())
        {
            errors.Add($"The custom field of type '{LegacyPublicContract.TenantCustomFieldTypeDto.Dropdown}' should contain at least one option");
            return CalculateValidationResult(errors);
        }

        var hashset = new HashSet<string>();
        foreach (var option in customField.Options)
        {
            ValidateOptionKey(option.Key);
            ValidateOptionValue(option.Key, option.Value);
            ValidateOptionCode(option.Key, option.Code);
        }

        return CalculateValidationResult(errors);

        void ValidateOptionValue(string key, string value)
        {
            if (!IsValidByLength(value))
            {
                errors.Add($"The custom field option '{nameof(LegacyPublicContract.CustomOptionDto.Value)}' with Key:'{key}' should contain from 1 to {MaxLength} symbols");
                return;
            }
            if (!IsValidByPattern(value, OnlyASCIIPattern))
            {
                errors.Add($"The custom field option '{nameof(LegacyPublicContract.CustomOptionDto.Value)}' with Key:'{key}' should contain only ASCII symbols");
            }
        }

        void ValidateOptionKey(string key)
        {
            if (!IsValidByLength(key))
            {
                errors.Add($"The custom field option '{nameof(LegacyPublicContract.CustomOptionDto.Key)}' should contain from 1 to {MaxLength} symbols");
                return;
            }
            if (!IsValidByPattern(key, OnlyASCIIPattern))
            {
                errors.Add($"The custom field option '{nameof(LegacyPublicContract.CustomOptionDto.Key)}':'{key}' should contain only ASCII symbols");
                return;
            }
            if (!hashset.Add(key))
            {
                errors.Add($"Custom field {customField.Name} has not unique options with '{nameof(LegacyPublicContract.CustomOptionDto.Key)}':'{key}'");
            }
        }

        void ValidateOptionCode(string key, string code)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                return;
            }
            if (code.Length > MaxLength)
            {
                errors.Add($"The custom field option '{nameof(LegacyPublicContract.CustomOptionDto.Code)}' with Key:'{key}' should contain max {MaxLength} symbols");
                return;
            }
            if (!IsValidByPattern(code, OnlyASCIIPattern))
            {
                errors.Add($"The custom field option '{nameof(LegacyPublicContract.CustomOptionDto.Value)}' with Key:'{key}' should contain only ASCII symbols");
            }
        }
    }

    private static (bool IsValid, string[] Errors) ValidateTooltip(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        if (string.IsNullOrWhiteSpace(customField.Tooltip))
        {
            return (IsValid: true, Errors: null);
        }

        var error = $"For custom field type {LegacyPublicContract.TenantCustomFieldTypeDto.Hidden} only empty {nameof(LegacyPublicContract.TenantCustomFieldDto.Tooltip)} is allowed";
        return CalculateValidationResult(new[] { error });
    }

    private static (bool IsValid, string[] Errors) ValidatePlaceholder(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        if (string.IsNullOrWhiteSpace(customField.Placeholder))
        {
            return (IsValid: true, Errors: null);
        }

        var error = $"For custom field type {LegacyPublicContract.TenantCustomFieldTypeDto.Hidden} only empty {nameof(LegacyPublicContract.TenantCustomFieldDto.Placeholder)} is allowed";
        return CalculateValidationResult(new[] { error });
    }

    private static (bool IsValid, string[] Errors) ValidateCustomFieldValidation(LegacyPublicContract.TenantCustomFieldDto customField)
    {
        if (customField.Validation is null)
        {
            return (IsValid: true, Errors: null);
        }

        var error = $"For custom field type {LegacyPublicContract.TenantCustomFieldTypeDto.Hidden} only empty {nameof(LegacyPublicContract.TenantCustomFieldDto.Validation)} is allowed";
        return CalculateValidationResult(new[] { error });
    }

    private static bool IsValidByLength(string value) =>
        !string.IsNullOrWhiteSpace(value) && value.Length <= MaxLength;

    private static bool IsValidByPattern(string value, string pattern) =>
        Regex.IsMatch(value, pattern);

    private static (bool IsValid, string[] Errors) CalculateValidationResult(IList<string> errors)
    {
        var isValid = !errors.Any();
        return (IsValid: isValid, Errors: isValid ? null : errors.ToArray());
    }
}