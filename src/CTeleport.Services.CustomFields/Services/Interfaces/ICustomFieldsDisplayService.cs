using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Services.CustomFields.Dto;

namespace CTeleport.Services.CustomFields.Services.Interfaces
{
    public interface ICustomFieldsDisplayService
    {
        /// <summary>
        /// Resolves custom fields values from a booking custom fields.
        /// </summary>
        /// <param name="fieldsReferences">Current custom fields references metadata.</param>
        /// <param name="tenantId">Tenant Id.</param>
        /// <returns>Custom Fields values resolved from <param>bookingCustomFields and ready to be displayed.</param></returns>
        Task<IList<DisplayCustomFieldDto>> GetCustomFieldsToDisplay(Dictionary<string, string> fieldsReferences, string tenantId);
    }
}