using Serilog;
using CTeleport.Services.CustomFields.Dto;
using CTeleport.Services.CustomFields.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using CTeleport.Services.CustomFields.Helpers;
using CTeleport.Services.CustomFields.Models;
using System.Globalization;
using Microsoft.FeatureManagement;
using NodaTime;
using NodaTime.Extensions;
using CTeleport.Services.CustomFields.Repositories.Interfaces.Legacy;

namespace CTeleport.Services.CustomFields.Services
{
    public class CustomDataService : ICustomDataService
    {
        private readonly ILogger _logger;
        private readonly ITenantCustomCodesRepository _tenantCustomCodesRepository;
        private readonly IVesselMetadataRepository _vesselMetadataRepository;
        private readonly ITenantCustomFieldsRepository _tenantCustomFieldsRepository;
        private readonly IVesselsCustomFieldsRepository _vesselsCustomFieldsRepository;
        private readonly IFeatureManager _featureManager;

        public CustomDataService(ILogger logger, ITenantCustomCodesRepository tenantCustomMetadataRepository, IVesselMetadataRepository vesselMetadataRepository, ITenantCustomFieldsRepository tenantCustomFieldsRepository, IVesselsCustomFieldsRepository vesselsCustomFieldsRepository, IFeatureManager featureManager)
        {
            _logger = logger;
            _tenantCustomCodesRepository = tenantCustomMetadataRepository;
            _vesselMetadataRepository = vesselMetadataRepository;
            _tenantCustomFieldsRepository = tenantCustomFieldsRepository;
            _vesselsCustomFieldsRepository = vesselsCustomFieldsRepository;
            _featureManager = featureManager;
        }

        private Dictionary<string, Dictionary<string, string>> AddMetadataByType(CustomMetadataRequest metadataRequest, Dictionary<string, Dictionary<string, string>> advancedMetadata)
        {
            if (metadataRequest.Type == CustomMetadataRequestType.TicketIssued)
            {
                if (!advancedMetadata.ContainsKey(DynamicMetadataGroups.Common))
                {
                    advancedMetadata[DynamicMetadataGroups.Common] = new Dictionary<string, string>();
                }

                advancedMetadata[DynamicMetadataGroups.Common][CommonFields.TicketIssueDate] = GetDateAtPCC(metadataRequest.ReservationContext.TargetSource.Timezone, DateTime.UtcNow);
            }

            return advancedMetadata;
        }

        public string GetDateAtPCC(string sourceTimeZone, DateTime dt)
        {
            DateTimeZone pccTz;
            try
            {
                pccTz = DateTimeZoneProviders.Tzdb[sourceTimeZone];
            }
            catch (NodaTime.TimeZones.DateTimeZoneNotFoundException)
            {
                throw new Common.Exceptions.NotFoundException($"Could not found timezone {sourceTimeZone}");
            }

            var zt = new ZonedDateTime(dt.ToInstant(), pccTz);
            return zt.ToString("ddMMM", CultureInfo.InvariantCulture);
        }

        private async Task<Dictionary<string, Dictionary<string, string>>> BuildAdvancedMetadata(CustomMetadataRequest metadataRequest)
        {
            var advancedMetadata = new Dictionary<string, Dictionary<string, string>>();

            if (metadataRequest.Vessel != null)
            {
                var isNewSourceEnabled = await _featureManager.IsEnabledAsync(FeaturesHelper.VesselManagementFeature);
                var vesselCodes = isNewSourceEnabled
                    ? await _vesselsCustomFieldsRepository.GetVesselCustomMetadataAsync(metadataRequest.Vessel.Name, metadataRequest.TenantId)
                    : await _vesselMetadataRepository.GetAsync(metadataRequest.Vessel.Name, metadataRequest.TenantId);

                var vesselMetadata = new Dictionary<string, string>()
                {
                    { "Name", metadataRequest.Vessel.Name },
                    { "Flag", metadataRequest.Vessel.Flag }
                };

                foreach (var item in vesselCodes.Where(x => x.MetaData != null).SelectMany(x => x.MetaData))
                {
                    vesselMetadata[item.Key] = item.Value;
                }

                advancedMetadata[DynamicMetadataGroups.Vessel] = vesselMetadata;
            }

            var customFields = new Dictionary<string, string>();
            if (metadataRequest.CustomFields != null)
            {
                var rs = await _tenantCustomFieldsRepository.GetAsync(metadataRequest.TenantId);
                var fieldsSettings = rs.Where(x => x.CustomFields != null).SelectMany(x => x.CustomFields).ToArray();

                foreach (var item in metadataRequest.CustomFields)
                {
                    var value = fieldsSettings.FirstOrDefault(x => x.FieldName == item.Key);
                    if (value?.FieldType == TenantCustomFieldType.Dropdown)
                    {
                        var codeValue = value.Options.FirstOrDefault(x => x.Key == item.Value);
                        if (codeValue != null)
                        {
                            customFields[item.Key] = codeValue.Code ?? codeValue.Key;
                            customFields[item.Key + "_Key"] = codeValue.Key;
                            customFields[item.Key + "_Value"] = codeValue.Value;
                        }
                    }
                    else
                    {
                        customFields[item.Key] = item.Value;
                    }
                }

                advancedMetadata[DynamicMetadataGroups.Custom] = customFields;
            }

            //Override account code by field from UI
            if (customFields.TryGetValue("account_code", out string accountCode))
            {
                metadataRequest.ReservationContext.AgentMetadata = new AgentAccountMetadata() { AccountCode = accountCode }; //TODO: avoid request mutation
            }

            if (metadataRequest.ReservationContext.AgentMetadata != null)
            {
                var agentMetadata = new Dictionary<string, string>()
                {
                    { "Code", metadataRequest.ReservationContext.AgentMetadata.AccountCode },
                    { "Name", metadataRequest.ReservationContext.AgentMetadata.AccountName }
                };

                advancedMetadata[DynamicMetadataGroups.Account] = agentMetadata;
            }

            var commonMetadata = new Dictionary<string, string>();

            // Common
            if (metadataRequest.CreatedBy != null)
            {
                commonMetadata[CommonFields.BookedBy] = metadataRequest.CreatedBy.Name ?? metadataRequest.CreatedBy.Email;
                commonMetadata[CommonFields.BookedByEmail] = metadataRequest.CreatedBy.Email;
            }

            commonMetadata[CommonFields.TargetTotalPrice] = metadataRequest.Price.TotalPrice.ToString("0.00");
            commonMetadata[CommonFields.TargetCurrency] = metadataRequest.Price.Currency;
            commonMetadata[CommonFields.QuotedPrice] = metadataRequest.Price.QuotedPrice.ToString("0.00");

            commonMetadata[CommonFields.Markup] = metadataRequest.Price.Markup.ToString("0.00");
            commonMetadata[CommonFields.MarkupCurrency] = metadataRequest.Price.MarkupCurrency;
            commonMetadata[CommonFields.TargetMarkup] = metadataRequest.Price.TargetMarkup.ToString("0.00");
            commonMetadata[CommonFields.BaseMarkup] = metadataRequest.Price.BaseMarkup.ToString("0.00");
            commonMetadata[CommonFields.BaseTargetMarkup] = metadataRequest.Price.BaseTargetMarkup.ToString("0.00");
            commonMetadata[CommonFields.InitialLocator] = metadataRequest.InitialLocator?.Replace("C-","");

            if (metadataRequest.Price.HighFareTargetMarkup != 0M)
            {
                commonMetadata[CommonFields.HighFareTargetMarkup] = metadataRequest.Price.HighFareTargetMarkup.ToString("0.00");
            }

            if (metadataRequest.Price.MarineFareTargetMarkup != 0M)
            {
                commonMetadata[CommonFields.MarineFareTargetMarkup] = metadataRequest.Price.MarineFareTargetMarkup.ToString("0.00");
            }

            advancedMetadata[DynamicMetadataGroups.Common] = commonMetadata;

            return advancedMetadata;
        }

        public async Task<CustomMetadataResponse> GenerateMetadata(CustomMetadataRequest metadata)
        {
            var advancedMetadata = await BuildAdvancedMetadata(metadata);
            
            AddMetadataByType(metadata, advancedMetadata);

            if (!TenantHelper.IsGmt(metadata.TenantId))
            {
                return BuildResponse(advancedMetadata);
            }

            if (metadata.ReservationContext.HomeSource == null)
            {
                _logger.Error($"HomeSource can not be null for: {metadata.ReservationContext.Tenant}");
                //throw new ArgumentNullException(nameof(metadata.ReservationContext.HomeSource));
                return BuildResponse(advancedMetadata);
            }

            if (metadata.ReservationContext.AgentMetadata == null)
            {
                _logger.Error($"AgentMetadata can not be null for Vessel:{metadata.Vessel?.Name}");
                metadata.ReservationContext.AgentMetadata = new AgentAccountMetadata();
            }

            advancedMetadata["Gmt"] = await BuildGmtMetadata(metadata);

            return BuildResponse(advancedMetadata);
        }

        private CustomMetadataResponse BuildResponse(Dictionary<string, Dictionary<string, string>> advancedMetadata)
        {
            return new CustomMetadataResponse
            {
                AdvancedMetadata = advancedMetadata
                //ReservationMetadata = CreateReservationMetadata(advancedMetadata)
            };
        }

        private static Dictionary<string, string> CreateReservationMetadata(Dictionary<string, Dictionary<string, string>> advancedMetadata)
        {
            //Flattering metadata to store in DB
            var reservationMetadata = new Dictionary<string, string>();
            foreach (var groupItem in advancedMetadata)
            {
                if (groupItem.Key == "Common")
                {
                    continue;
                }

                foreach (var item in groupItem.Value)
                {
                    reservationMetadata[groupItem.Key + "_" + item.Key] = item.Value;
                }
            }
            return reservationMetadata;
        }

        private async Task<Dictionary<string, string>> BuildGmtMetadata(CustomMetadataRequest metadata)
        {
            var gmtMetadata = new Dictionary<string, string>();

            var targetPriceMargin = metadata.Price.TargetClassDropMargin + metadata.Price.MarineFareTargetMarkup;
            if (targetPriceMargin > 0)
            {
                gmtMetadata[GmtFields.TargetPriceMargin] = targetPriceMargin.ToString();
            }

            var targetPriceMarginHidden = metadata.Price.TargetClassDropMargin + metadata.Price.TargetMarkup;
            if (targetPriceMarginHidden > 0)
            {
                gmtMetadata[GmtFields.TargetPriceMarginHidden] = targetPriceMarginHidden.ToString("0.00");
            }

            gmtMetadata[GmtFields.CeilingTotalPrice] = Math.Ceiling(metadata.Price.QuotedPrice).ToString("0.00");
            gmtMetadata[GmtFields.BaseTicketPrice] = (metadata.Price.QuotedPrice - metadata.Price.TotalTaxes).ToString("0.00"); // review format
            gmtMetadata[GmtFields.HomeCompanyCode] = GetHomeSourceCode(metadata);
            gmtMetadata[GmtFields.PaxName] = GetPaxName(metadata.Passenger);

            gmtMetadata[GmtFields.CustomerAccountNumber] = metadata.ReservationContext.AgentMetadata.AccountCode;

            var customCodesList = await _tenantCustomCodesRepository.GetAsync(metadata.TenantId);

            Dictionary<string, Dictionary<string, string>> customCodes = MergeCustomCodes(customCodesList);

            //Override accountCode field by old accountCode
            var oldOrNewAccountCode = metadata.ReservationContext.AgentMetadata.AccountCode;
            if (customCodes.TryGetValue("OldAccountCode", out Dictionary<string, string> oldAccountCodes)
                && oldAccountCodes.TryGetValue(metadata.ReservationContext?.AgentMetadata?.AccountCode ?? string.Empty, out string oldAccountCode))
            {
                oldOrNewAccountCode = oldAccountCode;
            }

            if (customCodes.TryGetValue("InterCompany", out Dictionary<string, string> interCompany))
            {
                if (interCompany.TryGetValue(metadata.ReservationContext?.HomeSource?.Country ?? string.Empty, out string homeInterCompanyNumber))
                {
                    gmtMetadata[GmtFields.HomeInterCompanyNumber] = homeInterCompanyNumber;
                }
                else
                {
                    _logger.Error($"{GmtFields.HomeInterCompanyNumber} not found for {metadata.ReservationContext?.HomeSource?.Country}");
                }

                if (interCompany.TryGetValue(metadata.ReservationContext?.TargetSource?.Country ?? string.Empty, out string targetInterCompanyNumber))
                {
                    gmtMetadata[GmtFields.TargetInterCompanyNumber] = targetInterCompanyNumber;
                }
                else
                {
                    _logger.Error($"{GmtFields.TargetInterCompanyNumber} not found for {metadata.ReservationContext?.TargetSource?.Country}");
                }

                if (metadata.ReservationContext?.TargetSource?.FullSource == metadata.ReservationContext?.HomeSource?.FullSource)
                {
                    gmtMetadata[GmtFields.AIAN] = oldOrNewAccountCode;
                    gmtMetadata[GmtFields.CU] = metadata.ReservationContext?.AgentMetadata.AccountCode;
                }
                else if (metadata.ReservationContext?.TargetSource?.Country == "PH")
                {
                    var interCompanyKey = $"PH{metadata.ReservationContext?.HomeSource?.Country}";

                    if (interCompany.TryGetValue(interCompanyKey, out string interCompanyNumber))
                    {
                        gmtMetadata[GmtFields.InterCompanyNumber] = interCompanyNumber;
                    }
                    else
                    {
                        _logger.Error("InterCompanyNumber not found for " + interCompanyKey);
                    }

                    gmtMetadata[GmtFields.AIAN] = interCompanyNumber;
                    gmtMetadata[GmtFields.CU] = homeInterCompanyNumber;
                }
                else
                {
                    gmtMetadata[GmtFields.AIAN] = oldOrNewAccountCode;
                    gmtMetadata[GmtFields.CU] = homeInterCompanyNumber;
                }
            }
            else
            {
                _logger.Error("InterCompany Codes was not found");
            }

            if (customCodes.TryGetValue("SupplierNumber", out Dictionary<string, string> supplierNumber))
            {
                if (supplierNumber.TryGetValue(metadata.ReservationContext?.TargetSource?.Country ?? string.Empty, out string targetSupplierNumber))
                {
                    gmtMetadata[GmtFields.SupplierNumber] = targetSupplierNumber;
                }
                else
                {
                    _logger.Error($"{GmtFields.SupplierNumber} not found for {metadata.ReservationContext?.TargetSource?.Country}");
                }
            }
            else
            {
                _logger.Error("SupplierNumber Codes was not found");
            }

            return gmtMetadata;
        }

        private static Dictionary<string, Dictionary<string, string>> MergeCustomCodes(List<TenantCustomCodes> customCodesList)
        {
            var customCodes = new Dictionary<string, Dictionary<string, string>>();
            foreach (var tenantCodes in customCodesList)
            {
                if (tenantCodes.Codes == null)
                {
                    continue;
                }

                foreach (var codesGroup in tenantCodes.Codes)
                {
                    if (customCodes.ContainsKey(codesGroup.Key))
                    {
                        foreach (var item in codesGroup.Value)
                        {
                            customCodes[codesGroup.Key][item.Key] = item.Value;
                        }
                    }
                    else
                    {
                        customCodes[codesGroup.Key] = codesGroup.Value;
                    }
                }
            }

            return customCodes;
        }

        private static string GetHomeSourceCode(CustomMetadataRequest metadata)
        {
            switch (metadata.ReservationContext.HomeSource.Country)
            {
                case "FR": return "VSMC";
                case "US": return "VSFL";
                default: return $"VS{metadata.ReservationContext.HomeSource.Country}";
            }
        }

        private static string GetPaxName(PassengerInfo passenger)
        {
            var firstName = passenger.FirstName?.ToUpper();
            var lastName = passenger.LastName?.ToUpper();

            if (!passenger.SingleNameOnly)
            {
                if (passenger.Gender == "M")
                {
                    firstName += "/" + lastName + " " + NamePrefixes.MALE;
                }
                else if (passenger.Gender == "F")
                {
                    firstName += "/" + lastName + " " + NamePrefixes.FEMALE;
                }
            }
            return firstName;
        }
    }

    public static class GmtFields
    {
        public static string AIAN => nameof(AIAN);
        public static string CU => nameof(CU);
        public static string InterCompanyNumber => nameof(InterCompanyNumber);
        public static string HomeInterCompanyNumber => nameof(HomeInterCompanyNumber);
        public static string TargetInterCompanyNumber => nameof(TargetInterCompanyNumber);
        public static string SupplierNumber => nameof(SupplierNumber);
        public static string HomeCompanyCode => nameof(HomeCompanyCode);
        public static string CustomerAccountNumber => nameof(CustomerAccountNumber);
        public static string CeilingTotalPrice => nameof(CeilingTotalPrice);
        public static string BaseTicketPrice => nameof(BaseTicketPrice);
        public static string PaxName => nameof(PaxName);
        /// <summary>
        /// Markup + Sea markup + Classdrop in target currency (called by GMT hidden fees)
        /// </summary>
        public static string TargetPriceMarginHidden => nameof(TargetPriceMarginHidden);
        /// <summary>
        /// Sea markup + Classdrop in target currency
        /// </summary>
        public static string TargetPriceMargin => nameof(TargetPriceMargin);

    }

    public static class NamePrefixes
    {
        public const string MALE = "MR";

        public const string FEMALE = "MRS";
    }
}
