using CTeleport.Services.CustomFields.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CTeleport.Services.CustomFields.Repositories.Interfaces
{
    public interface ICustomFieldTemplateRepository
    {
        Task AddAsync(CustomFieldTemplate item);
        Task<List<CustomFieldTemplate>> GetBySourceAsync(string source, string plattingCarrier);
        Task<List<CustomFieldTemplate>> GetByTenantAsync(string tenantId);
    }
}