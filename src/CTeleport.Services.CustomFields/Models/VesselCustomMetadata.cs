using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Text;

namespace CTeleport.Services.CustomFields.Models
{
    public class VesselCustomMetadata
    {
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        public string Tenant { get; set; }
        public string VesselName { get; set; }
        public Dictionary<string, string> MetaData { get; set; }
    }
}
