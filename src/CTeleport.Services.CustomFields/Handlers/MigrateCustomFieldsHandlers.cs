using CTeleport.Common.Messaging.Services;
using CTeleport.Services.CustomFields.Messages;
using CTeleport.Services.CustomFields.Services.Interfaces;
using Serilog;
using System.Threading.Tasks;

namespace CTeleport.Services.CustomFields.Handlers;

public class MigrateCustomFieldsHandlers : ICommandHandler<MigrateCustomFields>, ICommandHandler<RevertMigrationCustomFields>
{
    private readonly ILogger _logger;
    private readonly IHandlerFactory _handlerFactory;
    private readonly ICustomFieldsMigrationService _service;

    public MigrateCustomFieldsHandlers(
        ILogger logger,
        IHandlerFactory handlerFactory,
        ICustomFieldsMigrationService service)
    {
        _logger = logger;
        _handlerFactory = handlerFactory;
        _service = service;
    }

    public Task HandleAsync(MigrateCustomFields command)
    {
        _logger.Debug("Handling {@command}", command);

        return _handlerFactory.Create(command)
            .Run(async () =>
            {
                await _service.MigrateAsync();
            })
            .OnError(ex => _logger.Error(ex, "Custom fields migration failed"))
            .OnSuccess(async () => _logger.Information("Custom fields migration successful"))
            .ExecuteAsync();
    }

    public Task HandleAsync(RevertMigrationCustomFields command)
    {
        _logger.Debug("Handling {@command}", command);

        return _handlerFactory.Create(command)
            .Run(async () =>
            {
                await _service.RevertAsync();
            })
            .OnError(ex => _logger.Error(ex, "Custom fields revert migration failed"))
            .OnSuccess(async () => _logger.Information("Custom fields revert migration successful"))
            .ExecuteAsync();
    }
}
