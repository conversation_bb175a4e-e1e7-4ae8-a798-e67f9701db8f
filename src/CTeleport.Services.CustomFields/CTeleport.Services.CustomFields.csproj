<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\CTeleport.Services.Settings\CTeleport.Services.Settings.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.1.1" />
    <PackageReference Include="AutoMapper" Version="10.1.1" />
    <PackageReference Include="CTeleport.Authorization" Version="1.1.200" />
    <PackageReference Include="CTeleport.Common.Messaging" Version="1.1.293" />
    <PackageReference Include="CTeleport.Messages" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Messages.Abstractions" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Services.Booking.Core" Version="1.1.174" />    
    <PackageReference Include="CTeleport.Services.Booking.Shared" Version="1.1.124" />
    <PackageReference Include="CTeleport.Services.VesselsManagement.Contracts" Version="1.0.2.145" />
    <PackageReference Include="Microsoft.FeatureManagement" Version="3.1.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NodaTime" Version="2.4.0" />
    <PackageReference Include="SmartFormat.NET" Version="2.0.0" />
  </ItemGroup>

</Project>
