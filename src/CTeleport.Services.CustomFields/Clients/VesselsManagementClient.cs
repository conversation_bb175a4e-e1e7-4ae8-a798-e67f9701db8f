using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.CustomFields.Clients.Interfaces;
using CTeleport.Services.VesselsManagement.Contracts;
using CTeleport.Services.VesselsManagement.Contracts.Models;
using Polly;
using Polly.Wrap;
using RestEase;
using Serilog;

namespace CTeleport.Services.CustomFields.Clients;

public class VesselsManagementClient : IVesselsManagementClient
{
    private readonly IVesselsManagementApi _api;
    private readonly ILogger _logger;

    public VesselsManagementClient(IVesselsManagementApi api, ILogger logger)
    {
        _api = api;
        _logger = logger;
    }
    
    private AsyncPolicyWrap<T> GetResiliencePolicy<T>()
    {
        var notFoundPolicy = Policy<T>
            .Handle<Exception>(e => (e as ApiException)?.StatusCode == HttpStatusCode.NotFound)
            .FallbackAsync(_ => Task.FromResult(default(T)));

        return notFoundPolicy.WrapAsync(Policy<T>
            .Handle<Exception>(e => (e as ApiException)?.StatusCode != HttpStatusCode.NotFound)
            .WaitAndRetryAsync(2, retryAttempt => TimeSpan.FromMilliseconds(500 * retryAttempt),
                (result, _, context) =>
                {
                    _logger.Warning(result.Exception, "Exception {ExceptionType} on {RetryCount} retry count",
                        result.Exception.GetType().Name, context.Count);
                }));
    }

    public async Task<IReadOnlyCollection<VesselCustomMetadata>> GetVesselCustomMetadataAsync(string vesselName, string tenantId, CancellationToken ct)
    {
        return await GetResiliencePolicy<IReadOnlyCollection<VesselCustomMetadata>>().ExecuteAsync(async () => await _api.GetVesselCustomMetadataAsync(vesselName, tenantId, ct));
    }

    public async Task<IReadOnlyCollection<VesselAgentCode>> GetVesselAgentCodesAsync(string vesselName, string tenantId, CancellationToken ct)
    {
        return await GetResiliencePolicy<IReadOnlyCollection<VesselAgentCode>>().ExecuteAsync(async () => await _api.GetVesselAgentCodesAsync(vesselName, tenantId, ct));
    }

    public async Task<FleetAgentCode> GetFleetCodesAsync(string tenantId, CancellationToken ct)
    {
        return await GetResiliencePolicy<FleetAgentCode>().ExecuteAsync(async () => await _api.GetFleetCodesAsync(tenantId, ct));
    }
}