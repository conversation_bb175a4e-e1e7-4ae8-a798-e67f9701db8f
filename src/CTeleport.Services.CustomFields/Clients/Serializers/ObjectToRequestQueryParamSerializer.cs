using RestEase;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CTeleport.Services.CustomFields.Clients.Serializers;

internal class ObjectToRequestQueryParamSerializer : RequestQueryParamSerializer
{
    public override IEnumerable<KeyValuePair<string, string>> SerializeQueryParam<T>(string name, T value, RequestQueryParamSerializerInfo info)
    {
        foreach (var property in value.GetType().GetProperties(BindingFlags.Public | BindingFlags.Static | BindingFlags.Instance))
        {
            var propertyValue = Convert.ToString(property.GetValue(value));

            if (!string.IsNullOrEmpty(propertyValue))
            {
                yield return new KeyValuePair<string, string>(property.Name, propertyValue);
            }
        }
    }
}