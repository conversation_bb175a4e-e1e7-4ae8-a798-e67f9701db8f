using System;
using System.Collections.Generic;

namespace CTeleport.Services.CustomFields.Dto.LegacyPublicContract;

public class TenantCustomFieldsGroupDto
{
    public string Id { get; set; }
    public string Tenant { get; set; }
    public bool ShowCommentField { get; set; }
    public bool ShowContactsFields { get; set; }
    public List<TenantCustomFieldDto> CustomFields { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}