using System.Collections.Generic;

namespace CTeleport.Services.CustomFields.Dto.CustomFieldsClient;

//public class ModifyTenantCustomFieldsGroupRequestDto
//{
//    public string TenantWildcard { get; set; }

//    public TenantCustomBasicFieldsGroupDto BasicFields { get; set; } = new TenantCustomBasicFieldsGroupDto();

//    public IReadOnlyCollection<TenantCustomFieldDto> CustomFields { get; set; } = new List<TenantCustomFieldDto>();
//}
