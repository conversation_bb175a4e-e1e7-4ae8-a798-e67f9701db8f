using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.FareTermsMerger;
using CTeleport.Services.FareTermsMerger.DTOs;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;

public record TermsEntity
{
    public bool CanCancel { get; init; }
    public FareType FareType { get; init; } = FareType.Unknown;
    public RefundCondition Cancellations { get; init; } = RefundCondition.Unknown;
    public ChangeCondition Changes { get; init; } = ChangeCondition.Unknown;

    public static TermsEntity New(TermsEntity terms)
        => terms with { };

    public static TermsEntity MergeTerms(TermsEntity left, TermsEntity right)
        => ToTermsEntity(FareConditionMerger.MergeFareTerms(new[]
        {
            left.ToFareTermsDto(),
            right.ToFareTermsDto()
        }));

    private static TermsEntity ToTermsEntity(FareTermsDto terms)
        => new()
        {
            FareType = FareType.ToFareType(terms.FareType),
            Cancellations = RefundCondition.ToRefundCondition(terms.Cancellations),
            Changes = ChangeCondition.ToChangeCondition(terms.Changes),
            CanCancel = terms.CanCancel
        };

    private FareTermsDto ToFareTermsDto()
        => new()
        {
            FareType = FareType.ToFareTypeExt(FareType),
            Cancellations = RefundCondition.ToRefundConditionExt(Cancellations),
            Changes = ChangeCondition.ToChangeConditionExt(Changes),
            CanCancel = CanCancel
        };
}