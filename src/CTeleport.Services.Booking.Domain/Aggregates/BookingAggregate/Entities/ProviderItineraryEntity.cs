namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;

public record ProviderItineraryEntity
{
    public IReadOnlyCollection<ItineraryEntity> Legs { get; init; } = Array.Empty<ItineraryEntity>();

    public string? Origin => FirstSegment?.Origin;
    public string? Destination => LastSegment?.Destination;
    public long? DepartureTimestampUtc => FirstSegment?.DepartureTimestampUtc;
    public long? ArrivalTimestampUtc => LastSegment?.ArrivalTimestampUtc;

    private FlightSegmentEntity? FirstSegment => Legs.FirstOrDefault()?.Segments.FirstOrDefault();
    private FlightSegmentEntity? LastSegment => Legs.LastOrDefault()?.Segments.LastOrDefault();

    public static ProviderItineraryEntity New(ProviderItineraryEntity providerItinerary)
        => new()
        {
            Legs = providerItinerary.Legs
                .Select(ItineraryEntity.New)
                .ToImmutableList()
        };
}