using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.DomainEvents;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Requests;

public record InitializeReservationRequest
{
    public string ReservationId { get; init; } = Unknown;
    public string? OriginalReservationId { get; init; }
    public string? OriginalLocator { get; init; }
    public bool IsTicketedInstantly { get; init; }
    public string? FundingSource { get; init; }
    public TermsEntity Terms { get; init; } = new();
    public PriceEntity Price { get; init; } = new();
    public ProviderItineraryEntity ProviderItinerary { get; init; } = new();
    public IReadOnlyCollection<AncillaryEntity> Ancillaries { get; init; } = Array.Empty<AncillaryEntity>();

    public ReservationInitialized ToEvent(Booking aggregate)
        => new(aggregate)
        {
            ReservationId = ReservationId,
            OriginalReservationId = OriginalReservationId,
            OriginalLocator = OriginalLocator,
            IsTicketedInstantly = IsTicketedInstantly,
            FundingSource = FundingSource,
            Terms = Terms,
            Price = Price,
            ProviderItinerary = ProviderItinerary,
            Ancillaries = Ancillaries
        };
}