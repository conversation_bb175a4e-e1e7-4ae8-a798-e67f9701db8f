using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class ReservationAttachmentSpec : Specification<ReservationEntity>
{
    protected internal override string ErrorMessage => "Reservation is removed";

    protected internal override Expression<Func<ReservationEntity, bool>> ToExpression()
        => r => !r.IsRemoved;
}