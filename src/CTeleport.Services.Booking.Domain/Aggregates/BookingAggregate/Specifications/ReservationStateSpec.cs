using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Enums;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class ReservationStateSpec : Specification<ReservationEntity>
{
    private readonly ReservationState[] _states;

    public ReservationStateSpec(params ReservationState[] states)
        => _states = states;

    private string States => string.Join(InnerStringSeparator, _states);

    protected internal override string ErrorMessage => $"Invalid reservation state, expected: {States}";

    protected internal override Expression<Func<ReservationEntity, bool>> ToExpression()
        => r => _states.Contains(r.State);
}