using System.Linq.Expressions;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.SeedWork;

namespace CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Specifications;

public class BookingApprovalSpec : Specification<BookingEntity>
{
    protected internal override string ErrorMessage => "Booking must be in the approval process";

    protected internal override Expression<Func<BookingEntity, bool>> ToExpression()
        => b => b.IsApprovalRequired &&
                !b.ApprovedBy.Any();
}