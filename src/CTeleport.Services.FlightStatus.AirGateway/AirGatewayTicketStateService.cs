using CTeleport.Services.AirGateway.Clients;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Search.Shared;
using Serilog;

namespace CTeleport.Services.FlightStatus.AirGateway
{
    public class AirGatewayTicketStateService : IProviderTicketStateService
    {
        private readonly IAirGatewayClient _airGatewayClient;
        private readonly ILogger _logger;

        public AirGatewayTicketStateService(IAirGatewayClient airGatewayClient, ILogger logger)
        {
            _airGatewayClient = airGatewayClient;
            _logger = logger;
        }

        public async Task<ProviderRetrieveTicketResponse> GetTicketInfoAsync(ProviderRetrieveTicketRequest request)
        {
            try
            {
                _logger.Debug("[AirGatewayTicketStateService] Requesting Ticket state for locator: {@locator}",
                    request.Locators);
                return await _airGatewayClient.GetTicketStateAsync(request);
            }
            catch (Exception e)
            {
                _logger.Error(e, "Error getting ticket state from AirGateway in GetTicketInfoAsync()");
                return new ProviderRetrieveTicketResponse().WithError(ErrorCodes.Error, "error getting ticket state from provider");
            }
        }

        public async Task<RefundTicketResponse> GetTicketRefundInfoAsync(RefundTicketRequest request)
        {
            try
            {
                _logger.Debug("[AirGatewayTicketStateService] Requesting Ticket refund info for locator: {@locator}",
                    request.Locators);
                return await _airGatewayClient.GetTicketRefundInfoAsync(request);
            }
            catch (Exception e)
            {
                _logger.Error(e, "Error getting refunded ticket info from AirGateway in GetTicketRefundInfoAsync()");
                return new RefundTicketResponse().WithError(ErrorCodes.Error, "error getting ticket refund info from provider");
            }
        }
    }
}
