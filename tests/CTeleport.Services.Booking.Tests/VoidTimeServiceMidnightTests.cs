using System;
using System.Collections.Generic;
using CTeleport.Common.Helpers;
using CTeleport.Services.VoidCalc.Configuration;
using CTeleport.Services.VoidCalc.Interfaces;
using CTeleport.Services.VoidCalc.Services;
using CTeleport.Services.VoidCalc.Services.Carriers;
using Moq;
using NodaTime;
using Serilog.Core;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class VoidTimeServiceMidnightTests
    {
        static readonly VoidRule VoidRuleMidnight = VoidRule.SameDayMidnight;

        static readonly VoidOptions options = new VoidOptions()
        {
            VoidSafetyTime = 60,
            CustomVoidRules = new Dictionary<string, VoidRule>()
            {
                { "1G.55EL", VoidRule.SameDayMidnight }
            }
        };

        private readonly string _source = "1G.55EL";
        private readonly string _carrier = "HX";
        private readonly HashSet<string> _marketingCarriers = new HashSet<string> { "HX" };

        static readonly DateTimeZone PCC_TZ = DateTimeZoneProviders.Tzdb["Europe/Riga"];
        private readonly Mock<ISourceTimeZoneResolverService> _timezoneResolverMock;
        private readonly VoidTimeService _service;

        public VoidTimeServiceMidnightTests()
        {
            _timezoneResolverMock = new Mock<ISourceTimeZoneResolverService>();

            _timezoneResolverMock
                .Setup(r => r.GetSourceTimeZone(It.IsAny<string>()))
                .Returns(() => PCC_TZ);

            var timeMock = new Mock<DateTimeProvider>();
            timeMock.SetupGet(tp => tp.UtcNow).Returns(DateTime.UtcNow);
            var converter = new PccDateTimeConverter(_timezoneResolverMock.Object, timeMock.Object);
            
            var voidTimeCalculator = new VoidTimeCalculator(options);
            
            IVoidTimeResolver[] resolvers = 
            [
                new SameDayMidnightVoidTimeResolver(voidTimeCalculator),
                new DayBeforeDepartureResolver(voidTimeCalculator, options),
                new BeforeDepartureVoidTimeResolver(voidTimeCalculator),
                new AeroflotVoidTimeResolver(voidTimeCalculator),
                new IssueDateTimeAsVoidTimeResolver(),
                new VoidOnDepartureDayNotPermittedResolver(voidTimeCalculator)
            ];
            
            Random.Shared.Shuffle(resolvers);

            _service = new VoidTimeService(_timezoneResolverMock.Object, options, converter, voidTimeCalculator, resolvers, Logger.None);
        }


        [Fact]
        public void CalcLastVoidTime_Returns_1H_Before_Midnight_in_RIX()
        {
            var issueAt = new DateTime(2017, 9, 29, 22, 0, 0, DateTimeKind.Utc); // ticket issue on 30 Sep at 1:00 AM in RIX
            var departureAt = new DateTime(2017, 10, 29, 9, 10, 0, DateTimeKind.Utc);
            var dt = _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt);

            var voidAt = new DateTime(2017, 9, 30, 20, 0, 0, DateTimeKind.Utc); // void is possible until 11:00PM in RIX
            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_3H_BeforeDeparture()
        {
            var issueAt = new DateTime(2017, 9, 28, 22, 0, 0, DateTimeKind.Utc);
            var departureAt = new DateTime(2017, 9, 29, 9, 10, 0, DateTimeKind.Utc);
            var dt = _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt);

            var voidAt = new DateTime(2017, 9, 29, 6, 10, 0, DateTimeKind.Utc);
            Assert.Equal(voidAt, dt);
        }

        [Fact]
        public void CalcLastVoidTime_Throws_AgrumentException_for_non_Utc_issueAt_Datetime()
        {
            var issueAt = new DateTime(2017, 5, 11, 14, 25, 37);
            var departureAt = new DateTime(2017, 6, 11, 13, 25, 00, DateTimeKind.Utc);

            Assert.Throws<ArgumentException>(() => _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt));
        }

        [Fact]
        public void CalcLastVoidTime_Throws_AgrumentException_for_non_Utc_departureAt_Datetime()
        {
            var issueAt = new DateTime(2017, 5, 11, 14, 25, 37, DateTimeKind.Utc);
            var departureAt = new DateTime(2017, 6, 11, 13, 25, 00);

            Assert.Throws<ArgumentException>(() => _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt));
        }

        [Fact]
        public void CalcLastVoidTime_Returns_Utc_DateTime()
        {
            var issueAt = DateTime.UtcNow;
            var departureAt = DateTime.UtcNow.AddMonths(3);

            var lastVoidAt = _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt);

            Assert.Equal(DateTimeKind.Utc, lastVoidAt.Kind);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_1H_Before_Midnight_in_RIX_SummerTime()
        {
            var issueAt = new DateTime(2017, 5, 11, 14, 25, 37, DateTimeKind.Utc);
            var departureAt = new DateTime(2017, 6, 5, 10, 0, 0, DateTimeKind.Utc);

            var lastVoidAt = _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt);

            Assert.Equal(issueAt.Year, lastVoidAt.Year);
            Assert.Equal(issueAt.Month, lastVoidAt.Month);
            Assert.Equal(issueAt.Day, lastVoidAt.Day);
            Assert.Equal(20, lastVoidAt.Hour); // NOTE: 20:00 UTC is 23:00 in RIX (summer time)
            Assert.Equal(0, lastVoidAt.Minute);
            Assert.Equal(0, lastVoidAt.Second);
        }

        [Fact]
        public void CalcLastVoidTime_Returns_1H_Before_Midnight_in_RIX_WinterTime()
        {
            var issueAt = new DateTime(2017, 11, 1, 14, 25, 37, DateTimeKind.Utc);
            var departureAt = new DateTime(2017, 11, 5, 10, 0, 0, DateTimeKind.Utc);

            var lastVoidAt = _service.CalculateLastVoidTime(_source, _carrier, departureAt, _marketingCarriers, issueAt);

            Assert.Equal(issueAt.Year, lastVoidAt.Year);
            Assert.Equal(issueAt.Month, lastVoidAt.Month);
            Assert.Equal(issueAt.Day, lastVoidAt.Day);
            Assert.Equal(21, lastVoidAt.Hour); // NOTE: 21:00 UTC is 23:00 in RIX (winter time)
            Assert.Equal(0, lastVoidAt.Minute);
            Assert.Equal(0, lastVoidAt.Second);
        }
    }
}
