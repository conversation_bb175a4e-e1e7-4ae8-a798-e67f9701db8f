using CTeleport.Common.Exceptions.Interfaces;
using CTeleport.Common.Helpers;
using CTeleport.Common.Messaging.Services;
using CTeleport.Common.Redis;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Handlers;
using CTeleport.Services.Booking.Services;
using Moq;
using Serilog;
using System;
using System.Threading.Tasks;
using Xunit;
using User = CTeleport.Messages.Commands.Models.User;

namespace CTeleport.Services.Booking.Tests
{
    public class SyncReservationHandlerTests
    {
        private readonly IHandlerFactory _handlerFactory;
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<IMessageDispatcher> _dispatcherMock;

        public SyncReservationHandlerTests()
        {
            _dispatcherMock = new Mock<IMessageDispatcher>();
            _loggerMock = new Mock<ILogger>();
            _loggerMock.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(() => _loggerMock.Object);
            _handlerFactory = new HandlerFactory(new Mock<IExceptionHandler>().Object, new Mock<ILockFactory>().Object);
        }

        [Fact]
        public async Task Sync_Successfully_PublishReservationSyncCompleted()
        {
            var syncReservationServiceMock = new Mock<ISyncReservationService>();
            syncReservationServiceMock
                .Setup(s => s.SyncReservation(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), FareChangeReason.FareUpdate, It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            var handler = CreateHandler(syncReservationServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), Id.New(), false, true);
            command.ForceSegments = true;

            await handler.HandleAsync(command);

            //assert
            _dispatcherMock.Verify(b => b.DispatchAsync(It.Is<ReservationSyncCompleted>(e => e.RequestId == command.Request.Id && e.ReservationId == command.ReservationId)),
                Times.Exactly(1));
        }

        [Fact]
        public async Task Sync_Exception_PublishReservationSyncRejected()
        {
            var syncReservationServiceMock = new Mock<ISyncReservationService>();
            syncReservationServiceMock
                .Setup(s => s.SyncReservation(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), It.IsAny<bool>(), FareChangeReason.FareUpdate, It.IsAny<string>()))
                .Returns(() => throw new Exception());

            var handler = CreateHandler(syncReservationServiceMock.Object);
            var command = CreateSyncReservationCommand(Id.New(), Id.New(), false, true);
            command.ForceSegments = true;

            await handler.HandleAsync(command);

            //assert
            _dispatcherMock.Verify(b => b.DispatchAsync(It.Is<ReservationSyncRejected>(e => e.RequestId == command.Request.Id && e.ReservationId == command.ReservationId)),
                Times.Exactly(1));
        }

        #region helpers

        private ICommandHandler<SyncReservation> CreateHandler(ISyncReservationService syncReservationService)
        {
            return new SyncReservationHandler(
                syncReservationService,
                _handlerFactory,
                _dispatcherMock.Object,
                _loggerMock.Object);
        }

        private SyncReservation CreateSyncReservationCommand(string requestId, string reservationId, bool syncFare = false, bool syncSegments = false)
            => new SyncReservation
            {
                Request = Request.New<SyncReservation>(requestId),
                ReservationId = reservationId,
                User = new User(),
                SyncFare = syncFare,
                SyncSegments = syncSegments
            };

        #endregion
    }
}
