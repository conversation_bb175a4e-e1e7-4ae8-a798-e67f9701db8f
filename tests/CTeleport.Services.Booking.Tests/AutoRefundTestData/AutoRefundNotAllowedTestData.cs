using System;
using System.Collections.Generic;
using System.Linq;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Constants;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.FareTerms.Shared;
using CTeleport.Services.FareTerms.Shared.Responses;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Core.Dto;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;
using Xunit;

using RetrieveTicketResponse = CTeleport.Services.Booking.Core.Dto.ProviderRetrieveTicketResponse;
using TicketPrice = CTeleport.Services.Booking.Models.TicketPrice;

namespace CTeleport.Services.Booking.Tests.AutoRefundTestData
{
    class AutoRefundNotAllowedTestData : TheoryData
    {
        private const string Currency = "EUR";

        static readonly Dictionary<string, string> Locators = new Dictionary<string, string>
        {
            [LocatorNames.PROVIDER] = "XCHBTG"
        };
        static readonly (string TaxCode, decimal Amount)[] Taxes =
        {
            ("YQ", 0),
            ("YR", 50),
            ("LI", 0),
            ("IT", 0.01m),
            ("EX", 0),
            ("HB", 0),
            ("MJ", 0),
            ("VT", 0),
            ("ND", 0),
            ("D3", 0)
        };
        static readonly Segment[][] Segments =
        {
            new []
            {
                new Segment
                {
                    Origin = "AMS",
                    Destination = "RIX",
                    Carrier = "KL"
                },
                new Segment
                {
                    Origin = "RIX",
                    Destination = "SVO",
                    Carrier = "KL"
                },
            },
            new []
            {
                new Segment
                {
                    Origin = "SVO",
                    Destination = "VVO",
                    Carrier = "SU"
                },
             },
        };

        static readonly Dictionary<string, List<FareRuleSection>> FareRules = new Dictionary<string, List<FareRuleSection>>
        {
            ["AMS-VVO"] = new List<FareRuleSection>
            {
                new FareRuleSection
                {
                    Category = CTeleport.Services.Helpers.FareRules.PenaltiesSection,
                    Title = "Penalties",
                    Text = "Penalties"
                }
            }
        };

        static readonly Ticket ValidTicket = new Ticket
        {
            Number = "5551234567890",
            State = TicketState.Open,
            IssueDate = "2022-12-13",
            NoShow = false,
            Price = new TicketPrice { Taxes = Taxes.ToDictionary(t => t.TaxCode, t => Math.Max(t.Amount, 10)) }
        };

        static readonly List<ConditionsTimespan> FullRefTimeline = new List<ConditionsTimespan>
        {
            new ConditionsTimespan
            {
                Allowed = true,
                Fee = 0,
                Until = int.MaxValue
            }
        };

        static readonly List<ConditionsTimespan> PaidRefTimeline = new List<ConditionsTimespan>
        {
            new ConditionsTimespan
            {
                Allowed = true,
                Until = int.MaxValue
            }
        };

        static readonly List<ConditionsTimespan> NonRefTimeline = new List<ConditionsTimespan>
        {
            new ConditionsTimespan
            {
                Allowed = false,
                Until = int.MaxValue
            }
        };

        ReservationIrregularities Cancelled = new ReservationIrregularities { Cancelled = true };
        ReservationIrregularities Rescheduled = new ReservationIrregularities { Rescheduled = true };

        static readonly Dictionary<string, decimal> EmptyTaxes = new Dictionary<string, decimal>();
        static readonly OnAutoRefundResponse FullRef = new OnAutoRefundResponse
        {
            Fee = decimal.Zero,
            NonRefTaxes = EmptyTaxes
        };

        static readonly RetrieveTicketResponse UnusedTicketResponse = new RetrieveTicketResponse
        {
            Ticket = new ProviderTicket
            {
                Coupons = new List<TicketCoupon>
                {
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.OPEN
                    }
                }
            }
        };

        static readonly RetrieveTicketResponse FullyUsedTicketResponse = new RetrieveTicketResponse
        {
            Ticket = new ProviderTicket
            {
                Coupons = new List<TicketCoupon>
                {
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.USED
                    },
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.USED
                    }
                }
            }
        };

        static readonly RetrieveTicketResponse PartiallyUsedTicketResponse = new RetrieveTicketResponse
        {
            Ticket = new ProviderTicket
            {
                Coupons = new List<TicketCoupon>
                {
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.USED
                    },
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.OPEN
                    }
                }
            }
        };

        static readonly RetrieveTicketResponse LiftedCouponTicketResponse = new RetrieveTicketResponse
        {
            Ticket = new ProviderTicket
            {
                Coupons = new List<TicketCoupon>
                {
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.LFTD
                    },
                    new TicketCoupon
                    {
                        Status = TicketCouponStatus.OPEN
                    }
                }
            }
        };

        static Dictionary<string, decimal> CreateTaxes(int skip, int take) => Taxes.Skip(skip).Take(take).ToDictionary(p => p.TaxCode, p => p.Amount);

        static Reservation CreateReservation(TimeSpan departureIn, FareType fareType, List<ConditionsTimespan> promisedTimeline = null, ReservationIrregularities irregularities = null)
        {
            return new Reservation
            {
                Source = "1G.8WG0",
                State = ReservationState.Active,
                DepartureAt = DateTime.UtcNow.Add(departureIn),
                Locators = Locators,
                LegSegments = Segments,
                FareRules = FareRules,
                Fare = new Shared.Models.Fare {FareType = fareType },
                Price = new ReservationPrice(),
                Tickets = new[] { ValidTicket },
                CancellationTimeline = promisedTimeline,
                Irregularities = irregularities
            };
        }

        public AutoRefundNotAllowedTestData()
        {
            Unused("FareTermsService response has error",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public),
                new OnAutoRefundResponse { Error = new ErrorDescription { ErrorCode = ErrorCode.farerule_not_supported } },
                ErrorCode.farerule_not_supported
            );
            
            Unused("Tax to be refunded partially",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public),
                new OnAutoRefundResponse { NonRefTaxes = CreateTaxes(0, 3) },
                AutoRefundErrorCodes.PartialTaxRefund
            );
            
            Unused("1 hour before departure",
                CreateReservation(TimeSpan.FromHours(1), FareType.Public),
                FullRef,
                AutoRefundErrorCodes.AutorefundCooldownRequired
            );
            
            Unused("47 hours after departure",
                CreateReservation(TimeSpan.FromHours(-47), FareType.Public),
                FullRef,
                AutoRefundErrorCodes.AutorefundCooldownRequired
            );
            
            Unused("Non refundable with irregularities",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, NonRefTimeline, Cancelled),
                new OnAutoRefundResponse { Error = new ErrorDescription { ErrorCode = ErrorCode.farerule_non_refundable } },
                AutoRefundErrorCodes.AutorefundBlockedByIrregularity
            );
            
            Unused("Paid refund with fee and irregularities",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, PaidRefTimeline, Rescheduled),
                new OnAutoRefundResponse { Fee = 50 , NonRefTaxes = EmptyTaxes },
                AutoRefundErrorCodes.AutorefundBlockedByIrregularity
            );
            
            Unused("Paid refund with non-ref taxes and irregularities",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, PaidRefTimeline, Rescheduled),
                new OnAutoRefundResponse { Fee = decimal.Zero, NonRefTaxes = CreateTaxes(0, 1) },
                AutoRefundErrorCodes.AutorefundBlockedByIrregularity
            );
            
            Unused("Paid refund with irregularities while promised full ref",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, FullRefTimeline, Rescheduled),
                new OnAutoRefundResponse { Fee = 50, NonRefTaxes = EmptyTaxes },
                AutoRefundErrorCodes.AutorefundBlockedByIrregularity
            );
            
            Unused("Non refundable while promised full ref",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, FullRefTimeline),
                new OnAutoRefundResponse { Error = new ErrorDescription { ErrorCode = ErrorCode.farerule_non_refundable } },
                AutoRefundErrorCodes.ConditionsAreStricterThanPromised
            );
            
            Unused("Non refundable while promised paid ref",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, PaidRefTimeline),
                new OnAutoRefundResponse { Error = new ErrorDescription { ErrorCode = ErrorCode.farerule_non_refundable } },
                AutoRefundErrorCodes.ConditionsAreStricterThanPromised
            );
            
            Unused("Non refundable",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, NonRefTimeline),
                new OnAutoRefundResponse { Error = new ErrorDescription { ErrorCode = ErrorCode.farerule_non_refundable } },
                ErrorCode.farerule_non_refundable
            );
            
            Unused("Paid refundable while promised full ref",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, FullRefTimeline),
                new OnAutoRefundResponse { Fee = decimal.Zero, NonRefTaxes = CreateTaxes(5, 1) },
                AutoRefundErrorCodes.ConditionsAreStricterThanPromised
            );
            
            Unused($"Auto Refund is prohibited for {FareType.Marine} fare type with predicted rules and unknown fee",
                CreateReservation(TimeSpan.FromDays(2), FareType.Marine, FullRefTimeline),
                new OnAutoRefundResponse { IsPredicted = true, Fee = decimal.Zero, Error = new ErrorDescription { ErrorCode = ErrorCode.farerule_generic_error } },
                AutoRefundErrorCodes.AutorefundProhibitedPredicted
            );

            FullyUsed("Auto Refund is prohibited for fully used ticket",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, FullRefTimeline),
                FullRef,
                AutoRefundErrorCodes.TicketIsFullyUsed
            );

            PartiallyUsed("Auto Refund is prohibited for partially used ticket",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, FullRefTimeline),
                FullRef,
                AutoRefundErrorCodes.TicketIsPartiallyUsed
            );

            LiftedCoupon("Auto Refund is prohibited for ticket with coupon(s) in status Lifted",
                CreateReservation(TimeSpan.FromDays(2), FareType.Public, FullRefTimeline),
                FullRef,
                AutoRefundErrorCodes.TicketIsPartiallyUsed
            );
        }

        void Unused(string name, Reservation reservation, OnAutoRefundResponse fareTermsResponse, string expectedErrorCode)
        {
            AddRow(name, reservation, fareTermsResponse, UnusedTicketResponse, expectedErrorCode);
        }

        void FullyUsed(string name, Reservation reservation, OnAutoRefundResponse fareTermsResponse, string expectedErrorCode)
        {
            AddRow(name, reservation, fareTermsResponse, FullyUsedTicketResponse, expectedErrorCode);
        }

        void PartiallyUsed(string name, Reservation reservation, OnAutoRefundResponse fareTermsResponse, string expectedErrorCode)
        {
            AddRow(name, reservation, fareTermsResponse, PartiallyUsedTicketResponse, expectedErrorCode);
        }

        void LiftedCoupon(string name, Reservation reservation, OnAutoRefundResponse fareTermsResponse, string expectedErrorCode)
        {
            AddRow(name, reservation, fareTermsResponse, LiftedCouponTicketResponse, expectedErrorCode);
        }
    }
}