using System;
using CTeleport.Services.Booking.Models;
using Xunit;

namespace CTeleport.Services.Booking.Tests.AutoRefundTestData
{
    class CanVoidTestData : TheoryData
    {
        public CanVoidTestData()
        {
            var now = DateTime.UtcNow;

            Add(now.AddMinutes(10), true);
            Add(now.AddMinutes(10), false, "TF.CTTEST");
            Add(now, false);
        }

        void Add(DateTime lastVoidAt, bool canVoid, string source = null)
        {
            var reservation = new Reservation { Source = source };
            var ticket = new Ticket { LastVoidAt = lastVoidAt };
            AddRow(reservation, ticket, canVoid);
        }
    }
}
