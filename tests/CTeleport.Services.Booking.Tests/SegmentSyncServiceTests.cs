using AutoFixture;
using AutoFixture.Xunit2;
using AutoMapper;
using CTeleport.Common.Exceptions;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers;
using FluentAssertions;
using FluentAssertions.Extensions;
using Moq;
using Serilog;
using System;
using System.Linq;
using System.Text.RegularExpressions;
using CTeleport.Services.Booking.Api;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class SegmentSyncServiceTests
    {
        private readonly IMapper _mapper = AutoMapperConfig.InitializeMapper();
        private readonly SegmentSyncService _sut;

        public SegmentSyncServiceTests()
        {
            var logger = new Mock<ILogger>();
            logger.Setup(x => x.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(logger.Object);

            _sut = new SegmentSyncService(
                logger.Object,
                _mapper);
        }

        [Fact]
        public void SyncSegments_Should_ThrowServiceException_WhenOneLeg_AndOriginDontMatch()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("MOW-AMS");
            var providerSegments = BuildLegSegments("LED-AMS").SelectMany(l => l).ToArray();
            _sut.Invoking(s => s.SyncSegments(locator, reservationSegments, providerSegments, false))
                .Should().ThrowExactly<ServiceException>();
        }

        [Fact]
        public void SyncSegments_Should_ThrowServiceException_WhenOneLeg_AndDestinationDontMatch()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("MOW-AMS");
            var providerSegments = BuildLegSegments("MOW-RIX").SelectMany(l => l).ToArray();
            _sut.Invoking(s => s.SyncSegments(locator, reservationSegments, providerSegments, false))
                .Should().ThrowExactly<ServiceException>();
        }

        [Fact]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenOneLeg_AndOriginDontMatch_AndForce()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("MOW-AMS");
            var providerSegments = BuildLegSegments("LED-AMS").SelectMany(l => l).ToArray();
            var combinedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, true);
            combinedSegments.Should().HaveCount(1);
            combinedSegments.ElementAt(0).Should().HaveCount(1);
            combinedSegments.ElementAt(0).ElementAt(0).Should().BeEquivalentTo(new {Origin = "LED", Destination="AMS"}, opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenOneLeg_AndDestinationDontMatch_AndForce()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("MOW-AMS");
            var providerSegments = BuildLegSegments("MOW-RIX").SelectMany(l => l).ToArray();
            var combinedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, true);
            combinedSegments.Should().HaveCount(1);
            combinedSegments.ElementAt(0).Should().HaveCount(1);
            combinedSegments.ElementAt(0).ElementAt(0).Should().BeEquivalentTo(new {Origin = "MOW", Destination="RIX"}, opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenReturn_AndOriginDontMatch_AndForce()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("MOW-AMS, AMS-MOW");
            var providerSegments = BuildLegSegments("MOW-AMS, RIX-MOW").SelectMany(l => l).ToArray();
            var combinedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, true);
            combinedSegments.Should().HaveCount(2);
            combinedSegments.ElementAt(0).Should().HaveCount(1);
            combinedSegments.ElementAt(0).ElementAt(0).Should().BeEquivalentTo(new {Origin = "MOW", Destination="AMS"}, opt => opt.ExcludingMissingMembers());
            combinedSegments.ElementAt(1).Should().HaveCount(1);
            combinedSegments.ElementAt(1).ElementAt(0).Should().BeEquivalentTo(new {Origin = "RIX", Destination="MOW"}, opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenReturn_AndDestinationDontMatch_AndForce()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("MOW-AMS, AMS-MOW");
            var providerSegments = BuildLegSegments("MOW-AMS, AMS-LED").SelectMany(l => l).ToArray();
            var combinedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, true);
            combinedSegments.Should().HaveCount(2);
            combinedSegments.ElementAt(0).Should().HaveCount(1);
            combinedSegments.ElementAt(0).ElementAt(0).Should().BeEquivalentTo(new {Origin = "MOW", Destination="AMS"}, opt => opt.ExcludingMissingMembers());
            combinedSegments.ElementAt(1).Should().HaveCount(1);
            combinedSegments.ElementAt(1).ElementAt(0).Should().BeEquivalentTo(new {Origin = "AMS", Destination="LED"}, opt => opt.ExcludingMissingMembers());
        }

        [Theory]
        [InlineAutoData("AMS-MOW", "AMS-MOW")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenProviderHasBrokenSegments(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var brokenSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            foreach (var brokenSegment in brokenSegments)
            {
                brokenSegment.Status = "HX";
            }

            var providerSegments = brokenSegments.Concat(BuildLegSegments(providerItinerary).SelectMany(l => l)).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(1);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("MOW");
        }

        [Theory, AutoData]
        public void SyncSegments_Should_NotContainBrokenSegment_WhenReservationHasBrokenSegments_AndOneExtraStop_AndForceSegments(string locator)
        {
            var reservationSegments = BuildLegSegments("AMS-MOW");
            reservationSegments[0][0].Status = "HX";

            var providerSegments = BuildLegSegments("AMS-RIX-MOW").SelectMany(l => l).ToArray();

            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, true);

            syncedSegments.Should().HaveCount(1);
            syncedSegments.ElementAt(0).Should().NotContain(x => x.Status == "HX");
        }

        [Theory, AutoData]
        public void SyncSegments_Should_ContainBrokenSegment_WhenReservationHasBrokenSegments_AndRouteChanged_AndForce(string locator)
        {
            var reservationSegments = BuildLegSegments("AMS-MOW");
            reservationSegments[0][0].Status = "HX";

            var providerSegments = BuildLegSegments("RIX-LED").SelectMany(l => l).ToArray();

            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, true);

            syncedSegments.Should().HaveCount(1);
            syncedSegments.ElementAt(0).Should().Contain(x => x.Status == "HX");
        }

        [Theory]
        [InlineAutoData("AMS-MOW", "AMS-RIX-MOW")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenOneLeg_AndOneExtraStop(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(1);
            syncedSegments.ElementAt(0).Should().HaveCount(2);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("RIX");
            syncedSegments.ElementAt(0).ElementAt(1).Origin.Should().Be("RIX");
            syncedSegments.ElementAt(0).ElementAt(1).Destination.Should().Be("MOW");
        }

        [Theory]
        [InlineAutoData("AMS-MOW, MOW-AMS", "AMS-MOW, MOW-AMS")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenReturn(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(2);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("MOW");
            syncedSegments.ElementAt(1).Should().HaveCount(1);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("MOW");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("AMS");
        }

        [Theory]
        [InlineAutoData("AMS-MOW", "AMS-MOW")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenOneLeg(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(1);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("MOW");
        }

        [Theory]
        [InlineAutoData("AMS-MOW, MOW-AMS", "AMS-RIX-MOW, MOW-FRA-AMS")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenReturn_AndOneExtraStop(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(2);
            syncedSegments.ElementAt(0).Should().HaveCount(2);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("RIX");
            syncedSegments.ElementAt(0).ElementAt(1).Origin.Should().Be("RIX");
            syncedSegments.ElementAt(0).ElementAt(1).Destination.Should().Be("MOW");
            syncedSegments.ElementAt(1).Should().HaveCount(2);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("MOW");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("FRA");
            syncedSegments.ElementAt(1).ElementAt(1).Origin.Should().Be("FRA");
            syncedSegments.ElementAt(1).ElementAt(1).Destination.Should().Be("AMS");
        }

        [Theory]
        [InlineAutoData("AMS-FRA, FRA-MOW", "AMS-FRA, FRA-MOW")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenMultiLeg(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(2);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("FRA");
            syncedSegments.ElementAt(1).Should().HaveCount(1);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("FRA");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("MOW");
        }

        [Theory]
        [InlineAutoData("AMS-RIX, RIX-MOW", "AMS-FRA-RIX, RIX-LED-MOW")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenMultiLeg_AndOneExtraStop(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(2);
            syncedSegments.ElementAt(0).Should().HaveCount(2);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("FRA");
            syncedSegments.ElementAt(0).ElementAt(1).Origin.Should().Be("FRA");
            syncedSegments.ElementAt(0).ElementAt(1).Destination.Should().Be("RIX");
            syncedSegments.ElementAt(0).Should().HaveCount(2);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("RIX");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("LED");
            syncedSegments.ElementAt(1).ElementAt(1).Origin.Should().Be("LED");
            syncedSegments.ElementAt(1).ElementAt(1).Destination.Should().Be("MOW");
        }

        [Theory]
        [InlineAutoData("AMS-RIX-MOW", "AMS-MOW")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenOneLeg_AndFlightRerouted(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(1);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("MOW");
        }

        [Theory]
        [InlineAutoData("AMS-RIX-MOW, MOW-FRA-AMS", "AMS-MOW, MOW-AMS")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenReturn_AndFlightRerouted(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(2);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("MOW");
            syncedSegments.ElementAt(1).Should().HaveCount(1);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("MOW");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("AMS");
        }

        [Theory]
        [InlineAutoData("AMS-RIX-MOW, MOW-ROV-OVB", "AMS-MOW, MOW-OVB")]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenMultiLeg_AndFlightRerouted(string reservationItinerary, string providerItinerary, string locator)
        {
            var reservationSegments = BuildLegSegments(reservationItinerary);
            var providerSegments = BuildLegSegments(providerItinerary).SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(2);
            syncedSegments.ElementAt(0).Should().HaveCount(1);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("MOW");
            syncedSegments.ElementAt(1).Should().HaveCount(1);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("MOW");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("OVB");
        }

        [Fact]
        public void SyncSegments_Should_ReturnSyncedSegments_WhenMultiCity_AndFlightRerouted()
        {
            var locator = new Fixture().Create<string>();
            var reservationSegments = BuildLegSegments("AMS-SIN-MNL, MNL-SIN, SIN-AMS");
            var providerSegments = BuildLegSegments("AMS-SIN-MNL, MNL-SIN, SIN-AMS").SelectMany(l => l).ToArray();
            var syncedSegments = _sut.SyncSegments(locator, reservationSegments, providerSegments, false);
            syncedSegments.Should().HaveCount(3);
            syncedSegments.ElementAt(0).Should().HaveCount(2);
            syncedSegments.ElementAt(0).ElementAt(0).Origin.Should().Be("AMS");
            syncedSegments.ElementAt(0).ElementAt(0).Destination.Should().Be("SIN");
            syncedSegments.ElementAt(0).ElementAt(1).Origin.Should().Be("SIN");
            syncedSegments.ElementAt(0).ElementAt(1).Destination.Should().Be("MNL");
            syncedSegments.ElementAt(1).Should().HaveCount(1);
            syncedSegments.ElementAt(1).ElementAt(0).Origin.Should().Be("MNL");
            syncedSegments.ElementAt(1).ElementAt(0).Destination.Should().Be("SIN");
            syncedSegments.ElementAt(2).Should().HaveCount(1);
            syncedSegments.ElementAt(2).ElementAt(0).Origin.Should().Be("SIN");
            syncedSegments.ElementAt(2).ElementAt(0).Destination.Should().Be("AMS");
        }

        [Fact]
        public void BuildLegSegments_Should_ReturnOneLeg()
        {
            var segments = BuildLegSegments("AMS-MOW");
            segments[0][0].Origin.Should().Be("AMS");
            segments[0][0].Destination.Should().Be("MOW");
        }

        [Fact]
        public void BuildLegSegments_Should_ReturnOneLeg_WithOneStop()
        {
            var segments = BuildLegSegments("AMS-RIX-MOW");
            segments[0][0].Origin.Should().Be("AMS");
            segments[0][0].Destination.Should().Be("RIX");
            segments[0][1].Origin.Should().Be("RIX");
            segments[0][1].Destination.Should().Be("MOW");
        }

        [Fact]
        public void BuildLegSegments_Should_ReturnOneLeg_WithTwoStops()
        {
            var segments = BuildLegSegments("AMS-RIX-MOW-OVB");
            segments[0][0].Origin.Should().Be("AMS");
            segments[0][0].Destination.Should().Be("RIX");
            segments[0][1].Origin.Should().Be("RIX");
            segments[0][1].Destination.Should().Be("MOW");
            segments[0][2].Origin.Should().Be("MOW");
            segments[0][2].Destination.Should().Be("OVB");
        }

        [Fact]
        public void BuildLegSegments_Should_ReturnTwoLegs()
        {
            var segments = BuildLegSegments("AMS-RIX, RIX-MOW");
            segments[0][0].Origin.Should().Be("AMS");
            segments[0][0].Destination.Should().Be("RIX");
            segments[1][0].Origin.Should().Be("RIX");
            segments[1][0].Destination.Should().Be("MOW");
        }

        [Fact]
        public void BuildLegSegments_Should_ReturnTwoLegs_WithOneStop()
        {
            var segments = BuildLegSegments("AMS-RIX-MOW, MOW-RIX-AMS");
            segments[0][0].Origin.Should().Be("AMS");
            segments[0][0].Destination.Should().Be("RIX");
            segments[0][1].Origin.Should().Be("RIX");
            segments[0][1].Destination.Should().Be("MOW");
            segments[1][0].Origin.Should().Be("MOW");
            segments[1][0].Destination.Should().Be("RIX");
            segments[1][1].Origin.Should().Be("RIX");
            segments[1][1].Destination.Should().Be("AMS");
        }

        /// <summary>
        /// Build leg segments from itinerary in format<br/>
        /// ORIGIN_1[-STOP_1[-STOP_2...]]-DESTINATION_1[, ORIGIN_2...]
        /// </summary>
        /// <param name="itinerary">AMS-MOW, MOW-RIX-AMS</param>
        /// <returns>[[{AMS-MOW}],[{MOW-RIX},{RIX-AMS}]]</returns>
        private static Segment[][] BuildLegSegments(string itinerary)
        {
            var departureStart = new DateTime(2022, 3, 1);
            var legs = itinerary.Split(",").Select(s => s.Trim());
            var legSegments = legs.Select(l => Regex.Matches(l, @"(?=(?<orig>[A-Z]{3})-(?<dest>[A-Z]{3}))").Select(m => new {dest = m.Groups["dest"].Value, orig = m.Groups["orig"].Value}));
            return legSegments.Select(l => l.Select(s => new Segment
            {
                Origin = s.orig,
                Destination = s.dest,
                Status = "HK",
                DepartureDate = (departureStart = departureStart.AddDays(1)).ToString("yyyy-MM-dd"),
                ArrivalDate = departureStart.AddHours(2).ToString("yyyy-MM-dd"),
                DepartureTimestampUtc = departureStart.AsUtc().ToUnixTimeSeconds()
            }).ToArray()).ToArray();
        }
    }
}