using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using Moq;
using CTeleport.Common.Helpers;
using Serilog.Core;

namespace CTeleport.Services.Booking.Tests.Services;

public class ReissueTicketServiceTests
{
     private readonly IReissueTicketService _reissueTicketService;
     
     private readonly Mock<IReservationsRepository> _mockReservationsRepository;
     private readonly Mock<IBookingService> _mockBookingService;
     private readonly Mock<ITicketPriceChangeService> _mockTicketPriceChangeService;
     
     public ReissueTicketServiceTests()
     {
          _mockReservationsRepository = new Mock<IReservationsRepository>();
          _mockBookingService = new Mock<IBookingService>();
          _mockTicketPriceChangeService = new Mock<ITicketPriceChangeService>();
          
          _reissueTicketService = new ReissueTicketService(_mockReservationsRepository.Object,
               _mockBookingService.Object, 
               Logger.None, 
               _mockTicketPriceChangeService.Object);
     }
     
     [Theory]
     [InlineData(100, true)]
     [InlineData(0, false)]
     public async Task SetReissuedTicketAsync_NewTicketNetPriceIsZero_TaxesShouldBeCleared(decimal newTicketPrice, bool isTaxExist)
     {
          // Arrange
          var reissuedTicket = new TicketReissueModel
          {
               ReservationId = Id.New(),
               Price = new Money.Money(newTicketPrice, "USD"),
               OldNumber = "0743589096365",
               IssueAt = DateTime.UtcNow,
               NewNumber = "0743589096366",
               Taxes = new Dictionary<string, decimal>() { { "CJ", 10 } }
          };
          
          var reservation = new Reservation
          {
               Id = reissuedTicket.ReservationId,
               BookingId = Id.New(),
               Tickets = new List<Ticket>
               {
                    new()
                    {
                         Number = reissuedTicket.OldNumber
                    }
               }
          };
          
          _mockReservationsRepository.Setup(x => x.GetAsync(reissuedTicket.ReservationId))
               .ReturnsAsync(reservation);

          _mockBookingService.Setup(x => x.GetBookingAsync(reservation.BookingId))
               .ReturnsAsync(new Models.Booking());    
          
          // Act
          await _reissueTicketService.SetReissuedTicketAsync(reissuedTicket, false);
          
          // Assert   
          _mockTicketPriceChangeService.Verify(x=>x.AddInstantChangesAsync(reissuedTicket, 
               It.IsAny<Reservation>()), 
               Times.Once);
          
          _mockReservationsRepository.Verify(x=> x.AddTicketAsync(reservation.Id, 
                    It.Is<Ticket>(t=> t.Price.Taxes.Any() == isTaxExist), 
                    It.IsAny<DateTime>()), 
               Times.Once);
     }
}