using CTeleport.Common.Helpers;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Services.PnrEventProcessingServices;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers;
using FluentAssertions;
using Moq;
using Serilog;
using ServiceStack.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Messages.Events.Reservations;
using Xunit;

namespace CTeleport.Services.Booking.Tests.Services.PnrEventProcessingServices
{
    public class GalileoPnrEventProcessingServiceTests
    {
        private IPnrEventProcessingService _service;

        private MockRepository _mockRepository;
        private readonly Mock<IProviderReservationInfoService> _providerReservationInfoServiceMock;
        private readonly Mock<IBookingMetricsService> _metricsServiceMock;
        private readonly Mock<IBookingService> _bookingServiceMock;
        private readonly Mock<ILogger> _loggerMock;

        private const string PAX_LASTNAME = "LastName";

        private static readonly int YEAR = DateTime.UtcNow.Year + 1;
        private static readonly Tuple<string, string>[] VENDOR_REMARKS = new Tuple<string, string>[0];

        public GalileoPnrEventProcessingServiceTests()
        {
            _mockRepository = new MockRepository(MockBehavior.Loose);

            _providerReservationInfoServiceMock = _mockRepository.Create<IProviderReservationInfoService>();
            _metricsServiceMock = _mockRepository.Create<IBookingMetricsService>();
            _bookingServiceMock = _mockRepository.Create<IBookingService>();
            _loggerMock = _mockRepository.Create<ILogger>();

            _loggerMock
                .Setup(l => l.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(_loggerMock.Object);
            
            _service = new GalileoPnrEventProcessingService(
                _providerReservationInfoServiceMock.Object,
                _metricsServiceMock.Object,
                _bookingServiceMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectDontSetTicketingTimeAndReturnEmptyEventList_WhenGalileoVendorRemarksSkipsEventWithNoTicketingAt()
        {
            // Arrange
            var reservation = GetReservationWithTicket();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: null,
                paxLastname: PAX_LASTNAME,
                source: null);

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().BeEmpty();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectDontSetTicketingTimeAndReturnEmptyEventList_WhenGalileoVendorRemarksSkipsReservationWithTicket()
        {
            // Arrange
            var reservation = GetReservationWithTicket();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().BeEmpty();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectDontSetTicketingTimeAndReturnEmptyEventList_WhenGalileoVendorRemarksSkipsReservationWithEarlierTicketing()
        {
            // Arrange
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().BeEmpty();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSetTicketingTimeAndReturnTicketingAtUpdatedEvent_WhenGalileoVendorRemarksUpdatesReservationWithTwoHoursBeforeCarrierTTL()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(reservation.Id, new DateTime(YEAR, 1, 1, 10, 0, 0, DateTimeKind.Utc)), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSetTicketingTimeAndReturnTicketingAtUpdatedEvent_WhenGalileoVendorRemarksUpdatesReservationIfScheduledAtTheSameTimeAsCarrierTTL()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 13, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(reservation.Id, new DateTime(YEAR, 1, 1, 11, 0, 0, DateTimeKind.Utc)), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSetTicketingTimeAndReturnTicketingAtUpdatedEvent_WhenGalileoVendorRemarksUpdatesReservationIfScheduledWithinOneHourOfCarrierTTL()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 13, 59, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(reservation.Id, new DateTime(YEAR, 1, 1, 11, 59, 0, DateTimeKind.Utc)), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSetTicketingTimeAndReturnTicketingAtUpdatedEvent_WhenGalileoVendorRemarksUpdatesReservationWithTicketingNow()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.VENDOR_REMARKS,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR - 1, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(reservation.Id, It.Is<DateTime>(v => DateTime.UtcNow.Subtract(v).Minutes < 1)), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSkipScheduleChangeConfirmationAndReturnEventList_WhenGalileoTimeChangedQueueWithReservationStateCancelled()
        {
            // Arrange
            var expectedNumberOfMessages = 2;
            var reservation = GetCancelledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.TIME_CHANGED_QUEUE,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSkipScheduleChangeConfirmationAndReturnEventList_WhenGalileoTimeChangedQueueWithFlightAlreadyDeparted()
        {
            // Arrange
            var expectedNumberOfMessages = 2;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.TIME_CHANGED_QUEUE,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: DateTime.UtcNow.AddDays(-1),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { new Segment() }
                }
            };

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectScheduleChangeConfirmationAndReturnEventList_WhenGalileoTimeChangedQueueWithAllowedToChange()
        {
            // Arrange
            var expectedNumberOfMessages = 3;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.TIME_CHANGED_QUEUE,
                queueTitle: string.Empty,
                departure: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            var segment = new Segment
            {
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)DateTime.UtcNow.AddDays(10).ToUnixTime(),
                ArrivalTimestampUtc = (int)DateTime.UtcNow.AddDays(11).ToUnixTime(),
                Status = "TK"
            };

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { segment }
                }
            };

            reservation.DepartureAt = DateTime.UtcNow.AddDays(10);
            reservation.LegSegments = new List<ICollection<Segment>> { new List<Segment> { segment } };

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectRejectScheduleChangeConfirmationAndReturnEventList_WhenGalileoTimeChangedQueueWithNotAllowedToChange()
        {
            // Arrange
            var expectedNumberOfMessages = 3;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.TIME_CHANGED_QUEUE,
                queueTitle: string.Empty,
                departure: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            var segment = new Segment
            {
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)DateTime.UtcNow.AddDays(10).ToUnixTime(),
                ArrivalTimestampUtc = (int)DateTime.UtcNow.AddDays(11).ToUnixTime()
            };

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { segment }
                }
            };

            reservation.DepartureAt = DateTime.UtcNow.AddDays(10);
            reservation.LegSegments = new List<ICollection<Segment>> { new List<Segment> { segment } };
            reservation.Passenger = new ReservationPassenger { LastName = "last name" };

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectRegisterCancelledSegmentsAndReturnEventList_WhenGalileoUnableToConfirm()
        {
            // Arrange
            var expectedNumberOfMessages = 2;
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: GalileoQueues.UNABLE_TO_CONFIRM,
                queueTitle: string.Empty,
                departure: null,
                vendorRemarks: VENDOR_REMARKS,
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}");

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        private Reservation GetReservationWithTicket()
            => new Reservation
            {
                Id = Id.New(),
                Locators = new Dictionary<string, string> { { LocatorNames.PROVIDER, Id.New() } },
                Tickets = new[] { new Ticket() }
            };

        private Reservation GetScheduledReservation()
            => new Reservation
            {
                Id = Id.New(),
                Locators = new Dictionary<string, string> { { LocatorNames.PROVIDER, Id.New() } },
                TicketingAt = new DateTime(YEAR, 1, 1, 13, 0, 0, DateTimeKind.Utc),
                Source = "1G"
            };

        private Reservation GetCancelledReservation() =>
            new Reservation
            {
                Id = Id.New(),
                State = ReservationState.Cancelled,
                Locators = new Dictionary<string, string> { { LocatorNames.PROVIDER, Id.New() } },
                TicketingAt = new DateTime(YEAR, 1, 1, 13, 0, 0, DateTimeKind.Utc)
            };
    }
}
