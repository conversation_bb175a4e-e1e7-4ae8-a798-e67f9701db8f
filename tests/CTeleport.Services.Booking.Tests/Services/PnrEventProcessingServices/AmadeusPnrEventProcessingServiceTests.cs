using CTeleport.Common.Helpers;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Changes;
using CTeleport.Services.Booking.Constants;
using CTeleport.Services.Booking.Core.Dto;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Services.PnrEventProcessingServices;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Helpers;
using FluentAssertions;
using Moq;
using Serilog;
using ServiceStack.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Messages.Events.Reservations;
using Xunit;

namespace CTeleport.Services.Booking.Tests.Services.PnrEventProcessingServices
{
    public class AmadeusPnrEventProcessingServiceTests
    {
        private IPnrEventProcessingService _service;

        private MockRepository _mockRepository;
        private readonly Mock<IProviderReservationInfoService> _providerReservationInfoServiceMock;
        private readonly Mock<IBookingMetricsService> _metricsServiceMock;
        private readonly Mock<IBookingService> _bookingServiceMock;
        private readonly Mock<ILogger> _loggerMock;

        private const string PAX_LASTNAME = "LastName";

        private static readonly int YEAR = DateTime.UtcNow.Year + 1;
        private static readonly Tuple<string, string>[] VENDOR_REMARKS = new Tuple<string, string>[0];

        public AmadeusPnrEventProcessingServiceTests()
        {
            _mockRepository = new MockRepository(MockBehavior.Loose);

            _providerReservationInfoServiceMock = _mockRepository.Create<IProviderReservationInfoService>();
            _metricsServiceMock = _mockRepository.Create<IBookingMetricsService>();
            _bookingServiceMock = _mockRepository.Create<IBookingService>();
            _loggerMock = _mockRepository.Create<ILogger>();

            _loggerMock
                .Setup(l => l.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(_loggerMock.Object);

            _service = new AmadeusPnrEventProcessingService(
                _providerReservationInfoServiceMock.Object,
                _metricsServiceMock.Object,
                _bookingServiceMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectDontSetTicketingTimeAndReturnEmptyEventList_WhenAmadeusWithTicketingTimeWithReservationHasTickets()
        {
            // Arrange
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: string.Empty,
                queueTitle: string.Empty,
                departure: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                vendorRemarks: new Tuple<string, string>[0],
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.AMADEUS}.{PCCs.CTeleportLV}");

            var segment = new Segment
            {
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)DateTime.UtcNow.AddDays(10).ToUnixTime(),
                ArrivalTimestampUtc = (int)DateTime.UtcNow.AddDays(11).ToUnixTime(),
                Status = "TK"
            };

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { segment }
                }
            };

            reservation.Tickets = new List<Ticket> { new Ticket() };
            reservation.DepartureAt = DateTime.UtcNow.AddDays(10);
            reservation.LegSegments = new List<ICollection<Segment>> { new List<Segment> { segment } };

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().BeEmpty();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectDontSetTicketingTimeAndReturnEmptyEventList_WhenAmadeusWithTicketingTimeSkipReservation()
        {
            // Arrange
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: string.Empty,
                queueTitle: string.Empty,
                departure: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                vendorRemarks: new Tuple<string, string>[0],
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.AMADEUS}.{PCCs.CTeleportLV}");

            var segment = new Segment
            {
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)DateTime.UtcNow.AddDays(10).ToUnixTime(),
                ArrivalTimestampUtc = (int)DateTime.UtcNow.AddDays(11).ToUnixTime(),
                Status = "TK"
            };

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { segment }
                }
            };

            reservation.DepartureAt = DateTime.UtcNow.AddDays(10);
            reservation.LegSegments = new List<ICollection<Segment>> { new List<Segment> { segment } };

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().BeEmpty();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectSetTicketingTimeAndReturnTicketingAtUpdatedEvent_WhenAmadeusWithTicketingTimeWithTicketingTime()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var reservation = GetReservationWithTicket();
            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: string.Empty,
                queueTitle: string.Empty,
                departure: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                vendorRemarks: new Tuple<string, string>[0],
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.AMADEUS}.{PCCs.CTeleportLV}");

            var segment = new Segment
            {
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)DateTime.UtcNow.AddDays(10).ToUnixTime(),
                ArrivalTimestampUtc = (int)DateTime.UtcNow.AddDays(11).ToUnixTime(),
                Status = "TK"
            };

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { segment }
                }
            };

            reservation.Tickets = null;

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectReturnFlightTimeChangedEventsOnly_WhenProcessAmadeusEventWithFlightTimeChanged()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var currentDepartureDate = DateTime.UtcNow.AddHours(5);
            var currentArrivalDate = DateTime.UtcNow.AddHours(10);
            var reservation = GetReservationWithTicket();

            var @event = new PNRPlacedOnQueue(
                locator: reservation.Locators[LocatorNames.PROVIDER],
                queue: AmadeusQueueNumbers.SCHEDULED_CHANGED,
                queueTitle: AmadeusQueueTitles.SCHEDULED_CHANGED_TITLE,
                departure: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                vendorRemarks: new Tuple<string, string>[0],
                ticketingAt: new DateTime(YEAR, 1, 1, 14, 0, 0, DateTimeKind.Utc),
                paxLastname: PAX_LASTNAME,
                source: $"{CTeleport.Services.Helpers.Constants.Providers.AMADEUS}.{PCCs.CTeleportLV}");

            var newDepartureDate = currentDepartureDate.AddHours(5);
            var newArrivalDate = currentArrivalDate.AddHours(5);
            var newSegment = new Segment
            {
                DepartureDate = newDepartureDate.ToString("yyyy-MM-dd"),
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)newDepartureDate.AddHours(5).ToUnixTime(),
                ArrivalTimestampUtc = (int)newArrivalDate.AddHours(5).ToUnixTime(),
                DepartureTime = newDepartureDate.ToShortTimeString(),
                DepartureTimestamp = newDepartureDate.ToUnixTimeSeconds()
            };

            var originalSegment = new Segment
            {
                DepartureDate = currentDepartureDate.ToString("yyyy-MM-dd"),
                Carrier = "BT",
                FlightNumber = "123",
                DepartureTimestampUtc = (int)currentDepartureDate.ToUnixTime(),
                ArrivalTimestampUtc = (int)currentArrivalDate.ToUnixTime(),
                DepartureTime = currentDepartureDate.ToShortTimeString(),
                DepartureTimestamp = currentDepartureDate.ToUnixTimeSeconds()
            };

            var reservationResponse = new ProviderRetrieveReservationResponse
            {
                FlightReservation = new ProviderFlightReservation
                {
                    Locators = new Dictionary<string, string>(),
                    Segments = new[] { newSegment }
                }
            };

            reservation.DepartureAt = DateTime.UtcNow.AddDays(10);
            reservation.LegSegments = new List<ICollection<Segment>> { new List<Segment> { originalSegment } };
            reservation.Passenger = new ReservationPassenger { LastName = "last name" };

            _bookingServiceMock
                .Setup(r => r.GetReservationByLocatorAsync(reservation.Locators[LocatorNames.PROVIDER], PAX_LASTNAME))
                .ReturnsAsync(() => reservation);

            _providerReservationInfoServiceMock
                .Setup(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(reservationResponse);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);

            var resultType = results[0].GetType();
            resultType.Should().Be(typeof(FlightTimeChanged));

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Never);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectReturnFlightChangeAndTicketingAtUpdatedEvent_WhenProcessAmadeusEvent()
        {
            // Arrange
            var expectedNumberOfMessages = 2;
            var currentDepartureDate = DateTime.UtcNow.AddHours(5);
            var currentArrivalDate = DateTime.UtcNow.AddHours(10);

            var bookingId = Id.New();
            var reservation = GetScheduledReservation();
            reservation.BookingId = bookingId;
            reservation.DepartureAt = currentDepartureDate;
            reservation.LegSegments = new List<ICollection<Segment>>
            {
                new List<Segment>
                {
                    new()
                    {
                        DepartureDate = currentDepartureDate.ToString("yyyy-MM-dd"),
                        Carrier = "BT",
                        FlightNumber = "123",
                        DepartureTimestampUtc = (int)currentDepartureDate.ToUnixTime(),
                        ArrivalTimestampUtc = (int)currentArrivalDate.ToUnixTime(),
                        DepartureTime = currentDepartureDate.ToShortTimeString(),
                        DepartureTimestamp = currentDepartureDate.ToUnixTimeSeconds()
                    }
                }
            };

            var newDepartureDate = currentDepartureDate.AddHours(5);
            var newArrivalDate = currentArrivalDate.AddHours(5);
            var newSegments = new List<Segment>
            {
                new()
                {
                    DepartureDate = newDepartureDate.ToString("yyyy-MM-dd"),
                    Carrier = "BT",
                    FlightNumber = "123",
                    DepartureTimestampUtc = (int)newDepartureDate.AddHours(5).ToUnixTime(),
                    ArrivalTimestampUtc = (int)newArrivalDate.AddHours(5).ToUnixTime(),
                    DepartureTime = newDepartureDate.ToShortTimeString(),
                    DepartureTimestamp = newDepartureDate.ToUnixTimeSeconds()
                }
            };

            _providerReservationInfoServiceMock
                .Setup(x => x.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(() => new ProviderRetrieveReservationResponse
                {
                    HasError = false,
                    FlightReservation = new ProviderFlightReservation
                    {
                        Source = "1A",
                        Locators = reservation.Locators,
                        Segments = newSegments
                    }
                });

            var @event = new PNRPlacedOnQueue(
                reservation.Locators[LocatorNames.PROVIDER],
                AmadeusQueueNumbers.SCHEDULED_CHANGED,
                AmadeusQueueTitles.SCHEDULED_CHANGED_TITLE,
                departure: null,
                VENDOR_REMARKS,
                new DateTime(YEAR - 1, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                PAX_LASTNAME,
                CTeleport.Services.Helpers.Constants.Providers.AMADEUS + "." + PCCs.CTeleportLV);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            var flightTimeChangedEvent = results.Last() as FlightTimeChanged;
            flightTimeChangedEvent.Should().NotBeNull();
            flightTimeChangedEvent!.FlightNumber.Should().BeEquivalentTo($"{newSegments.First().Carrier}{newSegments.First().FlightNumber}");

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsyncExpectReturnFlightChangeAndTicketingAtUpdatedEvent_WhenProcessAmadeusEvent2()
        {
            // Arrange
            var expectedNumberOfMessages = 2;
            var currentDepartureDate = DateTime.UtcNow.AddHours(5);
            var currentArrivalDate = DateTime.UtcNow.AddHours(10);

            var bookingId = Id.New();
            var reservation = GetScheduledReservation();
            reservation.BookingId = bookingId;
            reservation.DepartureAt = currentDepartureDate;
            reservation.LegSegments = new List<ICollection<Segment>>
            {
                new List<Segment>
                {
                    new()
                    {
                        DepartureDate = currentDepartureDate.ToString("yyyy-MM-dd"),
                        Carrier = "BT",
                        FlightNumber = "123",
                        DepartureTimestampUtc = (int)currentDepartureDate.ToUnixTime(),
                        ArrivalTimestampUtc = (int)currentArrivalDate.ToUnixTime(),
                        DepartureTime = currentDepartureDate.ToShortTimeString(),
                        DepartureTimestamp = currentDepartureDate.ToUnixTimeSeconds()
                    }
                }
            };

            var newDepartureDate = currentDepartureDate.AddHours(5);
            var newArrivalDate = currentArrivalDate.AddHours(5);
            var newSegments = new List<Segment>
            {
                new()
                {
                    DepartureDate = newDepartureDate.ToString("yyyy-MM-dd"),
                    Carrier = "BT",
                    FlightNumber = "123",
                    DepartureTimestampUtc = (int)newDepartureDate.AddHours(5).ToUnixTime(),
                    ArrivalTimestampUtc = (int)newArrivalDate.AddHours(5).ToUnixTime(),
                    DepartureTime = newDepartureDate.ToShortTimeString(),
                    DepartureTimestamp = newDepartureDate.ToUnixTimeSeconds()
                }
            };

            _providerReservationInfoServiceMock
                .Setup(x => x.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(() => new ProviderRetrieveReservationResponse
                {
                    HasError = false,
                    FlightReservation = new ProviderFlightReservation
                    {
                        Source = "1A",
                        Locators = reservation.Locators,
                        Segments = newSegments
                    }
                });

            var @event = new PNRPlacedOnQueue(
                reservation.Locators[LocatorNames.PROVIDER],
                AmadeusQueueNumbers.SCHEDULED_CHANGED,
                AmadeusQueueTitles.SCHEDULED_CHANGED_TITLE,
                departure: null,
                VENDOR_REMARKS,
                new DateTime(YEAR - 1, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                PAX_LASTNAME,
                CTeleport.Services.Helpers.Constants.Providers.AMADEUS + "." + PCCs.CTeleportLV);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            var flightTimeChangedEvent = results.Last() as FlightTimeChanged;
            flightTimeChangedEvent.Should().NotBeNull();
            flightTimeChangedEvent!.FlightNumber.Should().BeEquivalentTo($"{newSegments.First().Carrier}{newSegments.First().FlightNumber}");

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Once);
            _providerReservationInfoServiceMock.Verify(i => i.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectReturnTicketingAtUpdatedEvent_WhenProcessAmadeusEventWithFlightWasCancelled()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var remarks = new[] { Tuple.Create("Text", "KL TRR KL1446/19OCT FLIGHT CANCELLED / PNR CTC MESSAGE ONGOING 19OCT 0841ZT") };
            var reservation = GetScheduledReservation();
            var @event = new PNRPlacedOnQueue(
                reservation.Locators[LocatorNames.PROVIDER],
                AmadeusQueueNumbers.SCHEDULED_CHANGED,
                AmadeusQueueTitles.SCHEDULED_CHANGED_TITLE,
                departure: null,
                remarks,
                new DateTime(YEAR - 1, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                PAX_LASTNAME,
                CTeleport.Services.Helpers.Constants.Providers.AMADEUS + "." + PCCs.CTeleportLV);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Once);
            _providerReservationInfoServiceMock.Verify(x => x.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Never);
        }

        [Fact]
        public async Task ProcessPNRPlacedOnQueueAsync_ExpectReturnTicketingAtEventEvent_WhenProcessAmadeusEventWithRemarksNull()
        {
            // Arrange
            var expectedNumberOfMessages = 1;
            var reservation = GetScheduledReservation();
            _providerReservationInfoServiceMock
                .Setup(x => x.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()))
                .ReturnsAsync(() => new ProviderRetrieveReservationResponse
                {
                    HasError = true,
                    FlightReservation = new ProviderFlightReservation
                    {
                        Source = "1A",
                        Locators = reservation.Locators,
                        Segments = ArraySegment<Segment>.Empty
                    }
                });

            var @event = new PNRPlacedOnQueue(
                reservation.Locators[LocatorNames.PROVIDER],
                AmadeusQueueNumbers.SCHEDULED_CHANGED,
                AmadeusQueueTitles.SCHEDULED_CHANGED_TITLE,
                departure: null,
                vendorRemarks: null,
                new DateTime(YEAR - 1, 1, 1, 12, 0, 0, DateTimeKind.Utc),
                PAX_LASTNAME,
                CTeleport.Services.Helpers.Constants.Providers.AMADEUS + "." + PCCs.CTeleportLV);

            // Act
            var results = await _service.ProcessPnrEventAsync(@event, reservation);

            // Assert
            results.Should().NotBeNull();
            results.Should().NotBeEmpty();
            results.Should().HaveCount(expectedNumberOfMessages);
            results.First().Should().BeOfType<TicketingAtUpdated>();

            _bookingServiceMock.Verify(i => i.SetTicketingTimeAsync(It.IsAny<string>(), It.IsAny<DateTime>()), Times.Once);
            _providerReservationInfoServiceMock.Verify(x => x.GetReservationAsync(It.IsAny<ProviderRetrieveReservationRequest>()), Times.Once);
        }

        private Reservation GetReservationWithTicket()
            => new Reservation
            {
                Id = Id.New(),
                Locators = new Dictionary<string, string> { { LocatorNames.PROVIDER, Id.New() } },
                Tickets = new[] { new Ticket() }
            };

        private Reservation GetScheduledReservation()
            => new Reservation
            {
                Id = Id.New(),
                Locators = new Dictionary<string, string> { { LocatorNames.PROVIDER, Id.New() } },
                TicketingAt = new DateTime(YEAR, 1, 1, 13, 0, 0, DateTimeKind.Utc),
                Source = "1G"
            };
    }
}
