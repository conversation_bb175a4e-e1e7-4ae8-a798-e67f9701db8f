using System.Collections.Generic;
using System.Threading.Tasks;
using CTeleport.Common.Messaging.Services;
using CTeleport.Messages.Commands.FareRules;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Search.Shared.Models;
using Moq;
using Xunit;

namespace CTeleport.Services.Booking.Tests.Services
{
    public class FareRulesProcessorTests
    {
        private readonly Mock<IMessageDispatcher> _dispatcherMock;
        private readonly FareRulesProcessor _processor;

        public FareRulesProcessorTests()
        {
            _dispatcherMock = new Mock<IMessageDispatcher>();
            _processor = new FareRulesProcessor(_dispatcherMock.Object);
        }

        [Fact]
        public void ProcessFareRules_ShouldUpdateReservationProperties()
        {
            // Arrange
            var reservation = new Reservation();
            var fareRules = new Dictionary<string, List<FareRuleSection>>
            {
                {
                    "RIX-AMS", new List<FareRuleSection>
                    {
                        new FareRuleSection { Category = "1", Text = "Test rule 1" },
                        new FareRuleSection { Category = "16", Text = "Test penalty rule" }
                    }
                }
            };

            // Act
            FareRulesProcessor.ApplyFareRules(reservation, fareRules);

            // Assert
            Assert.Same(fareRules, reservation.FareRules);
            Assert.NotNull(reservation.FareRulesIds);
            Assert.NotNull(reservation.FareRuleCat16Ids);
            Assert.NotEmpty(reservation.FareRulesIds);
            Assert.NotEmpty(reservation.FareRuleCat16Ids);
        }

        [Fact]
        public async Task DispatchFareRuleSectionsAsync_ShouldDispatchMessage()
        {
            // Arrange
            var fareRules = new Dictionary<string, List<FareRuleSection>>
            {
                {
                    "RIX-AMS", new List<FareRuleSection>
                    {
                        new FareRuleSection { Category = "1", Text = "Test rule 1" },
                        new FareRuleSection { Category = "16", Text = "Test penalty rule" }
                    }
                }
            };

            // Act
            await _processor.DispatchFareRuleSectionsAsync(fareRules);

            // Assert
            _dispatcherMock.Verify(
                d => d.DispatchAsync(It.IsAny<ProcessFareRulesSections>()),
                Times.Once);
        }

        [Fact]
        public async Task DispatchFareRuleSectionsAsync_WithNullFareRules_ShouldNotDispatch()
        {
            // Act
            await _processor.DispatchFareRuleSectionsAsync(null);

            // Assert
            _dispatcherMock.Verify(
                d => d.DispatchAsync(It.IsAny<ProcessFareRulesSections>()),
                Times.Never);
        }

        [Fact]
        public async Task DispatchFareRuleSectionsAsync_WithEmptyFareRules_ShouldNotDispatch()
        {
            // Arrange
            var fareRules = new Dictionary<string, List<FareRuleSection>>();

            // Act
            await _processor.DispatchFareRuleSectionsAsync(fareRules);

            // Assert
            _dispatcherMock.Verify(
                d => d.DispatchAsync(It.IsAny<ProcessFareRulesSections>()),
                Times.Never);
        }
    }
}