<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <DebugType>portable</DebugType>
    <AssemblyName>CTeleport.Services.Booking.Tests</AssemblyName>
    <PackageId>CTeleport.Services.Booking.Tests</PackageId>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\CTeleport.Services.Airlines\CTeleport.Services.Airlines.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.ApprovalQueueClient\CTeleport.Services.ApprovalQueueClient.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.Booking.Api\CTeleport.Services.Booking.Api.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.Booking\CTeleport.Services.Booking.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.Cancellation\CTeleport.Services.Cancellation.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.CheckFare.Amadeus\CTeleport.Services.CheckFare.Amadeus.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.CheckFare.Travelport\CTeleport.Services.CheckFare.Travelport.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.Co2Emissions\CTeleport.Services.Co2Emissions.csproj" />
    <ProjectReference Include="..\..\src\CTeleport.Services.ExtraServiceManagement\CTeleport.Services.ExtraServiceManagement.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageReference Include="CTeleport.Authorization" Version="1.1.200" />
    <PackageReference Include="CTeleport.Common.Messaging" Version="1.1.293" />
    <PackageReference Include="CTeleport.Messages" Version="1.1.3440" />
    <PackageReference Include="CTeleport.Common.V2.Redis" Version="1.1.39" Aliases="RedisV2" />
    <PackageReference Include="CTeleport.Services.ExtraServiceManagement.Shared" Version="2025.4.29.1173" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="15.7.0" />
    <PackageReference Include="TeamCity.VSTest.TestAdapter" Version="1.0.25" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="2.1.23" />
    <PackageReference Include="xunit" Version="2.3.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.3.1" />
    <PackageReference Include="Moq" Version="4.8.3" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
  </ItemGroup>
</Project>