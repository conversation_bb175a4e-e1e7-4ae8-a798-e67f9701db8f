using CTeleport.Services.Booking.Repositories.FilterConverters;
using FluentAssertions;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class MongoDbFIlterConverterTests
    {
        
        [Theory]
        [InlineData("created_by.id", "CreatedBy._id")]
        [InlineData("tenant_id", "TenantId")]
        [InlineData("created_by.user_name", "CreatedBy.UserName")]
        public void ConvertFieldName_shouldReturnFieldNamePascalCased(string fieldName, string expected)
        {
            var result = MongoDbFilterConverter.ConvertFieldName(fieldName);
            result.Should().Be(expected);
        }
    }
}