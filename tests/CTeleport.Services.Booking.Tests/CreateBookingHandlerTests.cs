using CTeleport.Common.Authorization.Services;
using CTeleport.Common.Exceptions.Interfaces;
using CTeleport.Common.Helpers;
using CTeleport.Common.Messaging.Services;
using CTeleport.Common.Redis;
using CTeleport.Messages;
using CTeleport.Messages.Commands;
using CTeleport.Messages.Commands.Bookings;
using CTeleport.Messages.Commands.Models;
using CTeleport.Messages.Commands.Reservations;
using CTeleport.Messages.Events.Bookings;
using CTeleport.Messages.Events.Reservations;
using CTeleport.Services.Booking.Core;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Handlers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.Booking.Validation;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.ExtraServiceManagement.Services;
using CTeleport.Services.FareCache;
using CTeleport.Services.FrequentFlyer.Models;
using CTeleport.Services.FrequentFlyer.Service;
using CTeleport.Services.Helpers;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using CTeleport.Services.SearchProxy.Services;
using CTeleport.Services.Settings.Contracts.TenantManagementService;
using CTeleport.Services.Settings.Services;
using CTeleport.Services.TenantShared.Models;
using CTeleport.Services.TravelPolicies.Enums;
using CTeleport.Services.ApprovalQueueClient;
using CTeleport.Services.ApprovalQueueClient.Configuration;
using CTeleport.Services.ApprovalQueueClient.Models;
using FluentAssertions;
using Moq;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Messages.Commands.FareRules;
using CTeleport.Services.Booking.Api;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Entities;
using CTeleport.Services.Booking.Domain.Aggregates.BookingAggregate.Requests;
using CTeleport.Services.Providers.Client;
using Xunit;
using FareRuleSection = CTeleport.Services.Search.Shared.Models.FareRuleSection;
using FrequentFlyerNumber = CTeleport.Messages.Commands.Models.FrequentFlyerNumber;
using IAuthService = CTeleport.Services.Booking.Services.IAuthService;
using ISecretsManagerService = CTeleport.Services.Booking.Services.ISecretsManagerService;
using Metadata = CTeleport.Services.Booking.Models.Metadata;
using RefundCondition = CTeleport.Services.Search.Shared.Enums.RefundCondition;

namespace CTeleport.Services.Booking.Tests
{
    public class CreateBookingHandlerTests
    {
        private readonly Mock<ITravelPoliciesService> _travelPoliciesServiceMock;
        private readonly Mock<ICustomFieldsService> _customFieldsService;
        private readonly CreateBookingHandler _handler;
        private readonly CreateReservationHandler _reservationHandler;
        private readonly Mock<IMessageDispatcher> _dispatcherMock;
        private readonly Mock<IFlightSolutionService> _searchMock;
        private readonly Mock<IBookingService> _bookingMock;
        private readonly Mock<IAuthService> _authMock;
        private readonly Mock<ITicketService> _ticketServiceMock;
        private readonly Mock<IFareRulesProcessor> _fareRulesProcessorMock;

        private const string BOOKING_ID = "BOOKING_ID";
        private const string FLIGHT_SOLUTION_ID = "FLIGHT_SOLUTION_ID";
        private const string SEARCH_ID = "SEARCH_ID";
        private const string ROUTE_ID = "ROUTE_ID";
        private const string CUSTOM_FIELDS_KEY = nameof(CUSTOM_FIELDS_KEY);
        private const string CUSTOM_FIELDS_VALUE = nameof(CUSTOM_FIELDS_VALUE);
        private const int DEFAULT_INVOICEE_BT = 2;
        private const string DEFAULT_TENANT = "test";

        private readonly List<string> TRAVELPORT_KEYS = new List<string>
        {
            "1G.8WG0:TRAVELPORT-KEY-1",
            "1G.8WG0:TRAVELPORT-KEY-2"
        };
        private readonly Mock<ISettingsService> _settingsServiceMock;
        private readonly Mock<IBookingSagaService> _bookingSagaServiceMock;
        private readonly Mock<IBookingSagaRepository> _sagaRepositoryMock;
        private readonly Mock<IFlightSearchReadService> _flightSearchReadServiceMock;
        private readonly Mock<IBookingPaymentService> _bookingPaymentsMock;
        private readonly Mock<IBillingDetailsValidator> _billingDetailsValidator;
        private readonly Mock<IFrequentFlyerService> _frequentFlyerServiceMock;
        private readonly Mock<IProvidersClient> _providersServiceMock;
        private readonly Mock<IServiceContext> _serviceContext;
        private readonly Mock<IBookingMetricsService> _metricMock;
        private readonly Mock<ISagaMapper> _sagaMapper;
        private const string SINGLE_TICKET_BOOKING_ID = "SINGLE_TICKET_BOOKING_ID";
        private const string SINGLE_FLIGHT_SOLUTION_ID = "1G.8WG0:SINGLE_FLIGHT_SOLUTION_ID";

        public CreateBookingHandlerTests()
        {
            var handlerFactory = new HandlerFactory(new Mock<IExceptionHandler>().Object, new Mock<ILockFactory>().Object);
            var loggerMock = new Mock<ILogger>();
            loggerMock
                .Setup(l => l.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(loggerMock.Object);

            _dispatcherMock = new Mock<IMessageDispatcher>();
            _serviceContext = new Mock<IServiceContext>();
            _authMock = new Mock<IAuthService>();
            _metricMock = new Mock<IBookingMetricsService>();
            _sagaMapper = new Mock<ISagaMapper>();
            _providersServiceMock = new Mock<IProvidersClient>();

            _bookingMock = new Mock<IBookingService>();
            _bookingMock.Setup(s => s.GetEnrichedReservationAsync(It.IsAny<string>()))
                .ReturnsAsync(new Reservation
                {
                    Id = Id.New(),
                    BookingId = BOOKING_ID,
                    Locators = new Dictionary<string, string>
                    {
                        { LocatorNames.PROVIDER, string.Empty },
                        { LocatorNames.UNIVERSAL_RECORD, string.Empty },
                        { LocatorNames.AIR_RESERVATION, string.Empty }
                    },
                    Price = new ReservationPrice(),
                    LegSegments = new[]
                    {
                        new []
                        {
                            new Shared.Models.Segment
                            {
                                Origin = "SVO",
                                Destination = "RIX"
                            }
                        }
                    },
                    FareRules = new Dictionary<string, List<FareRuleSection>>(),
                    Fare = new Shared.Models.Fare()
                });

            var flightSolution = new FlightSolution
            {
                Id = FLIGHT_SOLUTION_ID,
                SearchId = SEARCH_ID,
                RouteId = ROUTE_ID,
                Price = new FlightSolutionPrice
                {
                    MarkupComponents = new Component[0],
                    KickbackComponents = new Component[0]
                },
                ProviderKeys = TRAVELPORT_KEYS,
                FundingSource = TRAVELPORT_KEYS.First(),
                Terms = new FlightSolutionTerms
                {
                    Splitting = true
                }
            };
            _searchMock = new Mock<IFlightSolutionService>();
            _searchMock.Setup(s => s.GetFlightSolutionAsync(FLIGHT_SOLUTION_ID))
                .ReturnsAsync(flightSolution);

            var fareRulesCacheMock = new Mock<IFareRulesCache>();
            fareRulesCacheMock.Setup(s => s.Get(It.IsAny<string>()))
                .Returns(new FareRulesCacheEntry
                {
                    FareRules = new Dictionary<string, List<FareRuleSection>>()
                });

            var ticketServiceMock = new Mock<ITicketService>();
            _settingsServiceMock = new Mock<ISettingsService>();
            _settingsServiceMock.Setup(s => s.GetTenantCorporateCodesAsync(It.IsAny<string>()))
                .ReturnsAsync(new Dictionary<string, string>());
            _settingsServiceMock.Setup(s => s.GetTenantAsync(It.IsAny<string>(), It.IsAny<bool>()))
                .ReturnsAsync(new Tenant()
                {
                    Kickback = new Kickback()
                });

            _bookingSagaServiceMock = new Mock<IBookingSagaService>();
            
            _sagaRepositoryMock = new Mock<IBookingSagaRepository>();
            _sagaRepositoryMock.Setup(r => r.GetAsync(BOOKING_ID))
                .ReturnsAsync(new Sagas.CreateBooking(BOOKING_ID, null, "", FLIGHT_SOLUTION_ID, "", "", new List<IList<FlightSegment>>(), TRAVELPORT_KEYS,
                    0.1M, "EUR", new Models.PassengerDetails(), new Metadata { ExemptLiTax = false }, new CTeleport.Services.Search.Shared.Models.User(),
                    BookingState.Confirmed, RefundCondition.Unknown, ChangeCondition.Unknown, DEFAULT_INVOICEE_BT, PaymentMethod.BankTransfer, null));
            _sagaRepositoryMock.Setup(r => r.GetAsync(SINGLE_TICKET_BOOKING_ID))
                .ReturnsAsync(new Sagas.CreateBooking(SINGLE_TICKET_BOOKING_ID, null, "", SINGLE_FLIGHT_SOLUTION_ID, "", "", new List<IList<FlightSegment>>(),
                    TRAVELPORT_KEYS, 0.1M, "EUR", new Models.PassengerDetails(), new Metadata { ExemptLiTax = true }, new CTeleport.Services.Search.Shared.Models.User(),
                    BookingState.Confirmed, RefundCondition.Unknown, ChangeCondition.Unknown, DEFAULT_INVOICEE_BT, PaymentMethod.BankTransfer, null));

            _flightSearchReadServiceMock = new Mock<IFlightSearchReadService>();
            _flightSearchReadServiceMock.Setup(service => service.GetSearchAsync(SEARCH_ID))
                .ReturnsAsync(() => GetSearch(SEARCH_ID, ROUTE_ID));

            _travelPoliciesServiceMock = new Mock<ITravelPoliciesService>();
            _travelPoliciesServiceMock
                .Setup(client => client.VerifyTravelPolicy(It.IsAny<string>(), It.IsAny<FlightSolution>(), It.IsAny<Dictionary<string, string>>()))
                .ReturnsAsync(() => (false, new TravelPolicyInfo { RuleResult = RuleResult.Permitted }, false));

            _ticketServiceMock = new Mock<ITicketService>();

            var mapper = AutoMapperConfig.InitializeMapper();

            _customFieldsService = new Mock<ICustomFieldsService>();

            _bookingPaymentsMock = new Mock<IBookingPaymentService>();

            var airProvidersService = new Mock<IAirProvidersService>();

            var secretsManagerService = new Mock<ISecretsManagerService>();

            var ancillaryServiceMock = new Mock<IAncillaryService>();
            
            var providersClientMock = new Mock<IProvidersClient>();
            providersClientMock.Setup(p => p.GetFundingSourceEnabledAsync(It.IsAny<string>())).ReturnsAsync(true);

            _billingDetailsValidator = new Mock<IBillingDetailsValidator>();
            _billingDetailsValidator.Setup(x => x.ValidateCreateBookingAsync(It.IsAny<CreateBooking>()))
                .ReturnsAsync((CreateBooking b) =>
                    new BillingValidationResult(DEFAULT_INVOICEE_BT, PaymentMethod.BankTransfer));

            _frequentFlyerServiceMock = new Mock<IFrequentFlyerService>();

            _sagaMapper = new Mock<ISagaMapper>();

            _fareRulesProcessorMock = new Mock<IFareRulesProcessor>();
            _fareRulesProcessorMock
                .Setup(x => x.DispatchFareRuleSectionsAsync(It.IsAny<Dictionary<string, List<FareRuleSection>>>()))
                .Returns(Task.CompletedTask);

            _handler = new CreateBookingHandler(handlerFactory, loggerMock.Object, _dispatcherMock.Object,
                _authMock.Object, _bookingMock.Object, _bookingSagaServiceMock.Object, _searchMock.Object,
                ticketServiceMock.Object, mapper, new Mock<ISingleNameService>().Object, _settingsServiceMock.Object,
                _flightSearchReadServiceMock.Object, _travelPoliciesServiceMock.Object, _bookingPaymentsMock.Object,
                _billingDetailsValidator.Object, airProvidersService.Object, secretsManagerService.Object, _serviceContext.Object,
                ancillaryServiceMock.Object, _metricMock.Object, new Mock<IBookingVariantService>().Object, _sagaMapper.Object, providersClientMock.Object);

            IReservationBuilder reservationBuilder = new ReservationBuilder(
                loggerMock.Object, fareRulesCacheMock.Object, _customFieldsService.Object, _settingsServiceMock.Object,
                null, _ticketServiceMock.Object, new ApprovalQueueOptions(), priceService: null,
                _frequentFlyerServiceMock.Object, mapper, _providersServiceMock.Object);

            _reservationHandler = new CreateReservationHandler(handlerFactory, loggerMock.Object, _dispatcherMock.Object,
                                                               _bookingMock.Object, _bookingSagaServiceMock.Object,
                                                               _searchMock.Object, reservationBuilder, _fareRulesProcessorMock.Object);

            _settingsServiceMock.Setup(x => x.CheckSourceGloballyEnabled(It.IsAny<string>())).ReturnsAsync(true);
        }

        [Fact]
        public async Task Should_reject_if_source_disabled()
        {
            _settingsServiceMock.Setup(x => x.CheckSourceGloballyEnabled("1G.8WG0")).ReturnsAsync(false);

            var command = new CreateBooking
            {
                BookingId = BOOKING_ID,
                FlightSolutionId = FLIGHT_SOLUTION_ID,
                Metadata = new BookingMetadata(),
                Passenger = new Passenger(),
                User = new Messages.Commands.Models.User
                {
                    Email = "<EMAIL>",
                    Name = "Unit test",
                    TenantId = DEFAULT_TENANT
                },
                Request = new Request
                {
                    Id = Id.New()
                }
            };

            _bookingSagaServiceMock
                .Setup(s => s.CreateAsync(It.IsAny<CreateSagaRequest>()))
                .ReturnsAsync(new Domain.Aggregates.BookingAggregate.Booking("id", "tenantId"));
            
            await _handler.HandleAsync(command);

            _dispatcherMock.Verify(x => x.DispatchAsync(It.Is<CreateBookingRejected>(e => e.Code == OperationCodes.SourceDisabled)));
        }

        [Fact]
        public async Task Should_not_reject_if_source_enabled()
        {
            var bookingSaga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");
            bookingSaga.Initialize(new InitializeBookingRequest());
            bookingSaga.AddReservation(new AddReservationRequest());

            _bookingSagaServiceMock
                .Setup(x => x.CreateAsync(It.IsAny<CreateSagaRequest>()))
                .ReturnsAsync(bookingSaga);

            var command = new CreateBooking
            {
                BookingId = BOOKING_ID,
                FlightSolutionId = FLIGHT_SOLUTION_ID,
                Metadata = new BookingMetadata(),
                Passenger = new Passenger(),
                User = new Messages.Commands.Models.User
                {
                    Email = "<EMAIL>",
                    Name = "Unit test",
                    TenantId = DEFAULT_TENANT
                },
                Request = new Request
                {
                    Id = Id.New()
                },
                InvoiceeId = 0,
                PaymentMethodType = Messages.Commands.Enums.PaymentMethodType.BankTransfer
            };

            await _handler.HandleAsync(command);

            _dispatcherMock.Verify(x => x.DispatchAsync(It.IsAny<CreateReservation>()));
        }

        [Fact(Skip = "There are tech. issues with mocking optional agrument of DispatchAsync()")]
        public async Task Should_cancel_all_reservation_on_error()
        {
            var command = new CreateBooking
            {
                BookingId = BOOKING_ID,
                FlightSolutionId = FLIGHT_SOLUTION_ID,
                Metadata = new BookingMetadata(),
                Passenger = new Passenger(),
                User = new Messages.Commands.Models.User
                {
                    Email = "<EMAIL>",
                    Name = "Unit test",
                    TenantId = DEFAULT_TENANT
                },
                Request = new Request
                {
                    Id = Id.New()
                }
            };

            CreateReservation createReservationCommand = null;
            _dispatcherMock.Setup(d => d.DispatchAsync(It.IsAny<CreateReservation>(), null, It.IsAny<MessageContext>()))
                .Returns(() => Task.Delay(1))
                .Callback<CreateReservation, string>((c, provider) =>
                {
                    createReservationCommand = c;
                });

            CreateReservationRejected createReservationRejectedEvent = null;
            _dispatcherMock.Setup(d => d.DispatchAsync(It.IsAny<CreateReservationRejected>(), null, It.IsAny<MessageContext>()))
                .Returns(() => Task.Delay(1))
                .Callback<CreateReservationRejected, string>((e, provider) =>
                {
                    createReservationRejectedEvent = e;
                });

            CreateBookingRejected createBookingRejectedEvent = null;
            _dispatcherMock.Setup(d => d.DispatchAsync(It.IsAny<CreateBookingRejected>(), null, It.IsAny<MessageContext>()))
                .Returns(() => Task.Delay(1))
                .Callback<CreateBookingRejected>(e =>
                {
                    createBookingRejectedEvent = e;
                });

            await _handler.HandleAsync(command);
            await _reservationHandler.HandleAsync(createReservationCommand);

            await _reservationHandler.HandleAsync(createReservationCommand);
            await _reservationHandler.HandleAsync(createReservationRejectedEvent);

            _dispatcherMock.Verify(d => d.DispatchAsync(It.IsAny<CreateReservation>(), null, It.IsAny<MessageContext>()), Times.Exactly(2));
            _dispatcherMock.Verify(d => d.DispatchAsync(It.IsAny<CreateReservationRejected>(), null, It.IsAny<MessageContext>()), Times.Once);
            _dispatcherMock.Verify(d => d.DispatchAsync(It.IsAny<CancelReservation>(), null, It.IsAny<MessageContext>()), Times.Once);
            _dispatcherMock.Verify(d => d.DispatchAsync(It.IsAny<CreateBookingRejected>(), null, It.IsAny<MessageContext>()), Times.Once);
        }

        [Fact]
        public async Task Created_saga_should_contains_custom_fields()
        {
            var search = GetSearch(SEARCH_ID, ROUTE_ID, firstName: Id.New(), lastName: Id.New(), docNumber: Id.New());
            var passenger = search.Request.Routes.First().Passengers.First();
            _flightSearchReadServiceMock.Setup(service => service.GetSearchAsync(SEARCH_ID))
                .ReturnsAsync(() => search);

            var bookingSaga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");
            bookingSaga.Initialize(new InitializeBookingRequest());
            bookingSaga.AddReservation(new AddReservationRequest());

            _bookingSagaServiceMock
                .Setup(x => x.CreateAsync(It.IsAny<CreateSagaRequest>()))
                .ReturnsAsync(bookingSaga);

            var command = new CreateBooking
            {
                BookingId = BOOKING_ID,
                FlightSolutionId = FLIGHT_SOLUTION_ID,
                Metadata = new BookingMetadata(),
                Passenger = new Passenger
                {
                    FirstName = passenger.FirstName,
                    LastName = passenger.LastName,
                    DocNumber = passenger.DocNumber
                },
                User = new Messages.Commands.Models.User
                {
                    Email = "<EMAIL>",
                    Name = "Unit test",
                    TenantId = DEFAULT_TENANT,
                    Id = Guid.NewGuid().ToString("N")
                },
                Request = new Request
                {
                    Id = Id.New()
                },
                InvoiceeId = 0,
                PaymentMethodType = Messages.Commands.Enums.PaymentMethodType.BankTransfer
            };

            await _handler.HandleAsync(command);

            bookingSaga.Should().NotBeNull();
        }

        [Fact]
        public async Task Created_saga_should_not_contain_custom_fields()
        {
            var search = GetSearch(SEARCH_ID, ROUTE_ID, firstName: Id.New(), lastName: Id.New(), docNumber: Id.New());
            _flightSearchReadServiceMock.Setup(service => service.GetSearchAsync(SEARCH_ID))
                .ReturnsAsync(() => search);

            var bookingSaga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");
            bookingSaga.Initialize(new InitializeBookingRequest());
            bookingSaga.AddReservation(new AddReservationRequest());

            _bookingSagaServiceMock
                .Setup(x => x.CreateAsync(It.IsAny<CreateSagaRequest>()))
                .ReturnsAsync(bookingSaga);

            var command = new CreateBooking
            {
                BookingId = BOOKING_ID,
                FlightSolutionId = FLIGHT_SOLUTION_ID,
                Metadata = new BookingMetadata(),
                Passenger = new Passenger(),
                User = new Messages.Commands.Models.User
                {
                    Email = "<EMAIL>",
                    Name = "Unit test",
                    TenantId = DEFAULT_TENANT
                },
                Request = new Request
                {
                    Id = Id.New()
                },
                InvoiceeId = 0,
                PaymentMethodType = Messages.Commands.Enums.PaymentMethodType.BankTransfer
            };

            await _handler.HandleAsync(command);

            bookingSaga.Should().NotBeNull();
            bookingSaga.FlightSolutionEntity.CustomFields.Should().NotBeNull();
            bookingSaga.FlightSolutionEntity.CustomFields.Should().BeEmpty();
        }

        [Fact]
        public async Task Created_saga_should_contain_frequent_flyer_numbers()
        {
            var search = GetSearch(SEARCH_ID, ROUTE_ID, firstName: Id.New(), lastName: Id.New(), docNumber: Id.New());
            _flightSearchReadServiceMock.Setup(service => service.GetSearchAsync(SEARCH_ID))
                .ReturnsAsync(() => search);

            _frequentFlyerServiceMock.Setup(x => x.GetProgramsAsync(It.IsAny<IEnumerable<string>>()))
                .ReturnsAsync(new List<FrequentFlyerProgram>
                {
                    new FrequentFlyerProgram
                    {
                        Code = "BT1",
                        Carrier = "BT"
                    },
                    new FrequentFlyerProgram
                    {
                        Code = "SU",
                        Carrier = "SU"
                    }
                });

            var bookingSaga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");
            bookingSaga.Initialize(new InitializeBookingRequest
            {
                FreqFlyerNums = new List<FreqFlyerNumEntity>
                {
                    new()
                    {
                        Carrier = "BT",
                        Code = "BT1",
                        Number = "123456",
                        ProgramComponent = "SVORIX"
                    }
                }
            });
            bookingSaga.AddReservation(new AddReservationRequest());

            var command = new CreateBooking
            {
                BookingId = BOOKING_ID,
                FlightSolutionId = FLIGHT_SOLUTION_ID,
                Metadata = new BookingMetadata(),
                Passenger = new Passenger(),
                User = new Messages.Commands.Models.User
                {
                    Email = "<EMAIL>",
                    Name = "Unit test",
                    TenantId = DEFAULT_TENANT
                },
                Request = new Request
                {
                    Id = Id.New()
                },
                InvoiceeId = 0,
                PaymentMethodType = Messages.Commands.Enums.PaymentMethodType.BankTransfer,
                FrequentFlyerNumbers = new Dictionary<string, FrequentFlyerNumber>
                {
                    {"SVORIX", new FrequentFlyerNumber
                        {
                            Code = "BT1",
                            Number = "123456"
                        }}
                }
            };

            _bookingSagaServiceMock
                .Setup(s => s.CreateAsync(It.IsAny<CreateSagaRequest>()))
                .ReturnsAsync(bookingSaga);
            
            await _handler.HandleAsync(command);

            bookingSaga.Should().NotBeNull();
            bookingSaga.BookingEntity.FreqFlyerNums.Should().BeEquivalentTo(new List<FreqFlyerNumEntity>
            {
                new FreqFlyerNumEntity
                {
                    Carrier = "BT",
                    Code = "BT1",
                    Number = "123456",
                    ProgramComponent = "SVORIX"
                }
            });
        }
        
        [Fact]
        public async Task CreateReservation_Should_Dispatch_FareRuleSections_For_NonVirtual_Reservation()
        {
            // Arrange
            var command = new CreateReservation
            {
                RequestId = Id.New(),
                BookingId = BOOKING_ID,
                ProviderKey = "1G.PROVIDER_KEY"
            };
            
            var draftReservation = new Reservation
            {
                Id = Id.New(),
                FareRules = new Dictionary<string, List<FareRuleSection>>
                {
                    { 
                        "SVO-JFK", 
                        new List<FareRuleSection> 
                        { 
                            new FareRuleSection 
                            { 
                                Category = "16", 
                                Title = "PENALTIES",
                                Text = "PENALTY RULE 1" 
                            } 
                        } 
                    }
                }
            };
            
            var flightSolution = new FlightSolution();
            
            var saga = new Domain.Aggregates.BookingAggregate.Booking(BOOKING_ID, "tenant");
            saga.Initialize(new InitializeBookingRequest
            {
                CreatedBy = new UserEntity { Name = "Test User", Email = "<EMAIL>" }
            });
            
            var reservationParams = new CreateReservationParams
            {
                IsVirtual = false,
                ProviderKey = "1G.PROVIDER_KEY"
            };
            
            // Mock the booking service methods
            _bookingMock.Setup(s => s.CreateReservation(draftReservation))
                .ReturnsAsync(draftReservation);
            
            _bookingMock.Setup(s => s.CreateReservationCommand(reservationParams))
                .ReturnsAsync(new ProviderCreateReservation { ProviderKey = "1G.PROVIDER_KEY" });
            
            // Act
            var result = await _reservationHandler.CreateReservation(
                command, 
                draftReservation, 
                flightSolution, 
                saga, 
                reservationParams);
            
            // Assert
            Assert.Equal(draftReservation.Id, result.Id);
            
            // Verify that DispatchFareRuleSections was called
            _fareRulesProcessorMock.Verify(
                p => p.DispatchFareRuleSectionsAsync(It.IsAny<Dictionary<string, List<FareRuleSection>>>()),
                Times.Once,
                "DispatchFareRuleSections should be called once when creating a non-virtual reservation");
            
            // Verify that the provider command was dispatched
            _dispatcherMock.Verify(
                d => d.DispatchAsync(It.IsAny<ProviderCreateReservation>(), It.IsAny<string>(), null), 
                Times.Once, 
                "Provider command should be dispatched once when creating a non-virtual reservation");
        }

        #region Infrastructure

        private Search.Shared.Models.Search GetSearch(string searchId, string routeId,
            string firstName = null, string lastName = null, string docNumber = null)
        {
            return new Search.Shared.Models.Search
            {
                Id = searchId,
                Request = new SearchRequest
                {
                    Routes = new List<SearchRoute>{ new SearchRoute
                        {
                            Id = routeId,
                            Passengers=new List<SearchPassenger>{
                                new SearchPassenger
                                {
                                    FirstName = firstName,
                                    LastName = lastName,
                                    DocNumber = docNumber,
                                    CustomFields = new Dictionary<string, string>
                                    {
                                        {CUSTOM_FIELDS_KEY,CUSTOM_FIELDS_VALUE}
                                    }
                                }
                            } }
                    }
                }
            };
        }

        #endregion
    }
}
