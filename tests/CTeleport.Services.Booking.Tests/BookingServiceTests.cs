using AutoFixture;
using AutoFixture.Xunit2;
using AutoMapper;
using CTeleport.Common.Authorization;
using CTeleport.Common.Authorization.Services;
using CTeleport.Common.Helpers;
using CTeleport.Services.Billing.Clients;
using CTeleport.Services.Booking.Enums;
using CTeleport.Services.Booking.Helpers;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.CustomFields.Services;
using CTeleport.Services.CustomFields.Services.Interfaces.Legacy;
using CTeleport.Services.ExtraServiceManagement.Clients;
using CTeleport.Services.ExtraServiceManagement.Shared;
using CTeleport.Services.ExtraServiceManagement.Shared.Enums;
using CTeleport.Services.FareTerms;
using CTeleport.Services.FareTerms.Shared.Requests;
using CTeleport.Services.FareTerms.Shared.Responses;
using CTeleport.Services.FrequentFlyer.Models;
using CTeleport.Services.FrequentFlyer.Service;
using CTeleport.Services.Helpers;
using CTeleport.Services.PlacesApiClient;
using CTeleport.Services.Search.Shared.Enums;
using CTeleport.Services.Search.Shared.Models;
using FluentAssertions;
using Moq;
using Serilog.Core;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Api;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.Search.Shared.Models.ProviderTerms;
using CTeleport.Services.SearchProxy.Client;
using CTeleport.Services.Settings.Clients;
using OpenFeature;
using Xunit;
using AuthzQueryParserService = CTeleport.Services.Booking.Services.AuthzQueryParserService;
using FrequentFlyerNumber = CTeleport.Services.FrequentFlyer.Models.FrequentFlyerNumber;
using IAuthService = CTeleport.Services.Booking.Services.IAuthService;
using IBillingClient = CTeleport.Services.Billing.Clients.IBillingClient;
using ReservationPassenger = CTeleport.Services.Booking.Shared.Models.ReservationPassenger;
using Segment = CTeleport.Services.Booking.Shared.Models.Segment;
using LegacyModels = CTeleport.Services.CustomFields.Models.Legacy;
using LegacyPublicContract = CTeleport.Services.CustomFields.Dto.LegacyPublicContract;
using Metadata = CTeleport.Services.Booking.Models.Metadata;

namespace CTeleport.Services.Booking.Tests
{
    public partial class BookingServiceTests
    {
        private readonly IMapper _mapper;
        private readonly BookingService _service;
        private readonly IServiceContext _context;

        static string USER_ID = "USER01";

        static string BOOKING_A_ID = Id.New();
        static string TENANT_A_ID = Id.New();
        static string BOOKING_A_PAX_LASTNAME = "LastnameA";

        static Models.Booking BOOKING_A = new Models.Booking
        {
            Id = BOOKING_A_ID,
            TenantId = TENANT_A_ID,
            Passenger = new PassengerDetails
            {
                LastName = BOOKING_A_PAX_LASTNAME,
            }
        };

        static string BOOKING_B_ID = Id.New();
        static string BOOKING_B_TICKET_NUMBER = "1175267490774";
        static string TENANT_B_ID = Id.New();
        static string BOOKING_B_PAX_LASTNAME = "LastnameB";

        static Models.Booking BOOKING_B = new Models.Booking
        {
            Id = BOOKING_B_ID,
            TenantId = TENANT_B_ID,
            Passenger = new PassengerDetails
            {
                LastName = BOOKING_B_PAX_LASTNAME
            }
        };

        static string BOOKING_C_ID = Id.New();
        static Models.Booking BOOKING_C = new Models.Booking
        {
            Id = BOOKING_C_ID,
            TenantId = TENANT_A_ID,
            Legs = new List<Leg>(),
            Terms = new BookingTerms()
            {
                Splitting = false
            },
            Passenger = new PassengerDetails
            {
                Id = "1",
                UserId = "2",
                FirstName = "Firstname",
                SingleNameOnly = false,
                Autofilled = false,
                LastName = BOOKING_A_PAX_LASTNAME,
                Gender = Search.Shared.Enums.Gender.Male,
                DateOfBirth = "2000-11-11",
                Nationality = "NL",
                DocType = CTeleport.Services.Booking.Shared.Enums.DocumentType.Passport,
                DocNumber = "123123",
                DocCountry = "NL",
                DocExpire = "2050-11-11"
            },
            Price = new BookingPrice(),
            Metadata = new Metadata()
            {
                CustomFields = new Dictionary<string, string>()
                {
                    { "custom-field-1", "Value1" },
                    { "custom-field-2", "custom-field-2-code" }
                }
            }
        };

        static string BOOKING_D_ID = Id.New();
        static Models.Booking BOOKING_D = new Models.Booking
        {
            Id = BOOKING_D_ID,
            TenantId = TENANT_B_ID,
            Legs = new List<Leg>(),
            Terms = new BookingTerms()
            {
                Splitting = false
            },
            Passenger = new PassengerDetails
            {
                Id = "1",
                UserId = "2",
                FirstName = "Firstname",
                SingleNameOnly = false,
                Autofilled = false,
                LastName = BOOKING_A_PAX_LASTNAME,
                Gender = Search.Shared.Enums.Gender.Male,
                DateOfBirth = "2000-11-11",
                Nationality = "NL",
                DocType = CTeleport.Services.Booking.Shared.Enums.DocumentType.Passport,
                DocNumber = "123123",
                DocCountry = "NL",
                DocExpire = "2050-11-11"
            },
            Price = new BookingPrice(),
            FrequentFlyerNumbers = new Dictionary<string, FrequentFlyerNumber>
            {
                ["rixams"] = new FrequentFlyerNumber
                {
                    Code = "123",
                    Carrier = "Airbaltic"
                }
            }
        };


        static string BOOKING_WITHOUT_LASTNAME_ID = Id.New();

        static Models.Booking BOOKING_WITHOUT_LASTNAME = new Models.Booking
        {
            Id = BOOKING_B_ID,
            TenantId = TENANT_B_ID,
            Passenger = new PassengerDetails
            {
                LastName = null
            }
        };

        static string RESERVATION_A_LOCATOR = "RLA";

        private static Reservation RESERVATION_A = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_A_ID,
            CreatedAt = DateTime.UtcNow,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_A_PAX_LASTNAME
            },
            Locators = new Dictionary<string, string>
            {
                {LocatorNames.PROVIDER, RESERVATION_A_LOCATOR}
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            }
        };

        static Reservation RESERVATION_A_DUPLICATE = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_B_ID,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            Locators = new Dictionary<string, string>
            {
                {LocatorNames.PROVIDER, RESERVATION_A_LOCATOR}
            }
        };
        static Reservation RESERVATION_B = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_B_ID,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            Tickets = new List<Ticket>
            {
                new Ticket
                {
                    Number = "11111111"
                }
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            }
        };

        static Reservation RESERVATION_C = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_C_ID,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            Tickets = new List<Ticket>
            {
                new Ticket
                {
                    Number = "2222222"
                }
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            },
            LegDurations = new List<int> { 100 },
            Price = new ReservationPrice()
            {
                Net = 123M
            },
            State = ReservationState.Active,
            Locators = new Dictionary<string, string>
            {
                {LocatorNames.PROVIDER, RESERVATION_A_LOCATOR}
            },
            OriginalFare = new CTeleport.Services.Booking.Shared.Models.Fare(),
            Refund = new ReservationRefund(),
            Fare = new CTeleport.Services.Booking.Shared.Models.Fare()
            {
                NonRefAmounts = new Search.Shared.Models.NonRefundableAmounts()
            }
        };
        static Reservation RESERVATION_WITHOUT_LASTNAME = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_WITHOUT_LASTNAME_ID,
            Passenger = new ReservationPassenger
            {
                LastName = "LNU"
            },
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            Locators = new Dictionary<string, string>
            {
                {LocatorNames.PROVIDER, RESERVATION_A_LOCATOR}
            }
        };

        static LegacyPublicContract.TenantCustomFieldsInfoDto TENANT_CUSTOM_FIELDS = new LegacyPublicContract.TenantCustomFieldsInfoDto
        {
            CustomFields = new List<LegacyPublicContract.CustomFieldMetadataDto>()
            {
                new()
                {
                    FieldName = "custom-field-1",
                    FieldType = "string",
                    Name = "CustomField1"
                },
                new()
                {
                    FieldName = "custom-field-2",
                    FieldType = "Dropdown",
                    Name = "CustomField2",
                    Options = new List<LegacyModels.CustomOption>()
                    {
                        new()
                        {
                            Key = "custom-field-2",
                            Value = "Dropdown value to be displayed",
                            Code = "custom-field-2-code"
                        }
                    }
                }
            }
        };

        private Mock<IBookingsRepository> bookingsRepositoryMock;
        private Mock<IReservationsRepository> reservationsRepositoryMock;
        private Mock<IBillingClient> billingClient;
        private Mock<ITicketService> ticketServiceMock;
        private Mock<ITicketUsageService> ticketUsageServiceMock;
        private Mock<ICustomFieldInfoService> customFieldInfoServiceMock;
        private Mock<IBookingSagaService> _bookingSagaServiceMock;
        private Mock<IFrequentFlyerService> _frequentFlyerService;
        private Mock<IExtraServiceManagementClient> _extraServiceManagementClient;
        private Mock<IAuthService> _authServiceMock;
        private Mock<IFareChangeService> _mockFareChangeService;
        private Mock<DateTimeProvider> _timeMock;
        private Mock<IFeatureClient> _featureClientMock;
        private readonly Fixture _fixture;

        public BookingServiceTests()
        {
            _fixture = new Fixture();
            _fixture.Customize<DateOnly>(composer => composer.FromFactory<DateTime>(DateOnly.FromDateTime));
            
            bookingsRepositoryMock = new Mock<IBookingsRepository>();
            _bookingSagaServiceMock = new Mock<IBookingSagaService>();
            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_A_ID))
                .ReturnsAsync(() => BOOKING_A);

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_B_ID))
                .ReturnsAsync(() => BOOKING_B);

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_C_ID))
                .ReturnsAsync(() => BOOKING_C);

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_D_ID))
                .ReturnsAsync(() => BOOKING_D);

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_WITHOUT_LASTNAME_ID))
                .ReturnsAsync(() => BOOKING_WITHOUT_LASTNAME);

            reservationsRepositoryMock = new Mock<IReservationsRepository>();

            reservationsRepositoryMock
                .Setup(r => r.GetByTicketNumberAsync(BOOKING_B_TICKET_NUMBER))
                .ReturnsAsync(() => RESERVATION_B);

            reservationsRepositoryMock
                .Setup(r => r.GetByBookingIdAsync(BOOKING_C_ID))
                .ReturnsAsync(new List<Reservation>() { RESERVATION_C });

            reservationsRepositoryMock
                .Setup(r => r.GetByBookingIdAsync(BOOKING_D_ID))
                .ReturnsAsync(new List<Reservation> { RESERVATION_C });
            
            var fareTermsService = new Mock<IFareTermsClient>();
            fareTermsService
                .Setup(s => s.GetFareTermsByTimelineAsync(It.IsAny<OnPostBookingRequest>()))
                .ReturnsAsync(new OnPostBookingResponse());

            ticketServiceMock = new Mock<ITicketService>();

            billingClient = new Mock<IBillingClient>();

            ticketUsageServiceMock = new Mock<ITicketUsageService>();

            customFieldInfoServiceMock = new Mock<ICustomFieldInfoService>();

            customFieldInfoServiceMock.Setup(s => s.GetTenantCustomFieldsAsync(It.IsAny<string>(),
                    It.IsAny<Func<LegacyModels.CustomField, bool>>(),
                    It.IsAny<Func<LegacyModels.CustomOption, bool>>()))
                .ReturnsAsync(TENANT_CUSTOM_FIELDS);

            billingClient = new Mock<IBillingClient>();
            billingClient.Setup(s => s.GetTenantBillingPreferencesAsync(It.IsAny<string>()))
                .ReturnsAsync(new TenantBillingPreferencesDto());

            _frequentFlyerService = new Mock<IFrequentFlyerService>();
            _frequentFlyerService
                .Setup(s => s.GetProgramsAsync(It.IsAny<IEnumerable<string>>()))
                .ReturnsAsync(new List<FrequentFlyerProgram>
                {
                    new FrequentFlyerProgram
                    {
                        Carrier = "AA",
                        Code = "123",
                        Name = "Airbaltic",
                        Rank = 0
                    }
                });

            _authServiceMock = new Mock<IAuthService>();
            _timeMock = new Mock<DateTimeProvider>();
            _featureClientMock = new Mock<IFeatureClient>();

            _mapper = AutoMapperConfig.InitializeMapper();

            _context = ServiceContext.UserContext(TENANT_A_ID, new Common.Authorization.Models.User { Id = USER_ID });
            _extraServiceManagementClient = new Mock<IExtraServiceManagementClient>();
            _mockFareChangeService = new Mock<IFareChangeService>();
            var placesMock = new Mock<IPlacesClient>();
            _service = new BookingService(_context, ticketServiceMock.Object,
                 bookingsRepositoryMock.Object, reservationsRepositoryMock.Object,
                 _mapper, null, new Mock<ISingleNameService>().Object,
                 Logger.None, billingClient.Object, _authServiceMock.Object,
                 fareTermsService.Object,
                 ticketUsageServiceMock.Object,
                 new CustomFieldsDisplayService(customFieldInfoServiceMock.Object),
                 _frequentFlyerService.Object,
                 _bookingSagaServiceMock.Object,
                 _extraServiceManagementClient.Object,
                 placesMock.Object,
                 _mockFareChangeService.Object,
                 Mock.Of<ISearchClient>(),
                 Mock.Of<IProviderTicketStateService>(),
                 Mock.Of<ITenantManagementServiceClient>(),
                 _timeMock.Object,
                 Mock.Of<IFareRulesEnrichmentService>(),
                 _featureClientMock.Object);
        }

        [Fact]
        public async void GetBookingAsync_should_return_Booking()
        {
            var booking = await _service.GetBookingAsync(BOOKING_A_ID);

            Assert.Equal(BOOKING_A_ID, booking.Id);
        }

        [Fact]
        public async void GetBookingAsync_should_return_Null_for_non_existing_booking()
        {
            var booking = await _service.GetBookingAsync(Id.New());

            Assert.Null(booking);
        }

        [Fact]
        public async void GetBookingsForPassengerAsync_returns_active_bookings_within_range_specified()
        {
            bookingsRepositoryMock
                .Setup(r => r.GetByPassengerAsync(new List<List<FilterCondition>> { }, It.IsAny<string>(),
                    It.IsAny<string>()))
                .ReturnsAsync(() => new[]
                {
                    BuildBooking("2018-03-20T11:25", BookingState.Issued),
                    BuildBooking("2018-04-18T11:25", BookingState.Issued),
                    BuildBooking("2018-04-19T10:00", BookingState.Cancelled),
                    BuildBooking("2018-04-24T08:30", BookingState.Confirmed)
                });

            var bookings = await _service.GetBookingsForPassengerAsync("John", "Smith", "2018-04-19", 2);

            Assert.Single(bookings);
        }

        [Fact]
        public async void GetBookingsForPassengerAsync_returns_active_bookings()
        {
            bookingsRepositoryMock
                .Setup(r => r.GetByPassengerAsync(new List<List<FilterCondition>> { }, It.IsAny<string>(),
                    It.IsAny<string>()))
                .ReturnsAsync(() => new[]
                {
                    BuildBooking("2018-04-18T15:45", BookingState.Issued),
                    BuildBooking("2018-04-21T10:05", BookingState.Confirmed),
                });

            var bookings = await _service.GetBookingsForPassengerAsync("John", "Smith", "2018-04-19", 2);

            Assert.Equal(2, bookings.Count());
        }

        [Fact]
        public async void GetBookingsForPassengerAsync_skips_cancelled_and_pending_bookings()
        {
            bookingsRepositoryMock
                .Setup(r => r.GetByPassengerAsync(new List<List<FilterCondition>> { }, It.IsAny<string>(),
                    It.IsAny<string>()))
                .ReturnsAsync(() => new[]
                {
                    BuildBooking("2018-04-18T15:45", BookingState.Cancelled),
                    BuildBooking("2018-04-21T10:05", BookingState.RefundPending),
                });

            var bookings = await _service.GetBookingsForPassengerAsync("John", "Smith", "2018-04-19", 2);

            Assert.Empty(bookings);
        }

        [Fact]
        public async Task GetReservationByLocatorAsync_returns_reservation()
        {
            reservationsRepositoryMock
                .Setup(r => r.GetByProviderLocatorAsync(RESERVATION_A_LOCATOR))
                .ReturnsAsync(new List<Reservation>
                {
                    RESERVATION_A_DUPLICATE,
                    RESERVATION_A
                });

            var reservation =
                await _service.GetReservationByLocatorAsync(RESERVATION_A_LOCATOR, BOOKING_A_PAX_LASTNAME);

            reservation.Id.Should().Be(RESERVATION_A.Id);
        }

        [Fact]
        public async Task GetReservationByLocatorAsync_find_reservation_for_passenger_without_lastname()
        {
            reservationsRepositoryMock
                .Setup(r => r.GetByProviderLocatorAsync(RESERVATION_A_LOCATOR))
                .ReturnsAsync(new List<Reservation>
                {
                    RESERVATION_A,
                    RESERVATION_WITHOUT_LASTNAME
                });

            var reservation = await _service.GetReservationByLocatorAsync(RESERVATION_A_LOCATOR, "LNU");
            reservation.Id.Should().Be(RESERVATION_WITHOUT_LASTNAME.Id);
        }

        [Fact]
        public async Task
            GetReservationByLocatorAsync_does_not_thows_agrument_expeption_if_has_no_reservations_and_lastname_is_null()
        {
            reservationsRepositoryMock
                .Setup(r => r.GetByProviderLocatorAsync(RESERVATION_A_LOCATOR))
                .ReturnsAsync(new List<Reservation>());

            var reservation = await _service.GetReservationByLocatorAsync(RESERVATION_A_LOCATOR, null);

            reservation.Should().BeNull();
        }

        [Fact]
        public async Task GetReservationByLocatorAsync_returns_null_if_reservation_does_not_exist()
        {
            reservationsRepositoryMock
                .Setup(r => r.GetByProviderLocatorAsync(RESERVATION_A_LOCATOR))
                .ReturnsAsync(new List<Reservation>());

            var reservation =
                await _service.GetReservationByLocatorAsync(RESERVATION_A_LOCATOR, BOOKING_A_PAX_LASTNAME);

            reservation.Should().BeNull();
        }

        [Fact]
        public async Task GetReservationByLocatorAsync_returns_null_if_booking_has_different_pax_lastname()
        {
            reservationsRepositoryMock
                .Setup(r => r.GetByProviderLocatorAsync(RESERVATION_A_LOCATOR))
                .ReturnsAsync(new List<Reservation>
                {
                    RESERVATION_A
                });

            var reservation =
                await _service.GetReservationByLocatorAsync(RESERVATION_A_LOCATOR, BOOKING_B_PAX_LASTNAME);

            reservation.Should().BeNull();
        }

        [Fact]
        public void GetLastVoidAt_returns_last_void_at_from_the_ticket()
        {
            var lastVoidAt = new DateTime(2020, 1, 12, 14, 12, 0);

            var reservation = new Reservation()
            {
                CanCancel = true,
                Tickets = new List<Ticket>()
                {
                    new Ticket()
                    {
                        State = TicketState.Open,
                        LastVoidAt = lastVoidAt
                    }
                }
            };

            _service.GetLastVoidAt(reservation).Should().Be(lastVoidAt);
        }

        [Fact]
        public void GetLastVoidAt_returns_last_void_at_for_reservation_with_no_tickets()
        {
            var today = DateTime.Today;

            var reservation = new Reservation()
            {
                CanCancel = true,
                Tickets = new List<Ticket>()
            };

            var now = DateTime.Now;

            var expected = new DateTime(2020, 5, 9, 10, 10, 0);

            ticketServiceMock.Setup(s => s.CalcLastVoidTimeForReservation(reservation, now))
                .Returns(expected);

            var result = _service.GetLastVoidAt(reservation, now);

            result.Should().Be(expected);
        }

        [Fact]
        public void GetLastVoidAt_returns_empty_when_CanCancel_false()
        {
            var reservation = new Reservation
            {
                Tickets = new List<Ticket>
                {
                    new Ticket
                    {
                        State = TicketState.Open,
                        LastVoidAt = new DateTime(2020, 1, 12, 14, 12, 0)
                    }
                }
            };

            _service.GetLastVoidAt(reservation).Should().Be(default(DateTime));
        }

        [Fact]
        public async Task ShouldGetCompletedBookingWithCustomFields()
        {
            var expectedCustomFields = new Dictionary<string, Dto.CustomFieldValue>()
            {
                {
                    "custom-field-1",
                    new Dto.CustomFieldValue()
                    {
                        Title = "CustomField1",
                        Value = "Value1"
                    }
                },
                {
                    "custom-field-2",
                    new Dto.CustomFieldValue()
                    {
                        Title = "CustomField2",
                        Value = "Dropdown value to be displayed"
                    }
                }
            };

            var bookingResponse = await _service.GetCompleteBookingAsync(BOOKING_C_ID);

            bookingResponse.Metadata.CustomFields.Should().NotBeNull();
            bookingResponse.Metadata.CustomFields.Should().ContainKeys(expectedCustomFields.Keys);
            bookingResponse.Metadata.CustomFields.Values.Select(v => v.Title).Should().Contain(expectedCustomFields.Values.Select(v => v.Title));
            bookingResponse.Metadata.CustomFields.Values.Select(v => v.Value).Should().Contain(expectedCustomFields.Values.Select(v => v.Value));
        }

        [Fact]
        public async Task ShouldGetCompletedBookingWithFrequentFlyerNumbers()
        {
            var bookingResponse = await _service.GetCompleteBookingAsync(BOOKING_D_ID);

            bookingResponse.FrequentFlyerNumbers.Should().NotBeNull();

            var ffn = BOOKING_D.FrequentFlyerNumbers.First();

            bookingResponse.FrequentFlyerNumbers.Should().ContainKey(ffn.Key);
            ffn.Value.ProgramName.Should().Be("Airbaltic");
        }

        [Fact]
        public async void GetCompleteBookingAsync_should_fill_ExtraServices()
        {
            var extraServices = new ExtraServiceModel();

            _extraServiceManagementClient.Setup(x => x.GetExtraServicesByBooking(BOOKING_D_ID)).ReturnsAsync(new[] { extraServices });

            var booking = await _service.GetCompleteBookingAsync(BOOKING_D_ID);

            Assert.Equal(1, booking.ExtraServices.Count);
        }

        [Theory, AutoData]
        public async void GetCompleteBookingAsync_FillInvoiceeData_BankTransfer(
            Billing.Clients.Dto.InvoiceeExtendedDto invoicee,
            Billing.Clients.Dto.PaymentMethodDescription bankTransfer,
            Billing.Clients.Dto.PaymentMethodDescription creditCard,
            Billing.Clients.Dto.PaymentMethodDescription amexBta,
            int invoiceeId)
        {
            // Arrange
            bankTransfer.Type = Billing.Clients.Dto.PaymentMethod.BankTransfer;
            creditCard.Type = Billing.Clients.Dto.PaymentMethod.CreditCard;
            amexBta.Type = Billing.Clients.Dto.PaymentMethod.AmexBta;
            
            invoicee.PaymentMethods = new List<Billing.Clients.Dto.PaymentMethodDescription>
            {
                bankTransfer,
                creditCard,
                amexBta
            };

            billingClient
                .Setup(x => x.GetExtendedInvoiceeAsync(invoiceeId))
                .ReturnsAsync(invoicee);
            
            BOOKING_D.InvoiceeId = invoiceeId;
            BOOKING_D.PaymentMethod = Enums.PaymentMethod.BankTransfer;

            // Act
            var booking = await _service.GetCompleteBookingAsync(BOOKING_D_ID);

            // Assert
            Assert.NotNull(booking.Invoicee);
            Assert.Equal(invoicee.Name, booking.Invoicee.Name);
            Assert.NotNull(booking.Invoicee.PaymentMethod);
            Assert.Equal(bankTransfer, booking.Invoicee.PaymentMethod);
        }

        [Theory, AutoData]
        public async void GetCompleteBookingAsync_FillInvoiceeData_CreditCard(
            Billing.Clients.Dto.InvoiceeExtendedDto invoicee,
            Billing.Clients.Dto.PaymentMethodDescription bankTransfer,
            Billing.Clients.Dto.PaymentMethodDescription creditCard,
            Billing.Clients.Dto.PaymentMethodDescription amexBta,
            int invoiceeId,
            string cardId)
        {
            // Arrange
            bankTransfer.Type = Billing.Clients.Dto.PaymentMethod.BankTransfer;
            creditCard.Type = Billing.Clients.Dto.PaymentMethod.CreditCard;
            creditCard.Card.Id = cardId;
            amexBta.Type = Billing.Clients.Dto.PaymentMethod.AmexBta;

            invoicee.PaymentMethods = new List<Billing.Clients.Dto.PaymentMethodDescription>
            {
                bankTransfer,
                creditCard,
                amexBta
            };

            billingClient
                .Setup(x => x.GetExtendedInvoiceeAsync(invoiceeId))
                .ReturnsAsync(invoicee);

            BOOKING_D.InvoiceeId = invoiceeId;
            BOOKING_D.PaymentMethod = Enums.PaymentMethod.CreditCard;
            BOOKING_D.PaymentMethodId = cardId;

            // Act
            var booking = await _service.GetCompleteBookingAsync(BOOKING_D_ID);

            // Assert
            Assert.NotNull(booking.Invoicee);
            Assert.Equal(invoicee.Name, booking.Invoicee.Name);
            Assert.NotNull(booking.Invoicee.PaymentMethod);
            Assert.Equal(creditCard, booking.Invoicee.PaymentMethod);
        }

        [Theory, AutoData]
        public async void GetCompleteBookingAsync_FillInvoiceeData_AmexBta(
            Billing.Clients.Dto.InvoiceeExtendedDto invoicee,
            Billing.Clients.Dto.PaymentMethodDescription bankTransfer,
            Billing.Clients.Dto.PaymentMethodDescription creditCard,
            Billing.Clients.Dto.PaymentMethodDescription amexBta,
            int invoiceeId,
            string cardId)
        {
            // Arrange
            bankTransfer.Type = Billing.Clients.Dto.PaymentMethod.BankTransfer;
            creditCard.Type = Billing.Clients.Dto.PaymentMethod.CreditCard;
            amexBta.Type = Billing.Clients.Dto.PaymentMethod.AmexBta;
            amexBta.Card.Id = cardId;

            invoicee.PaymentMethods = new List<Billing.Clients.Dto.PaymentMethodDescription>
            {
                bankTransfer,
                creditCard,
                amexBta
            };

            billingClient
                .Setup(x => x.GetExtendedInvoiceeAsync(invoiceeId))
                .ReturnsAsync(invoicee);

            BOOKING_D.InvoiceeId = invoiceeId;
            BOOKING_D.PaymentMethod = Enums.PaymentMethod.AmexBta;
            BOOKING_D.PaymentMethodId = cardId;

            // Act
            var booking = await _service.GetCompleteBookingAsync(BOOKING_D_ID);

            // Assert
            Assert.NotNull(booking.Invoicee);
            Assert.Equal(invoicee.Name, booking.Invoicee.Name);
            Assert.NotNull(booking.Invoicee.PaymentMethod);
            Assert.Equal(amexBta, booking.Invoicee.PaymentMethod);
        }

        private Models.Booking BuildBooking(string departureDate, BookingState state)
            => new Models.Booking
            { State = state, DepartureAt = DateTime.Parse(departureDate, CultureInfo.InvariantCulture) };
        
        [Fact]
        public async Task GetFilterHash_Should_Change_WhenFilterChanged()
        {
            var filterConditionToUseForHash1 = new FilterCondition(AuthzQueryParserService.Operator.Equal, AuthScopes.TenantsManage, "123");
            var filterConditionToUseForHash2 = new FilterCondition(AuthzQueryParserService.Operator.Contains, AuthScopes.BookingsRead, 456);
            var filterConditionToUseForHash3 = new FilterCondition(AuthzQueryParserService.Operator.Contains, AuthScopes.BookingsCreate, 234);

            SetupGetReadValidationFilter(new[]
            {
                new[] { filterConditionToUseForHash1 },
                new[] { filterConditionToUseForHash2 }
            });

            var hash = await _service.GetFilterHash();

            SetupGetReadValidationFilter(new[]
            {
                new[] { filterConditionToUseForHash1 },
                new[] { filterConditionToUseForHash3 }
            });

            var hash2 = await _service.GetFilterHash();

            Assert.NotEqual(hash2, hash);
        }

        [Fact]
        public async Task GetFilterHash_Should_Return_CorrectHash()
        {
            var filterConditionToUseForHash1 = new FilterCondition(AuthzQueryParserService.Operator.Equal, AuthScopes.BookingsRead, "123");
            var filterConditionToUseForHash2 = new FilterCondition(AuthzQueryParserService.Operator.RegexpMatch, AuthScopes.BookingsRead, 456);
            var filterConditionToUseForHash3 = new FilterCondition(AuthzQueryParserService.Operator.Equal, AuthScopes.BookingsRead, 234);
            var filterConditionToUseForHash4 = new FilterCondition(AuthzQueryParserService.Operator.GreaterThen, AuthScopes.BookingsManage, "44");

            var filters = new[]
                {
                    filterConditionToUseForHash1,
                    filterConditionToUseForHash3,
                    filterConditionToUseForHash2,
                    filterConditionToUseForHash4
                }.OrderBy(f => f.Operator);

            var expectedHash = MD5Helper.MD5Hash(string.Join("||", filters));

            SetupGetReadValidationFilter(new[]
            {
                new[] { filterConditionToUseForHash1, filterConditionToUseForHash4, filterConditionToUseForHash3 },
                new[] { filterConditionToUseForHash2 }
            });

            var hash = await _service.GetFilterHash();

            Assert.Equal(expectedHash, hash);
        }

        [Fact]
        public async Task SyncBookingPrice_NoPriceChange_BookingPriceNotChanged()
        {
            const string reservationId = "res-id";
            
            var reservation = new Reservation
            {
                Id = reservationId,
                Price = new ReservationPrice
                {
                    Total = 100,
                    TargetMarkup = 10,
                    TargetKickback = 5,
                    Currency = "USD",
                    OriginalCurrency = "EUR",
                    ConversionRates = new Dictionary<string, decimal>
                    {
                        { "EUR-USD", 1.248112M }
                    },
                    ConversionMarginRates = new Dictionary<string, decimal>
                    {
                        { "EUR-USD", 0.005M }
                    },
                },
                Changes = new List<TicketChanges>
                {
                    new()
                    {
                        Price = new ChangePrice
                        {
                            Total = 0,
                            TargetMarkup = 0,
                            TargetKickback = 0,
                            Currency = "USD",
                            OriginalCurrency = "EUR",
                            ConversionRates = new Dictionary<string, decimal>
                            {
                                { "EUR-USD", 1.248112M }
                            },
                            ConversionMarginRates = new Dictionary<string, decimal>
                            {
                                { "EUR-USD", 0.005M }
                            },
                        }
                    }
                },
                BookingId = "booking-id"
            };
            
            reservationsRepositoryMock.Setup(x => x.GetAsync(reservationId)).ReturnsAsync(reservation);

            var booking = new Models.Booking
            {
                Id = reservation.BookingId,
                Price = new BookingPrice
                {
                    Total = 100,
                    Markup = 10,
                    Kickback = 5
                }
            };
            
            bookingsRepositoryMock.Setup(x => x.GetAsync(reservation.BookingId)).ReturnsAsync(booking);

            await _service.SyncBookingPriceAsync(reservationId);
            
            bookingsRepositoryMock.Verify(x=>x.UpdatePriceAsync(booking.Id, It.Is<BookingPrice>(b=>b.Total == reservation.Price.Total)), Times.Once);
            bookingsRepositoryMock.Verify(x=>x.UpdatePriceAsync(booking.Id, It.Is<BookingPrice>(b=>b.Kickback == reservation.Price.TargetKickback)),Times.Once);
            bookingsRepositoryMock.Verify(x=>x.UpdatePriceAsync(booking.Id, It.Is<BookingPrice>(b=>b.Markup == reservation.Price.TargetMarkup)),Times.Once);
        }
        
        [Fact]
        public async Task SyncBookingPrice_NoPriceChange_BookingPriceChanged()
        {
            const string reservationId = "res-id";
            
            var reservation = new Reservation
            {
                Id = reservationId,
                Price = new ReservationPrice
                {
                    Total = 100,
                    TargetMarkup = 10,
                    TargetKickback = 5,
                    Currency = "USD",
                    OriginalCurrency = "EUR",
                    ConversionRates = new Dictionary<string, decimal>
                    {
                        { "EUR-USD", 1.248112M }
                    },
                    ConversionMarginRates = new Dictionary<string, decimal>
                    {
                        { "EUR-USD", 0.005M }
                    },
                },
                Changes = new List<TicketChanges>
                {
                    new()
                    {
                        Price = new ChangePrice
                        {
                            Total = 1,
                            TargetMarkup = 2,
                            TargetKickback = 3,
                            Currency = "USD",
                            OriginalCurrency = "EUR",
                            ConversionRates = new Dictionary<string, decimal>
                            {
                                { "EUR-USD", 1.248112M }
                            },
                            ConversionMarginRates = new Dictionary<string, decimal>
                            {
                                { "EUR-USD", 0.005M }
                            },
                        }
                    },
                    new()
                    {
                        Price = new ChangePrice
                        {
                            Total = 0,
                            TargetMarkup = 0,
                            TargetKickback = 0,
                            Currency = "USD",
                            OriginalCurrency = "EUR",
                            ConversionRates = new Dictionary<string, decimal>
                            {
                                { "EUR-USD", 1.248112M }
                            },
                            ConversionMarginRates = new Dictionary<string, decimal>
                            {
                                { "EUR-USD", 0.005M }
                            },
                        }
                    }
                },
                BookingId = "booking-id"
            };
            
            reservationsRepositoryMock.Setup(x => x.GetAsync(reservationId)).ReturnsAsync(reservation);

            var booking = new Models.Booking
            {
                Id = reservation.BookingId,
                Price = new BookingPrice
                {
                    Total = 100,
                    Markup = 10,
                    Kickback = 5
                }
            };
            
            bookingsRepositoryMock.Setup(x => x.GetAsync(reservation.BookingId)).ReturnsAsync(booking);

            await _service.SyncBookingPriceAsync(reservationId);
            
            bookingsRepositoryMock.Verify(x=>x.UpdatePriceAsync(booking.Id, It.Is<BookingPrice>(b=>b.Total == 101)), Times.Once);
            bookingsRepositoryMock.Verify(x=>x.UpdatePriceAsync(booking.Id, It.Is<BookingPrice>(b=>b.Kickback == 8)),Times.Once);
            bookingsRepositoryMock.Verify(x=>x.UpdatePriceAsync(booking.Id, It.Is<BookingPrice>(b=>b.Markup == 12)),Times.Once);
        }

        [Fact]
        public async Task GetPublicBookingAsync_TrainService_BecameOther()
        {
            bookingsRepositoryMock
                .Setup(b => b.GetAsync(It.IsAny<string>()))
                .ReturnsAsync(_fixture.Create<Models.Booking>());

            var services = _fixture.Build<ExtraServiceModel>()
                .With(s => s.Type, ExtraServiceType.Train)
                .CreateMany()
                .Union(_fixture.Build<ExtraServiceModel>()
                    .With(s => s.Type, ExtraServiceType.ExtraBaggage)
                    .CreateMany());
            
            _extraServiceManagementClient
                .Setup(c => c.GetExtraServicesByBooking(It.IsAny<string>()))
                .ReturnsAsync(services.ToList());
            
            var actual = await _service.GetPublicBookingAsync(_fixture.Create<string>());
            
            actual.ExtraServices
                .Should()
                .Contain(s => s.Type == ExtraServiceType.Other)
                .And
                .NotContain(s => s.Type == ExtraServiceType.Train);
        }
        
        [Fact]
        public async void Check_call_of_UpdateReservationFareAsync_for_SyncFare_Reservation()
        {
            var originalFare = new Shared.Models.Fare
            {
                NonRefAmounts = new NonRefundableAmounts(),
                Cancellations = RefundCondition.PaidRefund
            };

            var reservation = _fixture.Build<Reservation>()
                .With(x => x.Id, Id.New)
                .With(x => x.OriginalFare, originalFare)
                .Create();

            var newFare = _fixture.Build<Shared.Models.Fare>()
                .Create();

            await _service.UpdateReservationFareAsync(reservation, newFare, FareChangeReason.FareUpdate);
            
            reservationsRepositoryMock.Verify(b => b.UpdateFareAsync(
                It.Is<Models.Reservation>(r => r.Fare == newFare
                                               && r.Fare.NonRefAmounts == originalFare.NonRefAmounts
                                               && r.Fare.Cancellations == originalFare.Cancellations
                                               && r.Fare.Changes == originalFare.Changes
                                               && r.OriginalFare == originalFare)), Times.Exactly(1));
        }

        
        [Fact]
        public async void Check_UpdateReservationFareAsync_DoNot_Update_FareType_Reservation()
        {
            var originalFare = new Shared.Models.Fare
            {
                NonRefAmounts = new NonRefundableAmounts(),
                Cancellations = RefundCondition.PaidRefund,
                FareType = FareType.Aircrew
            };

            var reservation = _fixture.Build<Reservation>()
                .With(x => x.Id, Id.New)
                .With(x => x.Fare, originalFare)
                .Create();

            var newFare = _fixture.Build<Shared.Models.Fare>()
                .With(x => x.FareType, FareType.Public)
                .Create();

            await _service.UpdateReservationFareAsync(reservation, newFare, FareChangeReason.FareUpdate);
            
            reservationsRepositoryMock.Verify(b => b.UpdateFareAsync(
                It.Is<Models.Reservation>(r => r.Fare.FareType == originalFare.FareType)), Times.Exactly(1));
        }

        [Fact]
        public async Task UpdateReservationFareAsync_WithValidData_ShouldUpdateFareAndAddFareChange()
        {
            var currentFarePrice = 100;
            var currentFarePriceCurrency = "EUR";
            var reservation = _fixture.Build<Reservation>()
                .With(x => x.Fare, new Shared.Models.Fare
                {
                    Net = currentFarePrice,
                    Currency = currentFarePriceCurrency
                }).Create();

            var newFare = _fixture.Build<Shared.Models.Fare>()
                .With(x => x.Currency, "USD")
                .With(x => x.Net, 50)
                .Create();
            
            var fareChangeReason = FareChangeReason.ClassDrop;
            
            await _service.UpdateReservationFareAsync(reservation, newFare, fareChangeReason);
            
            Assert.NotNull(reservation.OriginalFare);
            
            reservationsRepositoryMock.Verify(repo => repo.UpdateFareAsync(reservation), Times.Once);
            
            _mockFareChangeService.Verify(service => service.AddFareChangeAsync(
                    reservation.Id,
                    It.Is<Shared.Models.Fare>(x=> x.Net == currentFarePrice && x.Currency == currentFarePriceCurrency), 
                    It.Is<Shared.Models.Fare>(x=> x.Net == newFare.Net && x.Currency == newFare.Currency), 
                    fareChangeReason), Times.Once);
        }
        
        [Fact]
        public async Task BuildDraftReservation_ShouldFillFareRulesDict_WhenFaresCacheProvided()
        {
            // Arrange
            var fareRules = new Dictionary<string, List<FareRuleSection>>
            {
                { 
                    "SVO-JFK", 
                    new List<FareRuleSection> 
                    { 
                        new FareRuleSection 
                        { 
                            Category = "16", 
                            Title = "PENALTIES",
                            Text = "PENALTY RULE 1" 
                        } 
                    } 
                },
                { 
                    "JFK-SVO", 
                    new List<FareRuleSection> 
                    { 
                        new FareRuleSection 
                        { 
                            Category = "16", 
                            Title = "PENALTIES",
                            Text = "PENALTY RULE 2" 
                        } 
                    } 
                }
            };
            
            // Create flight segments for the legs
            var legSegments = new List<IList<FlightSegment>>
            {
                new List<FlightSegment>
                {
                    new FlightSegment
                    {
                        Origin = "SVO",
                        Destination = "JFK",
                        Carrier = "SU",
                        FlightNumber = "100",
                        DepartureTimestamp = DateTime.UtcNow.ToUnixTimeSeconds(),
                        DepartureTimestampUtc = DateTime.UtcNow.ToUnixTimeSeconds(),
                        ArrivalTimestamp = DateTime.UtcNow.AddHours(10).ToUnixTimeSeconds(),
                        ArrivalTimestampUtc = DateTime.UtcNow.AddHours(10).ToUnixTimeSeconds(),
                        FareComponent = "SVO-JFK"
                    }
                },
                new List<FlightSegment>
                {
                    new FlightSegment
                    {
                        Origin = "JFK",
                        Destination = "SVO",
                        Carrier = "SU",
                        FlightNumber = "101",
                        DepartureTimestamp = DateTime.UtcNow.AddDays(7).ToUnixTimeSeconds(),
                        DepartureTimestampUtc = DateTime.UtcNow.AddDays(7).ToUnixTimeSeconds(),
                        ArrivalTimestamp = DateTime.UtcNow.AddDays(7).AddHours(10).ToUnixTimeSeconds(),
                        ArrivalTimestampUtc = DateTime.UtcNow.AddDays(7).AddHours(10).ToUnixTimeSeconds(),
                        FareComponent = "JFK-SVO"
                    }
                }
            };
            
            var reservationParams = new CreateReservationParams
            {
                ReservationId = Id.New(),
                BookingId = Id.New(),
                TenantId = Id.New(),
                Passenger = new PassengerDetails
                {
                    FirstName = "John",
                    LastName = "Smith"
                },
                PlatingCarrier = "SU",
                LegSegments = legSegments,
                FaresCache = new FareRulesCacheEntry
                {
                    FareRules = fareRules,
                    CancellationTimeline = new List<ConditionsTimespan>(),
                    ChangeTimeline = new List<ConditionsTimespan>(),
                    PartiallyUsedChangeTimeline = new List<ConditionsTimespan>()
                },
                CreatedBy = new User { Name = "system" }
            };

            // Act
            var draftReservation = await _service.BuildDraftReservation(reservationParams);

            // Assert
            draftReservation.FareRules.Should().BeEquivalentTo(fareRules);
            draftReservation.FareRulesIds.Should().NotBeNull();
            draftReservation.FareRulesIds.Should().ContainKey("SVO-JFK");
            draftReservation.FareRulesIds.Should().ContainKey("JFK-SVO");
            
            // Verify that the FareRulesDict contains the expected section IDs
            var svoJfkSectionId = FareRulesHelper.GetFareRuleIdBySection(fareRules["SVO-JFK"][0]);
            var jfkSvoSectionId = FareRulesHelper.GetFareRuleIdBySection(fareRules["JFK-SVO"][0]);
            
            draftReservation.FareRulesIds["SVO-JFK"].Should().Contain(svoJfkSectionId);
            draftReservation.FareRulesIds["JFK-SVO"].Should().Contain(jfkSvoSectionId);
            
            // Verify that FareRuleCat16Ids is also populated correctly
            draftReservation.FareRuleCat16Ids.Should().NotBeNull();
            draftReservation.FareRuleCat16Ids.Should().NotBeEmpty();
        }
        
        
        private void SetupGetReadValidationFilter(IEnumerable<IEnumerable<FilterCondition>> filters)
        {
            _authServiceMock.Setup(s => s.GetReadValidationFiltersAsync())
                .ReturnsAsync(filters);
        }
    }
}