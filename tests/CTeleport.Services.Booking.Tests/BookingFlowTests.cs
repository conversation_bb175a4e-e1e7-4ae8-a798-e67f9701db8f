using AutoMapper;
using CTeleport.Common.Helpers;
using CTeleport.Common.Authorization.Services;
using CTeleport.Services.Booking.Extensions;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Booking.Shared.Models;
using CTeleport.Services.Booking.Repositories;
using CTeleport.Services.Booking.Services;
using CTeleport.Services.Search.Shared.Models;
using Kickback = CTeleport.Services.TenantShared.Models.Kickback;

using Moq;
using OpenFeature;
using System;
using System.Linq;
using System.Collections.Generic;
using Xunit;
using FluentAssertions;
using CTeleport.Services.Providers.Dto;
using CTeleport.Services.CustomFields.Services.Interfaces;
using CTeleport.Services.FareTerms;
using System.IO;
using System.Reflection;
using CTeleport.Messages.Models;
using CTeleport.Services.Booking.Api;
using CTeleport.Services.Booking.Core.Interfaces;
using CTeleport.Services.FrequentFlyer.Models;
using CTeleport.Services.Booking.Services.Interfaces;
using CTeleport.Services.ExtraServiceManagement.Clients;
using CTeleport.Services.FrequentFlyer.Service;
using CTeleport.Services.Helpers;
using CTeleport.Services.Price.Shared.Models;
using CTeleport.Services.PlacesApiClient;
using IBillingClient = CTeleport.Services.Billing.Clients.IBillingClient;
using CTeleport.Services.Price.Services;
using CTeleport.Services.SearchProxy.Client;
using CTeleport.Services.Settings.Clients;
using FundingSource = CTeleport.Messages.Models.FundingSource;
using IAuthService = CTeleport.Services.Booking.Services.IAuthService;
using Metadata = CTeleport.Services.Booking.Models.Metadata;
using User = CTeleport.Services.Search.Shared.Models.User;

namespace CTeleport.Services.Booking.Tests
{
    public class BookingFlowTests
    {
        static readonly string LV_SOURCE = "1G.8WG0";
        static readonly string providerKey = $"{LV_SOURCE}:**********";

        private readonly IMapper _mapper;
        private readonly BookingService _service;
        private readonly IServiceContext _context;

        static string USER_ID = "USER01";

        static string BOOKING_A_ID = Id.New();
        static string TENANT_A_ID = Id.New();
        static string BOOKING_A_PAX_LASTNAME = "LastnameA";

        static Models.Booking BOOKING_A = new Models.Booking
        {
            Id = BOOKING_A_ID,
            TenantId = TENANT_A_ID,
            Passenger = new PassengerDetails
            {
                LastName = BOOKING_A_PAX_LASTNAME
            }
        };

        static string BOOKING_B_ID = Id.New();
        static string BOOKING_B_TICKET_NUMBER = "1175267490774";
        static string TENANT_B_ID = Id.New();
        static string BOOKING_B_PAX_LASTNAME = "LastnameB";

        static Models.Booking BOOKING_B = new Models.Booking
        {
            Id = BOOKING_B_ID,
            TenantId = TENANT_B_ID,
            Passenger = new PassengerDetails
            {
                LastName = BOOKING_B_PAX_LASTNAME
            }
        };

        static string BOOKING_WITHOUT_LASTNAME_ID = Id.New();
        static Models.Booking BOOKING_WITHOUT_LASTNAME = new Models.Booking
        {
            Id = BOOKING_B_ID,
            TenantId = TENANT_B_ID,
            Passenger = new PassengerDetails
            {
                LastName = null
            }
        };

        static string RESERVATION_A_LOCATOR = "RLA";

        private static Reservation RESERVATION_A = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_A_ID,
            CreatedAt = DateTime.UtcNow,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_A_PAX_LASTNAME
            },
            Locators = new Dictionary<string, string>
            {
                { LocatorNames.PROVIDER, RESERVATION_A_LOCATOR }
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            }
        };

        static Reservation RESERVATION_A_DUPLICATE = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_B_ID,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            Locators = new Dictionary<string, string>
            {
                { LocatorNames.PROVIDER, RESERVATION_A_LOCATOR }
            }
        };

        static Reservation RESERVATION_B = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_B_ID,
            Passenger = new ReservationPassenger
            {
                LastName = BOOKING_B_PAX_LASTNAME
            },
            Tickets = new List<Ticket>
            {
                new Ticket
                {
                    Number = "11111111"
                }
            },
            LegSegments = new[]
            {
                new[]
                {
                    new Segment
                    {
                        CabinClass = "Y"
                    }
                }
            }
        };

        static Reservation RESERVATION_WITHOUT_LASTNAME = new Reservation
        {
            Id = Id.New(),
            BookingId = BOOKING_WITHOUT_LASTNAME_ID,
            Passenger = new ReservationPassenger
            {
                LastName = "LNU"
            },
            CreatedAt = DateTime.UtcNow.AddDays(-1),
            Locators = new Dictionary<string, string>
            {
                { LocatorNames.PROVIDER, RESERVATION_A_LOCATOR }
            }
        };

        private Mock<IBookingsRepository> bookingsRepositoryMock;
        private Mock<IReservationsRepository> reservationsRepositoryMock;
        private Mock<IBillingClient> billingClient;
        private Mock<ITicketUsageService> ticketUsageServiceMock;
        private Mock<ICommonRemarksBuilderService> remarksBuilderServiceMock;
        private Mock<ICustomFieldsDisplayService> customFieldsDisplayService;
        private Mock<IBookingSagaService> _bookingSagaServiceMock;
        private Mock<IFrequentFlyerService> _frequentFlyerService;
        private Mock<IExtraServiceManagementClient> _extraServiceManagementClient;

        public BookingFlowTests()
        {
            bookingsRepositoryMock = new Mock<IBookingsRepository>();
            _bookingSagaServiceMock = new Mock<IBookingSagaService>();

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_A_ID))
                .ReturnsAsync(() => BOOKING_A);

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_B_ID))
                .ReturnsAsync(() => BOOKING_B);

            bookingsRepositoryMock
                .Setup(r => r.GetAsync(BOOKING_WITHOUT_LASTNAME_ID))
                .ReturnsAsync(() => BOOKING_WITHOUT_LASTNAME);

            reservationsRepositoryMock = new Mock<IReservationsRepository>();

            reservationsRepositoryMock
                .Setup(r => r.GetByTicketNumberAsync(BOOKING_B_TICKET_NUMBER))
                .ReturnsAsync(() => RESERVATION_B);

            //var providersServiceMock = new Mock<IAirProvidersService>();
            //providersServiceMock.Setup(x => x.GetSource(It.IsAny<string>())).Returns(new Providers.Dto.AirProvider());

            //var agentMetadataService = new Mock<IAgentMetadataService>();
            var customFieldsService = new Mock<ICustomFieldsService>();
            var fareTermsService = new Mock<IFareTermsClient>();

            billingClient = new Mock<IBillingClient>();

            ticketUsageServiceMock = new Mock<ITicketUsageService>();

            remarksBuilderServiceMock = new Mock<ICommonRemarksBuilderService>();

            customFieldsDisplayService = new Mock<ICustomFieldsDisplayService>();

            var priceServiceMock = new Mock<IPriceService>();
            priceServiceMock.Setup(s => s.CreatePriceConverterAsync(It.IsAny<string>(), It.IsAny<string>(),
                    It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new PriceConverter(0, CurrencyExchange.DefaultMarkupCurrency, new Kickback
                {
                    Currency = "EUR"
                }, 0, 0, new Dictionary<string, decimal>(), new Dictionary<string, decimal>(), "EUR", null, null));

            _mapper = AutoMapperConfig.InitializeMapper();
            _context = ServiceContext.UserContext(TENANT_A_ID, new Common.Authorization.Models.User { Id = USER_ID });

            var loggerMock = new Mock<Serilog.ILogger>(MockBehavior.Loose);
            loggerMock.Setup(l => l.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
                .Returns(loggerMock.Object);

            _frequentFlyerService = new Mock<IFrequentFlyerService>();
            _frequentFlyerService
                .Setup(s => s.GetProgramsAsync(It.IsAny<IEnumerable<string>>()))
                .ReturnsAsync(new List<FrequentFlyerProgram>
                {
                    new FrequentFlyerProgram
                    {
                        Carrier = "AA",
                        Code = "123",
                        Name = "Airbaltic",
                        Rank = 0
                    }
                });

            _extraServiceManagementClient = new Mock<IExtraServiceManagementClient>();
            _service = new BookingService(_context, null,
                bookingsRepositoryMock.Object, reservationsRepositoryMock.Object,
                _mapper, null, new Mock<ISingleNameService>().Object,
                loggerMock.Object, billingClient.Object, new Mock<IAuthService>().Object,
                fareTermsService.Object, ticketUsageServiceMock.Object,
                customFieldsDisplayService.Object,
                _frequentFlyerService.Object,
                _bookingSagaServiceMock.Object,
                _extraServiceManagementClient.Object,
                Mock.Of<IPlacesClient>(),
                Mock.Of<IFareChangeService>(),
                Mock.Of<ISearchClient>(),
                Mock.Of<IProviderTicketStateService>(),
                Mock.Of<ITenantManagementServiceClient>(),
                Mock.Of<DateTimeProvider>(),
                Mock.Of<IFareRulesEnrichmentService>(),
                Mock.Of<IFeatureClient>());
        }

        [Fact]
        public async void SaveCompleatedReservationTest()
        {
            var providerFlightReservation = new ProviderFlightReservation
            {
                Source = LV_SOURCE,
                Locators = new Dictionary<string, string>
                {
                    { "UniversalRecord", "BKNQNR" },
                    { "AirReservation", "S4Y5SL" },
                    { "Provider", "KPCHZK" },

                },
                SupplierLocators = new Dictionary<string, string>
                {
                    { "TK", "TWZ4T2" }
                },
                Passenger = new ReservationPassenger
                {
                    FirstName = "GUY",
                    LastName = "SOME"
                },
                Fare = new CTeleport.Services.Booking.Shared.Models.Fare
                {
                    PlatingCarrier = "TK",

                    LatestTicketingTime = "2018-06-26T23:59:00.000+03:00",
                    GuaranteeDate = null,
                    Taxes = new Dictionary<string, decimal>()
                    {
                        { "AY", 4.83M },
                        { "US", 15.77M },
                        { "XF", 3.88M },
                        { "TR", 5.00M },
                        { "YR", 187.01M }
                    }
                },
                Baggage = new Dictionary<string, Search.Shared.Models.BaggageAllowance>()
                {
                    {
                        "IAH-KHE",
                        new Search.Shared.Models.BaggageAllowance()
                        {
                            Carryon = new Search.Shared.Models.BaggageDetails()
                            {
                                Pcs = 1,
                                Weight = 10
                            },
                            Checked = new Search.Shared.Models.BaggageDetails()
                            {
                                Pcs = 2,
                                Weight = 23
                            }
                        }
                    }
                },
                CanCancel = true,
                Ticketless = false
            };

            providerFlightReservation.Segments = new List<Segment>() {
                new Segment()
                {
                    Origin = "IAH",
                    Destination = "IST",
                    Carrier = "TK",
                    FlightNumber = "34",
                    DepartureDate = "2018-06-26",
                    DepartureTime = "21:05",
                    DepartureTimestamp = **********,
                    DepartureTimestampUtc = **********,
                    ArrivalDate = "2018-06-27",
                    ArrivalTime = "16:55",
                    ArrivalTimestamp = **********,
                    ArrivalTimestampUtc = **********
                },
                new Segment()
                {
                    Origin = "IST",
                    Destination = "KHE",
                    Carrier = "TK",
                    FlightNumber = "471",
                    DepartureDate = "2018-06-28",
                    DepartureTime = "06:45",
                    DepartureTimestamp = 1530168300,
                    DepartureTimestampUtc = 1530179100,
                    ArrivalDate = "2018-06-28",
                    ArrivalTime = "08:15",
                    ArrivalTimestamp = 1530173700,
                    ArrivalTimestampUtc = 1530184500
                }
            };

            var reservation = new Reservation()
            {
                Id = "reservationId",
                Price = new ReservationPrice()
                {
                    OriginalCurrency = "EUR",
                    Currency = "EUR"
                },
                Fare = new Shared.Models.Fare(),
                LegSegments = new List<ICollection<Segment>>
                {
                    new List<Segment>
                    {
                        new Segment
                        {
                            Carrier = "TK",
                            FlightNumber = "34",
                            DepartureDate = "2018-06-26",
                            DepartureTime = "2018-06-26T21:05:00.000-05:00",
                            ArrivalTime = "2018-06-27T16:55:00.000+03:00"
                        },
                        new Segment
                        {
                            Carrier = "TK",
                            FlightNumber = "471",
                            DepartureDate = "2018-06-28",
                            DepartureTime = "2018-06-28T06:45:00.000+03:00",
                            ArrivalTime = "2018-06-28T08:15:00.000+03:00"
                        }
                    }
                }
            };

            foreach (var leg in reservation.LegSegments)
            {
                foreach (var item in leg)
                {
                    item.ArrivalTimestamp = item.ArrivalTime.DateToTimestamp();
                    item.ArrivalTimestampUtc = item.ArrivalTime.DateToUtcTimestamp();

                    item.DepartureTimestamp = item.DepartureTime.DateToTimestamp();
                    item.DepartureTimestampUtc = item.DepartureTime.DateToUtcTimestamp();
                }
            }

            var saga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");

            var rs = await _service.SaveCompletedReservation(reservation, providerFlightReservation, saga);

            Assert.Equal(reservation.Id, rs.Id);
            Assert.Equal(new DateTime(2018, 6, 27, 2, 5, 0, DateTimeKind.Utc), rs.DepartureAt);
            Assert.Single(rs.LegSegments);         // 1 leg
            Assert.Equal(2, rs.LegSegments.First().Count()); // 2 segments

            reservation.BaggageAllowances.Should().ContainKey("IAH-KHE");
        }

        [Fact]
        public async void SaveCompleatedReservationTest_BUG4675()
        {
            ProviderFlightReservation providerFlightReservation = GetMockData<ProviderFlightReservation>("ProviderReservation_BUG4675.json");
            //Reservation reservation = GetMockData<Reservation>("ProviderReservation_BUG46752.json");
            Reservation reservation = new Reservation()
            {
                Fare = new Shared.Models.Fare() { },

                Legs = new List<Leg>()
                {
                    new Leg
                    {
                        Origin = "IST",
                        Destination = "DUR",
                        Departure = **********,
                        DepartureUtc = **********,
                        Arrival = **********,
                        ArrivalUtc = **********,
                        IsDirect = false
                    }
                },
                LegSegments = new List<ICollection<Segment>>
                {
                    new List<Segment>()
                    {
                        new Segment()
                        {
                            Origin = "IST",
                            Destination = "JNB",
                            FareComponent = "IST-DUR",
                            Carrier= "TK",
                            FlightNumber= "42",
                            DepartureDate = "2021-09-18",
                            DepartureTime = "01:45",
                            DepartureTimestamp = **********,
                            DepartureTimestampUtc = **********,
                            ArrivalDate = "2021-09-18",
                            ArrivalTime = "10:20",
                            ArrivalTimestamp = 1631960400,
                            ArrivalTimestampUtc = 1631953200,
                            Connecting = true
                        },
                        new Segment()
                        {
                            Origin = "JNB",
                            Destination = "DUR",
                            FareComponent = "IST-DUR",
                            Carrier= "BA",
                            FlightNumber= "6223",
                            DepartureDate= "2021-09-18",
                            DepartureTime= "16:45",
                            DepartureTimestamp = 1631983500,
                            DepartureTimestampUtc = **********,
                            ArrivalDate = "2021-09-18",
                            ArrivalTime = "17:50",
                            ArrivalTimestamp = **********,
                            ArrivalTimestampUtc= **********
                        }
                    }
                }
            };

            var saga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");

            var rs = await _service.SaveCompletedReservation(reservation, providerFlightReservation, saga);

            Assert.NotNull(rs);
        }

        private static T GetMockData<T>(string file)
        {
            var executable = new Uri(typeof(BookingFlowTests).GetTypeInfo().Assembly.CodeBase).LocalPath;

            return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(
                File.ReadAllText(Path.GetFullPath(Path.Combine(Path.GetDirectoryName(executable), $"../../../Mocks/{file}"))));
        }

        [Fact]
        public async void BaggageOverrideCheckTest()
        {
            var providerFlightReservation = new ProviderFlightReservation
            {
                Source = LV_SOURCE,
                Locators = new Dictionary<string, string>
                {
                    { "UniversalRecord", "BKNQNR" },
                    { "AirReservation", "S4Y5SL" },
                    { "Provider", "KPCHZK" },

                },
                SupplierLocators = new Dictionary<string, string>
                {
                    { "TK", "TWZ4T2" }
                },
                Passenger = new ReservationPassenger
                {
                    FirstName = "GUY",
                    LastName = "SOME"
                },
                Fare = new CTeleport.Services.Booking.Shared.Models.Fare
                {
                    PlatingCarrier = "TK",

                    LatestTicketingTime = "2018-06-26T23:59:00.000+03:00",
                    GuaranteeDate = null,
                    Taxes = new Dictionary<string, decimal>()
                    {
                        { "AY", 4.83M },
                        { "US", 15.77M },
                        { "XF", 3.88M },
                        { "TR", 5.00M },
                        { "YR", 187.01M }
                    }
                },
                Baggage = null,
                CanCancel = true,
                Ticketless = false
            };

            providerFlightReservation.Segments = new List<Segment>() {
                new Segment()
                {
                    Origin = "IAH",
                    Destination = "IST",
                    Carrier = "TK",
                    FlightNumber = "34",
                    DepartureDate = "2018-06-26",
                    DepartureTime = "21:05",
                    DepartureTimestamp = **********,
                    DepartureTimestampUtc = **********,
                    ArrivalDate = "2018-06-27",
                    ArrivalTime = "16:55",
                    ArrivalTimestamp = **********,
                    ArrivalTimestampUtc = **********
                },
                new Segment()
                {
                    Origin = "IST",
                    Destination = "KHE",
                    Carrier = "TK",
                    FlightNumber = "471",
                    DepartureDate = "2018-06-28",
                    DepartureTime = "06:45",
                    DepartureTimestamp = 1530168300,
                    DepartureTimestampUtc = 1530179100,
                    ArrivalDate = "2018-06-28",
                    ArrivalTime = "08:15",
                    ArrivalTimestamp = 1530173700,
                    ArrivalTimestampUtc = 1530184500
                }
            };

            var reservation = new Reservation()
            {
                Id = "reservationId",
                Price = new ReservationPrice()
                {
                    OriginalCurrency = "EUR",
                    Currency = "EUR"
                },
                BaggageAllowances = new Dictionary<string, BaggageAllowance>()
                {
                    {"IAH-IST", new BaggageAllowance()}
                },
                Fare = new Shared.Models.Fare(),
                LegSegments = new List<ICollection<Segment>>
                {
                    new List<Segment>
                    {
                        new Segment
                        {
                            Carrier = "TK",
                            FlightNumber = "34",
                            DepartureDate = "2018-06-26",
                            DepartureTime = "2018-06-26T21:05:00.000-05:00",
                            ArrivalTime = "2018-06-27T16:55:00.000+03:00"
                        },
                        new Segment
                        {
                            Carrier = "TK",
                            FlightNumber = "471",
                            DepartureDate = "2018-06-28",
                            DepartureTime = "2018-06-28T06:45:00.000+03:00",
                            ArrivalTime = "2018-06-28T08:15:00.000+03:00"
                        }
                    }
                }
            };

            foreach (var leg in reservation.LegSegments)
            {
                foreach (var item in leg)
                {
                    item.ArrivalTimestamp = item.ArrivalTime.DateToTimestamp();
                    item.ArrivalTimestampUtc = item.ArrivalTime.DateToUtcTimestamp();

                    item.DepartureTimestamp = item.DepartureTime.DateToTimestamp();
                    item.DepartureTimestampUtc = item.DepartureTime.DateToUtcTimestamp();
                }
            }

            var saga = new Domain.Aggregates.BookingAggregate.Booking("123", "tenant");

            var rs = await _service.SaveCompletedReservation(reservation, providerFlightReservation, saga);

            Assert.Equal(reservation.Id, rs.Id);
            reservation.BaggageAllowances.Should().ContainKey("IAH-IST");
        }

        [Fact]
        public async void CreateReservationCommandTest()
        {
            var reservationParams = new CreateReservationParams
            {
                TargetSource = new AirProvider() { },
                SearchJobId = "SearchJobId",
                ProviderKey = providerKey,
                ReservationId = Id.New(),
                RequestId = Id.New(),
                BookingId = Id.New(),
                TenantId = "TenantId",
                Price = new CreateReservationPrice
                {
                    Currency = "EUR",
                    OriginalCurrency = "EUR",
                    Markup = 1,
                    MarkupCurrency = "EUR",
                    QuotedPrice = 1
                },
                Passenger = new PassengerDetails()
                {
                    FirstName = "Jonh",
                    LastName = "Smith",
                    Email = "test@test",
                    Phone = "12345678",
                },
                Metadata = new Metadata()
                {
                    VesselName = "Vessel Name"
                },
                CorporateCodes = new Dictionary<string, string>(),
                PlatingCarrier = "SU",
                ExemptLiTax = false,
                CreatedBy = new User() { Name = "system" },
                FundingSource = new FundingSource()
                {
                    Id = "BSL.NL",
                    Type = "BSP"
                }
            };

            reservationParams.LegSegments = new List<IList<FlightSegment>>()
            {
                new List<FlightSegment>()
                {
                    new FlightSegment()
                    {
                        Origin = "IAH",
                        Destination = "IST",
                        Carrier = "TK",
                        FlightNumber = "34",
                        DepartureDate = "2018-06-26",
                        DepartureTime = "2018-06-26T21:05:00.000-05:00",
                        ArrivalTime = "2018-06-27T16:55:00.000+03:00"
                    },
                    new FlightSegment()
                    {
                        Origin = "IST",
                        Destination = "KHE",
                        Carrier = "TK",
                        FlightNumber = "471",
                        DepartureDate = "2018-06-28",
                        DepartureTime = "2018-06-28T06:45:00.000+03:00",
                        ArrivalTime = "2018-06-28T08:15:00.000+03:00"
                    }
                }
            };

            foreach (var leg in reservationParams.LegSegments)
            {
                foreach (var item in leg)
                {
                    item.ArrivalTimestamp = item.ArrivalTime.DateToTimestamp();
                    item.ArrivalTimestampUtc = item.ArrivalTime.DateToUtcTimestamp();

                    item.DepartureTimestamp = item.DepartureTime.DateToTimestamp();
                    item.DepartureTimestampUtc = item.DepartureTime.DateToUtcTimestamp();
                }
            }

            remarksBuilderServiceMock.Setup(s => s.BuildRemarks(new CustomFields.Dto.RemarksBuilderRequest()
            {
                LegSegments = reservationParams.LegSegments,
                PlatingCarrier = reservationParams.PlatingCarrier,
                AdvancedMetadata = new Dictionary<string, Dictionary<string, string>>(),
                CorporateCodes = reservationParams.CorporateCodes,
                Vessel = new CustomFields.Models.VesselInfo()
                {
                    Name = "Vessel Name"
                },
                ReservationContext = new CustomFields.Models.ReservationContext()
                {
                    TargetSource = new AirProvider()
                    {

                    },
                    Tenant = new Settings.Contracts.TenantManagementService.Tenant()
                    {

                    }
                }
            }))
                .Returns(new List<Messages.Commands.Models.RemarkMetadata>());

            var rs = await _service.CreateReservationCommand(reservationParams);

            Assert.NotNull(rs);
        }


    }
}
