using CTeleport.Common.Extensions;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.Helpers;
using FluentAssertions;
using Xunit;

namespace CTeleport.Services.Booking.Tests
{
    public class StringExtensionsTests
    {
        [Fact]
        public void Should_return_null_if_source_is_not_set()
        {
            var reservation = new Reservation();
            reservation.Source.GetPcc().Should().BeNull();
        }

        [Fact]
        public void Should_return_null_if_source_is_empty()
        {
            var reservation = new Reservation{ Source = string.Empty};
            reservation.Source.GetPcc().Should().BeNull();
        }

        [Fact]
        public void Should_return_null_if_source_has_no_pcc()
        {
            var reservation = new Reservation { Source = CTeleport.Services.Helpers.Constants.Providers.GALILEO };
            reservation.Source.GetPcc().Should().BeNull();
        }

        [Fact]
        public void Should_return_pcc_of_galileo_source()
        {
            var reservation = new Reservation { Source = $"{CTeleport.Services.Helpers.Constants.Providers.GALILEO}.{PCCs.CTeleportLV}" };
            reservation.Source.GetPcc().Should().Be(PCCs.CTeleportLV);
        }
    }
}
