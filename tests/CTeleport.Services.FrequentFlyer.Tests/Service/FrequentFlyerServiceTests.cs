using CTeleport.Services.AirlineSettingsClient.Core.Interfaces;
using CTeleport.Services.AirlineSettingsClient.Core.Models;
using CTeleport.Services.Booking.Tests;
using CTeleport.Services.FrequentFlyer.Models;
using CTeleport.Services.FrequentFlyer.Service;
using FluentAssertions;
using Moq;
using Serilog;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace CTeleport.Services.FrequentFlyer.Tests.Service
{
    public class FrequentFlyerServiceTests
    {
        public class FrequentFlyerServiceTestData : TheoryData
        {
            private class Case
            {
                public string Name { get; set; }
                public string RequestJsonPath { get; set; }
                public string LoyaltyProgramsJsonPath { get; set; }
                public FrequentFlyerInfo ExpectedResult { get; set; }
            }

            private List<Case> Cases = new List<Case>
            {
                new Case
                {
                    Name = "Split, One leg",
                    RequestJsonPath = "FrequentFlyerRequest/split_one_leg_request.json",
                    LoyaltyProgramsJsonPath = "loyalty_programs.json",
                    ExpectedResult = new FrequentFlyerInfo
                    {
                        Components = new List<FrequentFlyerComponent>
                        {
                            new FrequentFlyerComponent
                            {
                                Key = "ewrgdn",
                                Name = "From EWR to GDN",
                                SupportedFrequentFlyerPrograms = new List<FrequentFlyerProgram>
                                {
                                    new FrequentFlyerProgram
                                    {
                                        Carrier = "LH",
                                        Code = "LH",
                                        Name = "Lufthansa. The Miles and More",
                                        Rank = 10
                                    }
                                }
                            }
                        }
                    }
                },
                new Case
                {
                    Name = "Return flight",
                    RequestJsonPath = "FrequentFlyerRequest/return_flight_request.json",
                    LoyaltyProgramsJsonPath = "loyalty_programs.json",
                    ExpectedResult = new FrequentFlyerInfo
                    {
                        Components = new List<FrequentFlyerComponent>
                        {
                            new FrequentFlyerComponent
                            {
                                Key = "amssvo",
                                Name = "From AMS to SVO and back",
                                SupportedFrequentFlyerPrograms = new List<FrequentFlyerProgram>
                                {
                                    new FrequentFlyerProgram
                                    {
                                        Carrier = "DT",
                                        Code = "DT1",
                                        Name = " Delta Air Lines. SkyMiles",
                                        Rank = 9
                                    },
                                    new FrequentFlyerProgram
                                    {
                                        Carrier = "KL",
                                        Code = "KL",
                                        Name = "KLM. Flying blue",
                                        Rank = 10
                                    },
                                    new FrequentFlyerProgram
                                    {
                                        Carrier = "SU",
                                        Code = "SU",
                                        Name = "Aeroflot. Aeroflot bonus",
                                        Rank = 9
                                    },
                                    new FrequentFlyerProgram
                                    {
                                        Carrier = "AF",
                                        Code = "AF",
                                        Name = "AirFrance. Flying blue",
                                        Rank = 9
                                    }
                                }
                            }
                        }
                    }
                }
            };

            public FrequentFlyerServiceTestData()
            {
                Cases.ForEach(AddCase);
            }

            private void AddCase(Case @case)
            {
                var request = TestHelper.GetMockData<FrequentFlyerRequest>(@case.RequestJsonPath);
                var loyaltyPrograms = TestHelper.GetMockData<List<LoyaltyProgram>>(@case.LoyaltyProgramsJsonPath);
                AddRow(@case.Name, request, loyaltyPrograms, @case.ExpectedResult);
            }
        }

        public class FrequentFlyerServiceForReservationTestData : TheoryData
        {
            private class Case
            {
                public string Name { get; set; }
                public string RequestJsonPath { get; set; }
                public string LoyaltyProgramsJsonPath { get; set; }
                public FrequentFlyerInfoForReservation ExpectedResult { get; set; }
            }

            private List<Case> Cases = new List<Case>
            {
                new Case
                {
                    Name = "Split, One leg",
                    RequestJsonPath = "FrequentFlyerForReservationRequest/split_one_leg_request.json",
                    LoyaltyProgramsJsonPath = "loyalty_programs.json",
                    ExpectedResult = new FrequentFlyerInfoForReservation
                    {
                        AllowCorporateCodes = false,
                        FrequentFlyerNumbers = new List<FrequentFlyerNumber>
                        {
                            new FrequentFlyerNumber
                            {
                                Carrier = "LH",
                                Code = "LH",
                                Number = "123456"
                            }
                        }
                    }
                },
                new Case
                {
                    Name = "Return flight",
                    RequestJsonPath = "FrequentFlyerForReservationRequest/return_flight_request.json",
                    LoyaltyProgramsJsonPath = "loyalty_programs.json",
                    ExpectedResult = new FrequentFlyerInfoForReservation
                    {
                        AllowCorporateCodes = false,
                        FrequentFlyerNumbers = new List<FrequentFlyerNumber>
                        {
                            new FrequentFlyerNumber
                            {
                                Carrier = "SU",
                                Code = "SU",
                                Number = "123456"
                            }
                        }
                    }
                },
                new Case
                {
                    Name = "Return flight, allow corporate codes",
                    RequestJsonPath = "FrequentFlyerForReservationRequest/return_flight_allow_corp_codes_request.json",
                    LoyaltyProgramsJsonPath = "loyalty_programs.json",
                    ExpectedResult = new FrequentFlyerInfoForReservation
                    {
                        AllowCorporateCodes = true,
                        FrequentFlyerNumbers = new List<FrequentFlyerNumber>
                        {
                            new FrequentFlyerNumber
                            {
                                Carrier = "KL",
                                Code = "KL",
                                Number = "654321"
                            }
                        }
                    }
                }
            };

            public FrequentFlyerServiceForReservationTestData()
            {
                Cases.ForEach(AddCase);
            }

            private void AddCase(Case @case)
            {
                var request = TestHelper.GetMockData<FrequentFlyerForReservationRequest>(@case.RequestJsonPath);
                var loyaltyPrograms = TestHelper.GetMockData<List<LoyaltyProgram>>(@case.LoyaltyProgramsJsonPath);
                AddRow(@case.Name, request, loyaltyPrograms, @case.ExpectedResult);
            }
        }

        private readonly Mock<IAirlineSettingsClient> _airlineSettingsClientMock;
        private readonly FrequentFlyerService _service;

        public FrequentFlyerServiceTests()
        {
            var logger = new LoggerConfiguration().MinimumLevel.Debug().CreateLogger();
            _airlineSettingsClientMock = new Mock<IAirlineSettingsClient>();
            _service = new FrequentFlyerService(_airlineSettingsClientMock.Object, logger);
        }

        [Theory]
        [ClassData(typeof(FrequentFlyerServiceForReservationTestData))]
        public async Task GetForReservationAsyncTest(
            string @case,
            FrequentFlyerForReservationRequest request,
            List<LoyaltyProgram> loyaltyPrograms,
            FrequentFlyerInfoForReservation expected)
        {
            _airlineSettingsClientMock.Setup(x => x.GetLoyaltyProgramsAsync())
                .ReturnsAsync(loyaltyPrograms);

            var actual = await _service.GetForReservationAsync(request);

            actual.Should().BeEquivalentTo(expected);
        }
    }
}