using CTeleport.Messages.Commands.Models;
using CTeleport.Services.CustomFields.Configuration;
using CTeleport.Services.CustomFields.Dto;
using CTeleport.Services.CustomFields.Models;
using CTeleport.Services.CustomFields.Services;
using CTeleport.Services.Providers.Dto;
using CTeleport.Services.Search.Shared.Models;
using FluentAssertions;
using Moq;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using AutoFixture;
using CTeleport.Services.Settings.Contracts.TenantManagementService;
using Serilog.Core;
using Xunit;

namespace CTeleport.Services.CustomFields.Tests.Services;

public class CommonRemarksBuilderServiceTests
{
    private readonly Fixture _fixture;

    public CommonRemarksBuilderServiceTests()
    {
        _fixture = new Fixture();
    }
        
    [Fact]
    public void BuildRemarks_ExpectEmptyRemark_WhenNotAdded()
    {
        // Arrange
        var remarksOptions = new RemarksOptions
        {
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            EnergySSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>(0),
            Aircrew = new AircrewRemarks
            {
                OSI = _fixture.Build<AircrewOSI>().With(o => o.Default, string.Empty)
                    .Without(o => o.Carriers)
                    .Create()
            }
        };
        
        var target = new CommonRemarksBuilderService(remarksOptions, Logger.None);

        var remarksBuilderRequest = new RemarksBuilderRequest
        {
            ReservationContext = _fixture.Create<ReservationContext>(),
            AdvancedMetadata = new Dictionary<string, Dictionary<string, string>>(0),
            CorporateCodes = new Dictionary<string, string>(0),
            AircrewFareBooking = true, 
            LegSegments = new List<IList<FlightSegment>> { _fixture.CreateMany<FlightSegment>().ToList() }
        };
        
        // Act
        var actual = target.BuildRemarks(remarksBuilderRequest);

        // Assert
        actual.Should().BeEmpty();
    }
    
    [Fact]
    public void BuildRemarks_ExpectAircrewFareCarrierOSIIsNull_WhenContainsOSIWithDefaultValue()
    {
        // Arrange
        var defaultOSI = _fixture.Create<string>();
        var remarksOptions = new RemarksOptions
        {
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            EnergySSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>(0),
            Aircrew = new AircrewRemarks
            {
                OSI = _fixture.Build<AircrewOSI>().With(o => o.Default, defaultOSI)
                    .Without(o => o.Carriers)
                    .Create()
            }
        };
        
        var target = new CommonRemarksBuilderService(remarksOptions, Logger.None);

        var remarksBuilderRequest = new RemarksBuilderRequest
        {
            ReservationContext = new ReservationContext { TargetSource = new AirProvider(), Tenant = new Tenant { Id = "cTeleport" }},
            AdvancedMetadata = new Dictionary<string, Dictionary<string, string>>(0),
            CorporateCodes = new Dictionary<string, string>(0),
            AircrewFareBooking = true, 
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "SS" } } }
        };
        
        // Act
        var actual = target.BuildRemarks(remarksBuilderRequest);

        // Assert
        actual.Should().ContainSingle(c => c.Carrier == "SS" && c.Type == RemarkType.OSI && c.FreeText == defaultOSI);
    }
    
    [Fact]
    public void BuildRemarks_ExpectAircrewFareDefaultFreetext_WhenContainsOSIWithDefaultValue()
    {
        // Arrange
        var defaultOSI = _fixture.Create<string>();
            
        var remarksOptions = new RemarksOptions
        {
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            EnergySSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>(0),
            Aircrew = new AircrewRemarks
            {
                OSI = _fixture.Build<AircrewOSI>().With(o => o.Default, defaultOSI).Create()
            }
        };
            
        var target = new CommonRemarksBuilderService(remarksOptions, Logger.None);

        var remarksBuilderRequest = new RemarksBuilderRequest
        {
            ReservationContext = new ReservationContext { TargetSource = new AirProvider() , Tenant = new Tenant { Id = "cTeleport" }},
            AdvancedMetadata = new Dictionary<string, Dictionary<string, string>>(0),
            CorporateCodes = new Dictionary<string, string>(0),
            AircrewFareBooking = true, 
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "XX" } } }
        };
        
        // Act
        var actual = target.BuildRemarks(remarksBuilderRequest);

        // Assert
        actual.Should().ContainSingle(c => c.Carrier == "XX" && c.Type == RemarkType.OSI && c.FreeText == defaultOSI);
    }
        
    [Fact]
    public void BuildRemarks_ExpectAircrewFare_WhenContainsCarrierOSI()
    {
        // Arrange
        var remarksOptions = new RemarksOptions
        {
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            EnergySSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>(0),
            Aircrew = new AircrewRemarks
            {
                OSI = new AircrewOSI
                {
                    Default = _fixture.Create<string>(),
                    Carriers =  new Dictionary<string, string> { { "XX", "TEXT" } }
                }
            }
        };
        
        var target = new CommonRemarksBuilderService(remarksOptions, Logger.None);

        var remarksBuilderRequest = new RemarksBuilderRequest
        {
            ReservationContext = _fixture.Create<ReservationContext>(),
            AdvancedMetadata = new Dictionary<string, Dictionary<string, string>>(0),
            CorporateCodes = new Dictionary<string, string>(0),
            AircrewFareBooking = true, 
            LegSegments = new List<IList<FlightSegment>>
            {
                _fixture.Build<FlightSegment>().With(s => s.Carrier, "XX")
                    .CreateMany(4).ToList()
            }
        };
        
        // Act
        var actual = target.BuildRemarks(remarksBuilderRequest);

        // Assert
        actual.Should().ContainSingle(c => c.Carrier == "XX" && c.Type == RemarkType.OSI && c.FreeText == "TEXT");
    }
        
    [Fact]
    public void BuildRemarks_ExpectOptionsPerProviderNotSpecifiedWithinCarrier_WhenBuildDefaultSeamenRemarks()
    {
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string>
            {
                { "CKIN", "SEMN {Vessel.Name}/{Vessel.Flag}" },
                { "SEMN", "ON DUTY" }
            },
            OSI = new List<string>
            {
                "SEMN {Vessel.Name}/{Vessel.Flag}/TA {Agency.IATA} {Agency.Name} {Agency.Phone}"
            },
            Dynamic = new List<DynamicRemark>
            {
                new()
                {
                    Type = CustomFieldType.General,
                    Carrier = "LH",
                    TypeInGds = "Vendor",
                    CategoryInGds = "Vendor",
                    Template = "MARINE FARE {Pcc}"
                }
            },
            Type = "Default"
        };
        
        var options = new RemarksOptions
        {
            SeamenSSRs = new Dictionary<string, IList<SSROptions>> { { "PS", new List<SSROptions> { ssrOptions } } },
            EnergySSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(options, new Mock<ILogger>().Object);

        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "PS" } } },
            PlatingCarrier = "PS",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" }
        };

        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "SEMN THE NETHERLANDS/NL",
                Carrier = "PS",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "CKIN" } }
            },
            new RemarkMetadata
            {
                FreeText = "ON DUTY",
                Carrier = "PS",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
            },
            new RemarkMetadata
            {
                FreeText = "SEMN THE NETHERLANDS/NL/TA KBP KYIV +*********",
                Carrier = "PS",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>()
            },
            new RemarkMetadata
            {
                FreeText = "MARINE FARE 8WG0",
                Carrier = "LH",
                Type = RemarkType.General,
                ExtraData = new Dictionary<string, string> { { "typeInGds", "Vendor" }, { "categoryInGds", "Vendor" } }
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);
        
        // Arrange
        remarks.Should().BeEquivalentTo(expectedResult);
    }

    [Fact]
    public void BuildRemarks_ExpectReturnDefaultEnergyRemarks_WhenSSRNotFoundForSpecifiedCarrier()
    {
        // Arrange
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var defaultSsrOptions = new SSROptions
        {
            OSI = new List<string> { "MUST CARRY OFFSHORE TRAVEL DOC" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>>
            {
                { "AC", new List<SSROptions> { ssrOptions } },
                { "Default", new List<SSROptions> { defaultSsrOptions } }
            },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "OZ" } } },
            PlatingCarrier = "OZ",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = true
        };

        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "MUST CARRY OFFSHORE TRAVEL DOC",
                Carrier = "OZ",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNullOrEmpty();
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    [Fact]
    public void BuildRemarks_ExpectReturnEmptyRemarksList_WhenEnergyFareBookingIsNotEnabled()
    {
        // Arrange
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var defaultSsrOptions = new SSROptions
        {
            OSI = new List<string> { "MUST CARRY OFFSHORE TRAVEL DOC" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>>
            {
                { "AC", new List<SSROptions> { ssrOptions } },
                { "Default", new List<SSROptions> { defaultSsrOptions } }
            },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "OZ" } } },
            PlatingCarrier = "OZ",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = false
        };
        
        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNull();
        remarks.Should().BeEmpty();
    }
    
    [Fact]
    public void BuildRemarks_ExpectReturnEnergyRemarksByProvider_WhenEnergySSRExistForSpecifiedCarrier()
    {
        // Arrange
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>> { { "AC", new List<SSROptions> { ssrOptions } } },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "AC" } } },
            PlatingCarrier = "AC",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = true
        };

        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "SITE_NAME",
                Carrier = "AC",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
            },
            new RemarkMetadata
            {
                FreeText = "OFFSHORE/CTELEPORT/SITE_NAME/SITE_LOCATION",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            },
            new RemarkMetadata
            {
                FreeText = "DEAL CIC 50/ITOFFSH",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNullOrEmpty();
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    [Theory]
    [MemberData(nameof(AcCarrierTestingData))]
    public void BuildRemarks_ExpectReturnEnergyRemarksByProvider_WhenPlattingCarrierIsAC(
        string marketingCarrier, string operatingCarrier, RemarkMetadata[] expectedResult)
    {
        // Arrange
        const string acCarrier = "AC";
        const string ozCarrier = "OZ";
        var acSsrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var ozSsrOptions = new SSROptions
        {
            OSI = new List<string> { "OFFSHORE/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>>
            {
                { acCarrier, new List<SSROptions> { acSsrOptions } },
                { ozCarrier, new List<SSROptions> { ozSsrOptions } }
            },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = marketingCarrier, Operator = operatingCarrier } } },
            PlatingCarrier = acCarrier,
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = true
        };

        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNullOrEmpty();
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    [Fact]
    public void BuildRemarks_ExpectReturnEnergyRemarksByProviderWithoutDuplicates_WhenWithTwoSegmentsACCarrierIsAlsoOperatingAndOtherAC()
    {
        // Arrange
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>> { { "AC", new List<SSROptions> { ssrOptions } } },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { 
                new List<FlightSegment>
                {
                    new() { Carrier = "AC", Operator = "AC" },
                    new() { Carrier = "AC", Operator = "KL" }
                }
            },
            PlatingCarrier = "AC",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = true
        };

        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "SITE_NAME",
                Carrier = "AC",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
            },
            new RemarkMetadata
            {
                FreeText = "OFFSHORE/CTELEPORT/SITE_NAME/SITE_LOCATION",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            },
            new RemarkMetadata
            {
                FreeText = "DEAL CIC 50/ITOFFSH",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNullOrEmpty();
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    [Fact]
    public void BuildRemarks_ExpectReturnEnergyRemarksByProviderWithoutDuplicates_WhenACCarrierIsAlsoOperatingDuplicate()
    {
        // Arrange
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>> { { "AC", new List<SSROptions> { ssrOptions } } },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>>
            {
                new List<FlightSegment> { new() { Carrier = "AC" } },
                new List<FlightSegment> { new() { Carrier = "AC" } },
                new List<FlightSegment> { new() { Carrier = "KL", Operator = "AC" } }
            },
            PlatingCarrier = "AC",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = true
        };

        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "SITE_NAME",
                Carrier = "AC",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
            },
            new RemarkMetadata
            {
                FreeText = "OFFSHORE/CTELEPORT/SITE_NAME/SITE_LOCATION",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            },
            new RemarkMetadata
            {
                FreeText = "DEAL CIC 50/ITOFFSH",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNullOrEmpty();
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    [Fact]
    public void BuildRemarks_ExpectReturnEnergyRemarksByProvider_WhenUACarrierIsOperating()
    {
        // Arrange
        var ssrOptions = new SSROptions
        {
            SSR = new Dictionary<string, string> { { "SEMN", "{Site.Name}" } },
            OSI = new List<string> { "OFFSHORE/{Tenant.Name}/{Site.Name}/{Site.Location}" },
            Type = "1G"
        };
        
        var remarksOptions = new RemarksOptions
        {
            EnergySSRs = new Dictionary<string, IList<SSROptions>> { { "AC", new List<SSROptions> { ssrOptions } } },
            SeamenSSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        };
        
        var service = new CommonRemarksBuilderService(remarksOptions, new Mock<ILogger>().Object);
        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "AC", Operator = "UA" } } },
            PlatingCarrier = "AC",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" },
            Site = new SiteInfo { Location = "Site_Location", Name = "Site_Name" },
            EnergyFareBooking = true
        };

        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "SITE_NAME",
                Carrier = "AC",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
            },
            new RemarkMetadata
            {
                FreeText = "OFFSHORE/CTELEPORT/SITE_NAME/SITE_LOCATION",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            },
            new RemarkMetadata
            {
                FreeText = "DEAL CIC 50/ITOFFSH",
                Carrier = "AC",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            },
            new RemarkMetadata
            {
                FreeText = "SEE GG INT REG FOR CHANGES",
                Carrier = "UA",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            },
            new RemarkMetadata
            {
                FreeText = "UA MARINE NETT TKT",
                Carrier = "UA",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>(0)
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);

        // Assert
        remarks.Should().NotBeNullOrEmpty();
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    [Fact]
    public void BuildRemarks_ExpectOptionsPerProviderSpecifiedWithinCarrier_WhenBuildSeamenRemarksPerProvider()
    {
        // Arrange
        var options = new SSROptions
        {
            SSR = new Dictionary<string, string>
            {
                { "CKIN", "SEMN {Vessel.Name}/{Vessel.Flag}" },
                { "OTHS", "MARINE FARE {Pcc}" },
                { "SEMN", "ON DUTY" }
            },
            OSI = new List<string>
            {
                "SEMN {Vessel.Name}/{Vessel.Flag}/TA {Agency.IATA} {Agency.Name} {Agency.Phone}"
            },
            Dynamic = new List<DynamicRemark>
            {
                new()
                {
                    Type = CustomFieldType.General,
                    Carrier = "LH",
                    TypeInGds = "Vendor",
                    CategoryInGds = "Vendor"
                }
            },
            Type = "1G"
        };
        
        var service = new CommonRemarksBuilderService(new RemarksOptions
        {
            SeamenSSRs = new Dictionary<string, IList<SSROptions>> { { "PS", new List<SSROptions> { options } } },
            EnergySSRs = new Dictionary<string, IList<SSROptions>>(0),
            CorporateCodeOSITemplates = new Dictionary<string, CorporateCodeOptions>()
        }, new Mock<ILogger>().Object);

        var request = new RemarksBuilderRequest
        {
            LegSegments = new List<IList<FlightSegment>> { new List<FlightSegment> { new() { Carrier = "PS" } } },
            PlatingCarrier = "PS",
            ReservationContext = new ReservationContext
            {
                Tenant = new Tenant { Id = "cTeleport" },
                TargetSource = new AirProvider
                {
                    AgencyIata = "KBP",
                    AgencyName = "Kyiv",
                    AgencyPhone = "+*********",
                    Id = "8WG0",
                    Source = "1G"
                }
            },
            Vessel = new VesselInfo { Flag = "NL", Name = "The Netherlands" }
        };
        
        var expectedResult = new[]
        {
            new RemarkMetadata
            {
                FreeText = "SEMN THE NETHERLANDS/NL",
                Carrier = "PS",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "CKIN" } }
            },
            new RemarkMetadata
            {
                FreeText = "MARINE FARE 8WG0",
                Carrier = "PS",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "OTHS" } }
            },
            new RemarkMetadata
            {
                FreeText = "ON DUTY",
                Carrier = "PS",
                Type = RemarkType.SSR,
                ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
            },
            new RemarkMetadata
            {
                FreeText = "SEMN THE NETHERLANDS/NL/TA KBP KYIV +*********",
                Carrier = "PS",
                Type = RemarkType.OSI,
                ExtraData = new Dictionary<string, string>()
            },
            new RemarkMetadata
            {
                Carrier = "LH",
                Type = RemarkType.General,
                ExtraData = new Dictionary<string, string> { { "typeInGds", "Vendor" }, { "categoryInGds", "Vendor" } }
            }
        };
        
        // Act
        var remarks = service.BuildRemarks(request);
        
        // Assert
        remarks.Should().BeEquivalentTo(expectedResult);
    }
    
    private static RemarkMetadata _ssrAcRemark = new()
    {
        FreeText = "SITE_NAME",
        Carrier = "AC",
        Type = RemarkType.SSR,
        ExtraData = new Dictionary<string, string> { { "ssrType", "SEMN" } }
    };

    private static RemarkMetadata _basicAcOsiRemark = new()
    {
        FreeText = "OFFSHORE/CTELEPORT/SITE_NAME/SITE_LOCATION",
        Carrier = "AC",
        Type = RemarkType.OSI,
        ExtraData = new Dictionary<string, string>(0)
    };
    
    private static RemarkMetadata _basicOzOsiRemark = new()
    {
        FreeText = "OFFSHORE/SITE_NAME/SITE_LOCATION",
        Carrier = "OZ",
        Type = RemarkType.OSI,
        ExtraData = new Dictionary<string, string>(0)
    };
    
    private static RemarkMetadata _extraAcOsiRemark = new()
    {
        FreeText = "DEAL CIC 50/ITOFFSH",
        Carrier = "AC",
        Type = RemarkType.OSI,
        ExtraData = new Dictionary<string, string>(0)
    };
    
    public static TheoryData<string, string, RemarkMetadata[]> AcCarrierTestingData
    {
        get
        {
            // MarketingCarrier, OperatingCarrier, ExpectedRemarks
            var data = new TheoryData<string, string, RemarkMetadata[]>
            {
                { "AC", "AC", [
                    _ssrAcRemark,
                    _basicAcOsiRemark,
                    _extraAcOsiRemark
                ] },
                { "OZ", "AC", [
                    _basicOzOsiRemark,
                    _extraAcOsiRemark
                ] },
                { "AC", "OZ", [
                    _ssrAcRemark,
                    _basicAcOsiRemark,
                    _extraAcOsiRemark
                ] },
                { "OZ", "TP", [
                    _basicOzOsiRemark
                ] }
            };
            return data;
        }
    }
}
