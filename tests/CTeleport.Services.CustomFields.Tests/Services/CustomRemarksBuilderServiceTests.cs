using System.Collections.Generic;
using CTeleport.Services.CustomFields.Models;
using CTeleport.Services.CustomFields.Repositories.Interfaces;
using CTeleport.Services.CustomFields.Services;
using FluentAssertions;
using Moq;
using Serilog;
using Xunit;

namespace CTeleport.Services.CustomFields.Tests.Services;

public class CustomRemarksBuilderServiceTests
{
    private readonly CustomRemarksBuilderService _service = new(Mock.Of<ICustomFieldTemplateRepository>(), Mock.Of<ILogger>());
    
    private const string FallbackTemplate = "CO-{Custom:fallback:cost_centre|Vessel.CostCentre}";
    
    [Theory]
    [InlineData("ACCOUNTING", "vessel", "CO-ACCOUNTING")]  // Primary value wins
    [InlineData("", "vessel", "CO-VESSEL")]                // Empty primary → fallback
    [InlineData(null, "vessel", "CO-VESSEL")]              // Null primary → fallback
    [InlineData(null, null, "CO-")]                        // Both missing → empty fallback
    [InlineData("ACCOUNTING", null, "CO-ACCOUNTING")]      // Fallback missing, primary works
    public void FormatRemarks_FallbackFormatter_WorksCorrectly(string customValue, string vesselValue, string expected)
    {
        // Arrange
        var template = new CustomFieldTemplate
        {
            Template = FallbackTemplate,
            TemplateType = CustomFieldTemplateType.Template
        };

        var values = new Dictionary<string, Dictionary<string, string>>
        {
            { "Custom", new Dictionary<string, string> { { "cost_centre", customValue } } },
            { "Vessel", new Dictionary<string, string> { { "CostCentre", vesselValue }, } },
        };

        // Act
        var result = _service.FormatRemark(template, values);

        // Assert
        result.FreeText.Should().Be(expected);
    }

    [Fact]
    public void FormatRemarks_ShouldUseFallback_WhenPrimaryIsMissing()
    {
        // Arrange
        var template = new CustomFieldTemplate
        {
            Template = FallbackTemplate,
            TemplateType = CustomFieldTemplateType.Template
        };

        var values = new Dictionary<string, Dictionary<string, string>>
        {
            { "Custom", new Dictionary<string, string>() },
            { "Vessel", new Dictionary<string, string> { { "CostCentre", "vessel" } } },
        };

        // Act
        var result = _service.FormatRemark(template, values);

        // Assert
        result.FreeText.Should().Be("CO-VESSEL");
    }
    
    [Fact]
    public void FormatRemarks_ShouldReturnEmpty_WhenBothValuesMissing()
    {
        // Arrange
        var template = new CustomFieldTemplate
        {
            Template = FallbackTemplate,
            TemplateType = CustomFieldTemplateType.Template
        };

        var values = new Dictionary<string, Dictionary<string, string>>
        {
            { "Custom", new Dictionary<string, string>() },
            { "Vessel", new Dictionary<string, string>() },
        };

        // Act
        var result = _service.FormatRemark(template, values);

        // Assert
        result.FreeText.Should().Be("CO-");
    }
    
    [Theory]
    [InlineData("ACCOUNTING", "CO-ACCOUNTING")]  // Primary value wins
    [InlineData(null, "CO-")]              // Null primary → empty
    public void FormatRemarks_ShouldReturnFirstEmpty_WhenNoSecondDictionary(string customValue, string expected)
    {
        // Arrange
        var template = new CustomFieldTemplate
        {
            Template = FallbackTemplate,
            TemplateType = CustomFieldTemplateType.Template
        };

        var values = new Dictionary<string, Dictionary<string, string>>
        {
            { "Custom", new Dictionary<string, string> { { "cost_centre", customValue } } },
        };

        // Act
        var result = _service.FormatRemark(template, values);

        // Assert
        result.FreeText.Should().Be(expected);
    }
    
    [Fact]
    public void FormatRemarks_ShouldChooseCorrectOption_ByCommonMarkup()
    {
        // Arrange
        var template = new CustomFieldTemplate
        {
            Template = "{Common.Markup:choose(15.00):{Gmt.HomeCompanyCode}-NG-/SD-15|{Gmt.HomeCompanyCode}-NG-/SD-40}",
            TemplateType = CustomFieldTemplateType.Template
        };

        var values = new Dictionary<string, Dictionary<string, string>>
        {
            { "Common", new Dictionary<string, string> { { "Markup", "15.00" } } },
        };

        // Act
        var result = _service.FormatRemark(template, values);

        // Assert
        result.FreeText.Should().Be("-NG-/SD-15");
    }
}