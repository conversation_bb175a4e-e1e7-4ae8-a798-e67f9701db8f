using CTeleport.Services.Booking.Services;
using CTeleport.Services.ClassDrop.Communication.Contracts.Messages;
using CTeleport.Services.ClassDrop.Operations.RequestHandlers;
using FluentAssertions;
using Moq;
using Serilog;
using Xunit;

namespace CTeleport.Services.ClassDrop.Tests.Operations.RequestHandlers;

public class DisableClassDropRequestHandlerTests
{
    private readonly Mock<IBookingService> _bookingServiceMock;
    private readonly Mock<ILogger> _loggerMock;
    private readonly DisableClassDropRequestHandler _handler;

    public DisableClassDropRequestHandlerTests()
    {
        _bookingServiceMock = new Mock<IBookingService>();
        _loggerMock = new Mock<ILogger>();
        _loggerMock.Setup(x => x.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(_loggerMock.Object);

        _handler = new DisableClassDropRequestHandler(
            _bookingServiceMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task HandleAsync_WhenSuccessful_ReturnsSuccessResult()
    {
        // Arrange
        var request = new DisableClassDrop.Request("test-reservation-id");
        _bookingServiceMock.Setup(x => x.DisableClassDropCheck(request.ReservationId))
            .Returns(Task.CompletedTask);

        // Act
        var response = await _handler.HandleAsync(request);

        // Assert
        response.IsSuccess.Should().BeTrue();
        _bookingServiceMock.Verify(x => x.DisableClassDropCheck(request.ReservationId), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_WhenExceptionOccurs_ReturnsFailResult()
    {
        // Arrange
        var request = new DisableClassDrop.Request("test-reservation-id");
        var expectedException = new Exception("Test exception");

        _bookingServiceMock.Setup(x => x.DisableClassDropCheck(request.ReservationId))
            .ThrowsAsync(expectedException);

        // Act
        var response = await _handler.HandleAsync(request);

        // Assert
        response.IsSuccess.Should().BeFalse();
        response.Errors.Should().Contain(expectedException.Message);
        _loggerMock.Verify(
            x => x.Error(
                It.Is<Exception>(e => e == expectedException),
                It.Is<string>(s => s.Contains(nameof(DisableClassDrop)))),
            Times.Once);
    }
}