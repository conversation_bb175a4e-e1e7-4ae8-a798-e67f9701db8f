<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="CTeleport.Services.ClassDrop.Communication.Http" Version="2025.5.1.60" />
      <PackageReference Include="FluentAssertions" Version="6.12.0" />
      <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.11.0" />
      <PackageReference Include="Moq" Version="4.7.145" />
      <PackageReference Include="xunit" Version="2.3.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\CTeleport.Services.ClassDrop\CTeleport.Services.ClassDrop.csproj" />
    </ItemGroup>

</Project>
